import 'dart:async';

var _kConsoleMode = false;
String? _workingDirectory;

bool get kConsoleMode => _kConsoleMode;

String? get kWorkingDirectory => _workingDirectory;

const kDataRepositoryDirectory = '../bitacora_flutter_data';

void initEnvironment(List<String> args) {
  _kConsoleMode = {'console', '-c'}.contains(args.first);
}

FutureOr<T> wrapWorkingDirectory<T>(
  String workingDirectory,
  FutureOr<T> Function() work,
) async {
  _workingDirectory = workingDirectory;
  final result = await work();
  _workingDirectory = null;
  return result;
}
