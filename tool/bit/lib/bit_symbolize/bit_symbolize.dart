import 'dart:async';
import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/environment.dart';
import 'package:bit/file_utils.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';
import 'package:path/path.dart' as path;

const _kFlagHelp = '-h';

const _help = '''
A tool for symbolizing bug reports.

Takes a bug report zip file, extracts it and symbolizes the logs based on
matched symbols in the data repository.

Usage: 

  bit symbolize [path/to/bug/report.zip]
  
''';

Future<int> bitSymbolize([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  if (args.length != 1) {
    writeln(_help, TextStyle.help);
    return 1;
  }

  return _symbolize(args.first);
}

Future<int> _symbolize(String bugReportZip) async {
  writeln('Symbolizing bug report...', TextStyle.highlighted);

  final outputPath = path.join(
    path.dirname(bugReportZip),
    '${path.basenameWithoutExtension(bugReportZip)}_symbolized',
  );
  final extract = await _extract(bugReportZip, outputPath);
  if (extract != 0) {
    return extract;
  }

  final symbolsPath = await _determineSymbolsPath(outputPath);
  if (symbolsPath == null) {
    return 1;
  }

  final symbolized = await _symbolizeLogs(outputPath, symbolsPath);
  if (symbolized != 0) {
    return symbolized;
  }

  writeln('\nOpening bug report...');
  await runProcess('open', [outputPath]);
  return 0;
}

Future<int> _extract(String bugReportZip, String outputPath) async {
  final inputExists = await File(bugReportZip).exists();
  if (!inputExists) {
    writeln('Input file not found.', TextStyle.error);
    writeln('\n  $bugReportZip\n');
    return 1;
  }

  final outputPathExists = await Directory(outputPath).exists();
  if (outputPathExists) {
    writeln('Output directory already exists.', TextStyle.error);
    writeln('\n  $outputPath\n');
    return 1;
  }

  writeln('Output path:');
  writeln('\n  $outputPath\n');

  await extractFileToDisk(bugReportZip, outputPath);
  return 0;
}

Future<String?> _determineSymbolsPath(String outputPath) async {
  final dataRepositoryUpdated = await _updateDataRepository();
  if (dataRepositoryUpdated != 0) {
    writeln(
      'Data repository failed to update, symbols might be missing.',
      TextStyle.error,
    );
  }

  final symbolsTarget = await _determineSymbolsTarget(outputPath);
  writeln('\nLooking for [$symbolsTarget] symbols...');

  final symbolsRepoPath = path.join(kDataRepositoryDirectory, 'symbols');
  final symbolsVersionPaths = await findEntitiesInDirectory(
    symbolsRepoPath,
    RegExp(symbolsTarget.version),
  );
  if (symbolsVersionPaths.isEmpty) {
    writeln('No symbols found in $symbolsRepoPath', TextStyle.error);
    return null;
  }

  final symbolsAliasPath =
      path.join(symbolsVersionPaths.first, symbolsTarget.alias);
  final symbolsAliasDirectoryExists =
      await Directory(symbolsAliasPath).exists();
  if (!symbolsAliasDirectoryExists) {
    writeln('No symbols found in $symbolsVersionPaths', TextStyle.error);
    return null;
  }

  final symbolsFilePaths = await findEntitiesInDirectory(
    symbolsAliasPath,
    RegExp('(.*)${symbolsTarget.arch}.symbols'),
  );
  if (symbolsFilePaths.isEmpty) {
    writeln('No symbols found in $symbolsAliasPath', TextStyle.error);
    return null;
  }

  writeln('Found symbols ${symbolsFilePaths.first}');
  return symbolsFilePaths.first;
}

Future<int> _symbolizeLogs(String outputPath, String symbolsPath) async {
  writeln('\nSymbolizing logs...');

  final logsDir = Directory(path.join(outputPath, 'logs'));
  final inputLogsDir = await logsDir.rename(path.join(
    outputPath,
    'logs_obfuscated',
  ));

  final logsPaths = await findEntitiesInDirectory(
    inputLogsDir.path,
    RegExp(r'bit-log_(.*).txt'),
  );
  final sortedLogsPaths = logsPaths.toList(growable: false)
    ..sort((a, b) => b.compareTo(a));

  await runProcess('ulimit', ['-S', '-n', '2048']);

  await logsDir.create();
  final futures = <Future>[];
  for (final logPath in sortedLogsPaths) {
    final future = runProcess('flutter', [
      'symbolize',
      '-i',
      logPath,
      '-o',
      path.join(logsDir.path, path.basename(logPath)),
      '-d',
      symbolsPath,
    ]);
    futures.add(future);
    future.then((_) => futures.remove(future));
    if (futures.length >= 10) {
      await Future.any(futures);
    }
  }

  await Future.wait(futures);

  await inputLogsDir.delete(recursive: true);

  return 0;
}

Future<int> _updateDataRepository() async {
  writeln('Updating data repository...');
  return wrapWorkingDirectory(
    kDataRepositoryDirectory,
    gitAssertBranchCleanAndPull,
  );
}

Future<_SymbolsTarget> _determineSymbolsTarget(String outputPath) async {
  final headerFilePath =
      await findEntitiesInDirectory(outputPath, RegExp(r'bug_report(.*).txt'));

  final headerFile = await File(headerFilePath.first).readAsString();
  final headerFileSplit = headerFile.split('\n');
  final targetLine = headerFileSplit[2];
  final archLine = headerFileSplit[5];

  final buildTargetVersionRegexp =
      RegExp(r'(.*) (.*) com.bitacora.locust (.*)');
  final buildTargetVersionMatch =
      buildTargetVersionRegexp.allMatches(targetLine).first;
  final alias = buildTargetVersionMatch.group(2);
  final version = buildTargetVersionMatch.group(3);

  final archArm64Regexp = RegExp(r'\[(.*)arm64(.*)]');
  final archArmRegexp = RegExp(r'\[(.*)arm(.*)]');
  final arch = archArm64Regexp.hasMatch(archLine)
      ? 'arm64'
      : (archArmRegexp.hasMatch(archLine) ? 'arm' : 'x64');

  return _SymbolsTarget(alias!, version!, arch);
}

class _SymbolsTarget {
  final String alias;
  final String version;
  final String arch;

  _SymbolsTarget(this.alias, this.version, this.arch);

  @override
  String toString() {
    return '$alias $version $arch';
  }
}
