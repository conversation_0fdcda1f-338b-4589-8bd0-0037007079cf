import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for debugging bitacora_flutter.

Runs `flutter run` with correct flags for debugging.

Available modes:

 debug      Debug normally (hot reload available).
 profile    Profile performance.

Usage:
 
  bit debug
  bit profile

''';

const _debug = 'debug';
const _profile = 'profile';

Future<int> bitDebug([
  String mode = _debug,
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  writeln(
    '${mode == _debug ? 'Debugging' : 'Profiling'} Larvacora...',
    TextStyle.highlighted,
  );
  final run = await startProcess(
    'flutter',
    ['run', ..._getRunFlags(mode, args)],
    ProcessStartMode.inheritStdio,
  );
  return run.exitCode;
}

Future<void> bitProfile([List<String> args = const <String>[]]) =>
    bitDebug(_profile, args);

List<String> _getRunFlags(String mode, List<String> inputArgs) {
  return [
    '--dart-define=APP_NAME=Larvacora.io',
    '--dart-define=APPLICATION_ID=com.bitacora.locust',
    '--dart-define=DEV=true',
    if (mode == 'profile') '--profile',
    ...inputArgs
  ];
}
