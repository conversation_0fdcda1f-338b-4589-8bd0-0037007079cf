import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/environment.dart';
import 'package:bit/stdin.dart';

List<Process> _processStack = <Process>[];

Future<Process> startProcess(
  String executable,
  List<String> arguments, [
  ProcessStartMode mode = ProcessStartMode.normal,
  bool printHeaderFooter = true,
]) async {
  if (printHeaderFooter) {
    writeln(
      'Running `$executable'
      '${arguments.isEmpty ? '' : ' ${arguments.join(' ')}'}`',
      TextStyle.process,
    );
  }

  Stdin().pause();
  final process = await Process.start(
    executable,
    arguments,
    mode: mode,
    workingDirectory: kWorkingDirectory,
  );
  _processStack.add(process);

  if (mode != ProcessStartMode.detachedWithStdio) {
    process.exitCode.then((value) {
      if (printHeaderFooter) {
        writeln('Process ended $executable $value $arguments $mode');
      }
      _processStack.remove(process);
      if (_processStack.isEmpty) {
        Stdin().resume();
      }
    });
  }
  return process;
}

Future<int> runProcess(
  String executable,
  List<String> arguments, [
  ProcessStartMode mode = ProcessStartMode.normal,
  bool printHeaderFooter = true,
]) async {
  final process =
      await startProcess(executable, arguments, mode, printHeaderFooter);
  if (mode != ProcessStartMode.detachedWithStdio) {
    return process.exitCode;
  }
  return 1;
}

Future<String> getProcessOutput(Process process) async {
  final output = await process.stdout.transform(utf8.decoder).toList();
  await process.exitCode;
  return output.join();
}

Future<int> assertProcessOutputsLine(
  Process process,
  RegExp pattern, [
  bool printOutput = false,
]) async {
  var hasMatch = false;
  for (final stream in [process.stdout, process.stderr]) {
    stream.listen((event) {
      if (printOutput) {
        (stream == process.stdout ? stdout : stderr).add(event);
      }

      if (hasMatch) {
        return;
      }
      for (final line in utf8.decoder.convert(event).split('\n')) {
        if (pattern.hasMatch(line)) {
          hasMatch = true;
          return;
        }
      }
    });
  }

  await process.exitCode;
  return hasMatch ? 0 : 1;
}
