import 'dart:async';
import 'dart:io';

import 'package:console/console.dart';

typedef OnData = void Function(List<int> event);

class Stdin {
  static final Stdin _instance = Stdin._();

  StreamSubscription<List<int>>? _stdinListen;
  Stream<List<int>>? _stdinStream;
  final List<OnData> _listeners = [];
  bool? _initialEchoMode;
  bool? _initialLineMode;
  bool _isOpen = false;
  bool _isListening = false;

  factory Stdin() => _instance;

  Stdin._();

  void _open() {
    if (_isOpen) {
      return;
    }
    _isOpen = true;

    _initialEchoMode = stdin.echoMode;
    _initialLineMode = stdin.lineMode;
    stdin.echoMode = false;
    stdin.lineMode = false;
    Console.hideCursor();

    _stdinStream = stdin.asBroadcastStream(
      onCancel: (controller) => controller.pause(),
      onListen: (controller) => controller.resume(),
    );
  }

  void close() {
    if (!_isOpen) {
      return;
    }

    stdin.echoMode = _initialEchoMode!;
    stdin.lineMode = _initialLineMode!;
    _stdinListen?.cancel();
    Console.showCursor();
  }

  void resume() {
    if (_isListening || _listeners.isEmpty) {
      return;
    }
    Console.hideCursor();
    _isListening = true;

    _stdinListen = _stdinStream!.listen((data) {
      _listeners.last(data);
    });
  }

  void pause() {
    if (!_isListening) {
      return;
    }
    _isListening = false;

    _stdinListen!.cancel();
    _stdinListen = null;
  }

  Future<T> listenAndWait<T>(OnData listener, Future<T> future) async {
    _subscribeListener(listener);
    await future;
    _unsubscribeListener(listener);
    return future;
  }

  void _subscribeListener(OnData listener) {
    _listeners.add(listener);

    _open();
    resume();
  }

  void _unsubscribeListener(OnData listener) {
    _listeners.remove(listener);
    if (_listeners.isEmpty) {
      pause();
    }
  }
}
