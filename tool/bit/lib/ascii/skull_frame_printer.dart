import 'dart:math';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:console/console.dart';

const _kHorizontalEdge = '─━╾╼';
const _kVerticalEdge = '│┃╽╿';
const _kCornerTopLeft = '┌┍┎┏';
const _kCornerTopRight = '┐┑┒┓';
const _kCornerBottomLeft = '└┕┖┗';
const _kCornerBottomRight = '┘┙┚┛';

Random _random = Random();
List<_FrameLine>? _frameLines;

void printSkullFrame(int frame, int offset) {
  if (frame <= 0) {
    _frameLines = _buildFrameLines();
  }

  for (var line in _frameLines!) {
    line.draw(frame, offset);
  }
}

List<_FrameLine> _buildFrameLines() {
  final list = <_FrameLine>[];
  for (var i = 0; i < 10; i++) {
    list.add(_FrameLine(-10 * i, 10, 1, 1));
    list.add(_FrameLine(0, 10 * i, -1, 1));
    list.add(_FrameLine(10, 10 * i, -1, -1));
    list.add(_FrameLine(20 * i, 10 * i, 1, -1));
  }
  return list;
}

class _FrameLine {
  final int x0;
  final int y0;
  final int hDir;
  final int vDir;

  int _x = 0;
  int _y = 0;
  bool _isFalling = false;

  _FrameLine(this.x0, this.y0, this.hDir, this.vDir);

  void draw(int frame, int offset) {
    if (frame == -1) {
      for (var sFrame = 0; sFrame < 60; sFrame++) {
        draw(sFrame, offset);
      }
      return;
    }

    if (frame == 0) {
      _x = x0;
      _y = y0;
    }

    Console.moveCursor(
      row: _y % Console.rows,
      column: (_x + offset) % Console.columns,
    );
    Console.write(_random.nextBool() ? kAnsiColorYellowPlus : kAnsiColorYellow);
    if (_isFalling) {
      if (_random.nextBool()) {
        _isFalling = false;
        Console.write(_pick(vDir > 0
            ? hDir < 0
                ? _kCornerBottomRight
                : _kCornerBottomLeft
            : hDir < 0
                ? _kCornerTopRight
                : _kCornerTopLeft));
        _x += hDir;
      } else {
        _y += vDir;
        Console.write(_pick(_kVerticalEdge));
      }
    } else {
      if (_random.nextInt(5) == 0) {
        _isFalling = true;
        _y += vDir;
        Console.write(_pick(vDir > 0
            ? hDir < 0
                ? _kCornerTopLeft
                : _kCornerTopRight
            : hDir < 0
                ? _kCornerBottomLeft
                : _kCornerBottomRight));
      } else {
        _x += vDir;
        Console.write(_pick(_kHorizontalEdge));
      }
    }
    Console.write(kAnsiColorReset);
  }

  String _pick(String s) {
    int x = _random.nextInt(s.length);
    return s.substring(x, x + 1);
  }
}
