import 'dart:io';
import 'dart:math';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/ascii/skull_frame_printer.dart';
import 'package:console/console.dart';

const kEyes = ['⁛', '\$', '☪︎'];

int _lastRow = -1;
int _lastColumn = -1;
String _lastColor = '';
List<List<String>>? _skullLayers;
Random _random = Random();

void printSkull([int frame = -1]) {
  final offset = _getOffset();

  printSkullFrame(frame, offset + 15);
  _printSkull(frame);

  Console.moveCursor(row: 10, column: 15 + offset);
  write(kEyes[Random().nextInt(kEyes.length)], TextStyle.focus);

  Console.write(kAnsiColorReset);
  if (frame == -1) {
    Console.moveCursor(row: Console.rows);
  }
}

void _printSkull(int frame) {
  _skullLayers ??= [1, 2, 3, 0]
      .map((i) =>
          File('./tool/bit/ascii-art/skull/skull-$i.txt').readAsLinesSync())
      .toList();
  for (int row = 0; row < 80; row++) {
    for (int column = 0; column < 120; column++) {
      if (frame == -1 || _random.nextInt(10) == 0) {
        _writePixel(_skullLayers!, row, column);
      }
    }
  }
}

void _writePixel(List<List<String>> greySkulls, int row, int column) {
  var layer = 0;
  for (var skull in greySkulls) {
    if (_maybePrintSkullLayer(skull, row, column, layer++)) {
      _lastRow = row;
      _lastColumn = column;
      break;
    }
  }
}

bool _maybePrintSkullLayer(List<String> lines, int row, int column, int layer) {
  if (row >= lines.length || column >= lines[row].length) {
    return false;
  }

  if (lines[row].codeUnits[column] == ' '.codeUnits[0]) {
    return false;
  }

  final color = _getSkullLayerColor(layer);
  if (_lastColor != color) {
    _lastColor = color;
    Console.write(kAnsiColorReset);
    Console.write(color);
  }

  if (_lastRow == row && _lastColumn == column - 1) {
    write(String.fromCharCode(lines[row].codeUnits[column]));
    return true;
  }
  int offsetColumn = column + _getOffset();
  if (offsetColumn >= 0 || offsetColumn < Console.columns) {
    Console.moveCursor(row: row + 1, column: offsetColumn);
    write(String.fromCharCode(lines[row].codeUnits[column]));
  }

  return true;
}

int _getOffset() {
  return 2 * Console.columns ~/ 3;
}

String _getSkullLayerColor(int layer) {
  switch (layer) {
    case 0:
      return kAnsiColorWhitePlus;
    case 1:
      return kAnsiColorWhite;
    case 2:
      return kAnsiColorGrey;
    case 3:
      return kAnsiColorYellow;
  }
  throw 'oops';
}
