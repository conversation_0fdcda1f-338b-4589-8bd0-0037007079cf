import 'dart:convert';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/bit_test/test_watcher.dart';
import 'package:bit/command_line.dart';
import 'package:bit/process.dart';
import 'package:glob/glob.dart';
import 'package:glob/list_local_fs.dart';
import 'package:path/path.dart' as path;

const _kRootPrefix = './';
const _kPackageName = 'bitacora';
const _kDefaultMaxDepth = 3;

const _kFlagHelp = '-h';
const _kFlagFull = '--full';
const _kFlagAmend = '--amend';
const _kFlagMaxDepth = '--max-depth';
const _kFlagSurface = '--surface';
const _kFlagShallow = '--shallow';
const _kFlagDeep = '--deep';

const _help = '''
A tool for testing staged changes (or last commit).

Runs `flutter test` on test files associated with currently touched files.
If no changes are staged, considers files touched in the last commit instead.

Available arguments:

 -h               Prints help.
 
 --full           Runs `flutter test` without arguments.
                  All tests should run.

 --amend          Combines current changes with changes from last commit.
                  The result is the same as amending your commit first, and
                  then running `bit test`.

 --max-depth 3    Set the max-depth for dependency analysis.
                  
                  Test files can depend on source files through long dependency 
                  chains. i.e.

                  A.dart <- B.dart <- C.dart <- D.dart <- D_test.dart

                  Should a change in A.dart trigger testing D_test.dart?
                  Maybe if it's not too far away?
                  
                  Default is 3.
                  
 --surface        Sets the max-depth to 0.
                  *Only* touched test files will run.

 --shallow        Sets the max-depth to 1.
                  Tests must depend directly on touched sources to run.

 --deep           Sets the max-depth to 1000.
                  All tests associated with touched files should run.

Usage:
 
  bit test
  bit test [--max-depth 5 | --surface | --shallow | --deep | --full]
  bit test --amend [--max-depth 5 | --surface | --shallow | --deep | --full]
''';

CommandLine? commandLine;
bool _hasFinishedSuccessfully = false;

Future<int> bitTest([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  writeln('Testing...', TextStyle.highlighted);
  if (args.contains(_kFlagFull)) {
    return _test();
  }

  final depth = _getDepth(args);
  final amend = args.contains(_kFlagAmend);
  await _findChangesAndTest(depth, amend);

  await CommandLine(
    {'test': () => _findChangesAndTest(depth, amend)},
    isSticky: true,
  ).present();
  return 0;
}

int _getDepth(List<String> args) {
  if (args.contains(_kFlagSurface)) {
    return 0;
  }

  if (args.contains(_kFlagShallow)) {
    return 1;
  }

  if (args.contains(_kFlagDeep)) {
    return 1000;
  }

  final flagIndex = args.indexOf(_kFlagMaxDepth);
  if (flagIndex >= 0) {
    return int.parse(args[flagIndex + 1]);
  }

  return _kDefaultMaxDepth;
}

Future<int> _findChangesAndTest(int maxDepth, bool amend) async {
  writeln(
    'Finding changes (maxDepth=$maxDepth${amend ? ' | amend' : ''})...',
  );

  await TestWatcher().onTestDidStart();

  final touchedSourceFiles = await _getTouchedSourceFiles(amend);

  if (touchedSourceFiles.isEmpty) {
    writeln('No changes found. Aborting.', TextStyle.error);
    return _finishWithSuccess();
  }

  final sourceFiles = _getAllSourceFiles();
  if (sourceFiles.isEmpty) {
    writeln('No sources found (?). Aborting.', TextStyle.error);
    return _finishWithSuccess();
  }

  final filesToTest = _getFilesToTest(
    maxDepth,
    touchedSourceFiles,
    sourceFiles,
  );
  if (filesToTest.isEmpty) {
    writeln('No relevant test files found. Aborting.', TextStyle.error);
    return _finishWithSuccess();
  }

  final result = await _test(filesToTest);
  if (result == 0) {
    return _finishWithSuccess();
  }
  return result;
}

int _finishWithSuccess() {
  _hasFinishedSuccessfully = true;
  TestWatcher().onTestDidSucceed();
  return 0;
}

Future<int> _test([Set<String> filesToTest = const <String>{}]) {
  return runProcess(
    'flutter',
    ['test', ...filesToTest],
    ProcessStartMode.inheritStdio,
  );
}

Future<Iterable<String>> _getTouchedSourceFiles(bool amend) async {
  final fromWatch = await _getTouchedSourceFilesFromWatch();
  if (fromWatch != null) {
    return fromWatch;
  }

  if (amend) {
    return _getTouchedSourceFilesFromCommit('HEAD^');
  }

  final fromHead = await _getTouchedSourceFilesFromCommit('HEAD');
  if (fromHead.isNotEmpty) {
    return fromHead;
  }
  writeln('Testing last commit instead...');
  return _getTouchedSourceFilesFromCommit('HEAD^');
}

Future<Iterable<String>?> _getTouchedSourceFilesFromWatch() async {
  if (!_hasFinishedSuccessfully) {
    return null;
  }

  writeln('Analyzing changes from watcher...');

  final touchedFiles = TestWatcher().touchedFiles;
  if (touchedFiles.isNotEmpty) {
    writeln();
    for (var file in touchedFiles) {
      writeln(file, TextStyle.highlighted);
    }
    writeln();
  }
  return touchedFiles;
}

Future<Iterable<String>> _getTouchedSourceFilesFromCommit(String commit) async {
  writeln(
    'Finding touched dart sources (lib|test) between current and $commit ... ',
  );

  final gitDiff = await startProcess(
    'git',
    ['diff', '--name-only', commit],
  );
  final touchedFiles = <String>[];
  gitDiff.stdout.listen((bytes) {
    touchedFiles.addAll(utf8.decoder.convert(bytes).split('\n').where(
        (e) => e.startsWith(RegExp(r'(lib/|test/)')) && e.endsWith('.dart')));
  });
  await gitDiff.exitCode;
  writeln(
    'Found ${touchedFiles.length} files${touchedFiles.isEmpty ? '.' : ':\n'}',
  );
  for (var file in touchedFiles) {
    writeln(file, TextStyle.highlighted);
  }
  if (touchedFiles.isNotEmpty) {
    writeln();
  }
  return touchedFiles;
}

Set<String> _getAllSourceFiles() {
  write('Getting all dart source files... ');
  final files = Glob('**.dart')
      .listSync(root: _kRootPrefix, followLinks: false)
      .map((e) => e.path.substring(_kRootPrefix.length))
      .whereType<String>()
      .where((file) => file.startsWith(RegExp(r'(lib/|test/)')))
      .toSet();
  writeln('[${files.length}]');
  return files;
}

Set<String> _getFilesToTest(
  int maxDepth,
  Iterable<String> touchedSourceFiles,
  Set<String> sourceFiles,
) {
  final visited = <String>{};
  final filesToTest = <String>{};
  final deps = _getDependencies(sourceFiles);

  writeln('Visiting dependencies to find relevant test files...\n');
  for (var file in touchedSourceFiles) {
    _visit(visited, filesToTest, deps, file, 'touched', 0, maxDepth);
  }
  writeln();
  writeln(
    'Found [${filesToTest.length}] files to test from '
    '[${visited.length}] visited nodes.',
    TextStyle.highlighted,
  );
  writeln();
  return filesToTest;
}

Map<String, Set<String>> _getDependencies(Set<String> files) {
  write('Building dependencies map from source files... ');
  final deps = <String, Set<String>>{};
  int found = 0;
  for (var file in files) {
    found += _addDependenciesFrom(deps, file);
  }
  writeln('[$found]');
  return deps;
}

int _addDependenciesFrom(Map<String, Set<String>> deps, String from) {
  int found = 0;
  final lines = File('$_kRootPrefix$from').readAsLinesSync();
  for (var line in lines) {
    line = line.trim();
    if (line.startsWith(RegExp(r'(import|export)'))) {
      found += _maybeAddDependencyFromImportLine(deps, from, line);
    }
  }
  return found;
}

int _maybeAddDependencyFromImportLine(
    Map<String, Set<String>> deps, String source, String importLine) {
  const importPackagePrefix = 'import \'package:$_kPackageName/';
  const importRelativePrefix = 'import \'';

  if (importLine.startsWith(importPackagePrefix)) {
    final importedFile = importLine.substring(
      importPackagePrefix.length,
      importLine.length - 2,
    );
    _addDependency(deps, 'lib/$importedFile', source);
    return 1;
  }

  if (importLine.startsWith(importRelativePrefix) &&
      !importLine.contains(':')) {
    final relativePath = importLine.substring(
      importRelativePrefix.length,
      importLine.length - 2,
    );
    final importedFile = path.normalize(
        path.join(Directory(path.dirname(source)).path, relativePath));
    _addDependency(deps, importedFile, source);
    return 1;
  }

  return 0;
}

void _addDependency(Map<String, Set<String>> deps, String from, String to) {
  if (deps[from] == null) {
    deps[from] = <String>{};
  }
  deps[from]!.add(to);
}

void _visit(
  Set<String> visited,
  Set<String> filesToTest,
  Map<String, Set<String>> deps,
  String file,
  String from,
  int depth,
  int maxDepth,
) {
  if (visited.contains(file) || file.isEmpty || depth > maxDepth) {
    return;
  }

  if (file.endsWith('mocks.dart') && depth > 0) {
    return;
  }

  visited.add(file);

  bool isTestFile = file.startsWith('test/') && file.endsWith('_test.dart');
  if (isTestFile) {
    filesToTest.add(file);
  }

  writeln(
    '$file ($from)',
    isTestFile ? TextStyle.highlighted : TextStyle.normal,
  );

  if (deps[file] == null) {
    return;
  }

  for (var dependency in deps[file]!) {
    _visit(visited, filesToTest, deps, dependency, file, depth + 1, maxDepth);
  }
}
