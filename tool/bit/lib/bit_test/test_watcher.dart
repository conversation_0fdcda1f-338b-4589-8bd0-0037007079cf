import 'dart:isolate';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:watcher/watcher.dart';

class TestWatcher {
  static final TestWatcher _instance = TestWatcher._();

  final Set<String> _touchedFiles = <String>{};
  final Set<String> _currentTestFiles = <String>{};

  Isolate? _isolate;

  factory TestWatcher() => _instance;

  TestWatcher._();

  Iterable<String> get touchedFiles => [..._currentTestFiles];

  Future<void> onTestDidStart() async {
    if (_isolate == null) {
      writeln('Spawning watcher isolate...');
      _spawnNewIsolate();
      return;
    }
    await Future.delayed(Duration(milliseconds: 100));

    // If test failed, prepare to run changes + previous test files
    _touchedFiles.addAll(_currentTestFiles);

    _currentTestFiles.clear();
    _currentTestFiles.addAll(_touchedFiles);
    _touchedFiles.clear();
  }

  void onTestDidSucceed() {
    _currentTestFiles.clear();
  }

  void _spawnNewIsolate() async {
    final receivePort = ReceivePort();
    _isolate = await Isolate.spawn(_watch, receivePort.sendPort);
    receivePort.listen((dynamic file) {
      _touchedFiles.add(file);
    });
  }

  static void _watch(SendPort sendPort) {
    final watcher = Watcher('./');
    watcher.events.listen((event) {
      final path = event.path.substring(2);
      if (path.startsWith(RegExp(r'(lib/|test/)')) && path.endsWith('.dart')) {
        sendPort.send(path);
      }
    });
  }
}
