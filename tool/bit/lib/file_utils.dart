import 'dart:io';

import 'package:path/path.dart' as path;

void copyDirectory(Directory source, Directory destination) {
  source.listSync(recursive: false).forEach((var entity) {
    if (entity is Directory) {
      final newDirectory = Directory(
          path.join(destination.absolute.path, path.basename(entity.path)));
      newDirectory.createSync();

      copyDirectory(entity.absolute, newDirectory);
    } else if (entity is File) {
      entity.copySync(path.join(destination.path, path.basename(entity.path)));
    }
  });
}

Future<Iterable<String>> findEntitiesInDirectory(
  String directory,
  RegExp regexp,
) async {
  final dir = Directory(directory);
  final entities = await dir.list().toList();
  final matches = entities.where((e) => regexp.hasMatch(path.basename(e.path)));
  return matches.map((e) => e.path);
}
