import 'dart:async';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for amending commits.

Usage: 

  bit git amend
''';

Future<int> bitGitAmend([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  final branch = await gitGetCurrentBranch();
  write('Amending ', TextStyle.highlighted);
  write(branch, TextStyle.focus);
  writeln('...', TextStyle.highlighted);

  return await CommandLine.confirm(_amend).present() ?? 1;
}

Future<int> _amend() async {
  final add = await runProcess('git', ['add', '.']);
  if (add != 0) {
    return add;
  }

  return runProcess(
    'git',
    ['commit', '--amend', '--no-edit'],
    ProcessStartMode.inheritStdio,
  );
}
