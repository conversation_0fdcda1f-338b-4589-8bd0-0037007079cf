import 'dart:async';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for deleting a branch.

Checks out master, pulls, and deletes the current branch.

If branch is not fully merged, a forced delete will be suggested with a prompt.

Usage: 

  bit git delete
''';

Future<int> bitGitDelete([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  final branch = await gitGetCurrentBranch();
  writeln('Deleting \'$branch\'...', TextStyle.highlighted);

  return _maybeDeleteBranch(branch);
}

Future<int> _maybeDeleteBranch(String branch) async {
  if (branch == 'master') {
    writeln('Can\'t delete master branch.');
    return 1;
  }

  write('\nThis will checkout master, pull, and delete ');
  writeln(branch, TextStyle.focus);
  final delete =
      await CommandLine.confirm(() => _deleteBranch(branch)).present();
  return delete ?? 1;
}

Future<int> _deleteBranch(String branch) async {
  final checkoutMaster = await runProcess(
    'git',
    ['checkout', 'master'],
    ProcessStartMode.inheritStdio,
  );
  if (checkoutMaster != 0) {
    writeln('Failed to checkout master.', TextStyle.error);
    return 1;
  }

  final gitPull = await runProcess(
    'git',
    ['pull'],
    ProcessStartMode.inheritStdio,
  );
  if (gitPull != 0) {
    writeln('Failed to pull.', TextStyle.error);
    await gitCheckoutBranch(branch);
    return 1;
  }

  final deleteBranch = await startProcess(
    'git',
    ['branch', '-d', branch],
  );
  final didGitSuggestForceDelete = await assertProcessOutputsLine(
    deleteBranch,
    RegExp(
      'If you are sure you want to delete it, run \'git branch -D $branch\'.',
    ),
    true,
  );
  final result = await deleteBranch.exitCode;
  if (result == 0) {
    return 0;
  }

  if (didGitSuggestForceDelete != 0) {
    writeln('Failed to delete branch.', TextStyle.error);
    await gitCheckoutBranch(branch);
    return 1;
  }

  writeln('Branch is not fully merged.', TextStyle.error);
  write('\nThis will permanently delete ');
  writeln(branch, TextStyle.focus);
  writeln('> `git branch -D $branch`');
  final forceDelete = await CommandLine.confirm(
    () => runProcess(
      'git',
      ['branch', '-D', branch],
      ProcessStartMode.inheritStdio,
    ),
    title: 'Are you REALLY sure?',
    yes: 'yes-permanently-delete',
  ).present();

  if (forceDelete != 0) {
    await gitCheckoutBranch(branch);
  }
  return forceDelete ?? 1;
}
