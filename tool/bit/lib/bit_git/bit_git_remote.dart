import 'dart:async';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for managing remote git branches.

Usage:

  bit git remote
  
Options:

  checkout  ---  Check out a remote branch.
  list  - - ---  List all remote branches.
''';

Future<int> bitGitRemote([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  return _presentCommands();
}

Future<int> _presentCommands() async {
  return await CommandLine(
        {
          'checkout': _checkout,
          'list': _listRemotes,
        },
        title: 'Choose remote option.',
      ).present() ??
      1;
}

Future<int> _checkout() async {
  final isClean = await gitIsWorkingTreeClean();
  if (isClean != 0) {
    writeln('Interrupting. Working tree is not clean.', TextStyle.error);
    return 1;
  }

  final take = await CommandLine.take(
    _checkoutBranch,
    title: 'Choose branch to checkout.\n i.e. branch, origin/branch',
  ).present();
  if (take == null) {
    return _presentCommands();
  }
  return take;
}

Future<int?> _checkoutBranch(String branch) async {
  var localBranch = branch;
  if (branch.startsWith('origin/')) {
    localBranch = branch.substring('origin/'.length);
  }

  final remoteBranch = 'origin/$localBranch';
  write('Linking ');
  write(localBranch, TextStyle.highlighted);
  write(' with ');
  write(remoteBranch, TextStyle.error);
  writeln('\n\n  git checkout -b $localBranch $remoteBranch', TextStyle.help);

  return CommandLine.confirm(
    () async {
      return runProcess(
        'git',
        ['checkout', '-b', localBranch, remoteBranch],
        ProcessStartMode.inheritStdio,
      );
    },
  ).present();
}

Future<int> _listRemotes() async {
  final git = await startProcess(
    'git',
    [
      'for-each-ref',
      '--sort=committerdate',
      'refs/remotes/origin',
      '--format=\'%(HEAD) %(color:yellow)%(refname:short)%(color:reset) - %(color:red)%(objectname:short)%(color:reset) - %(contents:subject) - %(authorname) (%(color:green)%(committerdate:relative)%(color:reset))\'',
      '--color=always',
    ],
    ProcessStartMode.inheritStdio,
    false,
  );

  await git.exitCode;
  return _presentCommands();
}
