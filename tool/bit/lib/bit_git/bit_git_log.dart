import 'dart:async';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for logging git history.

Usage: 

  bit git log
''';

Future<int> bitGitLog([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  return runProcess(
    'git',
    ['log'],
    ProcessStartMode.inheritStdio,
  );
}
