import 'dart:convert';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/bit_version_bump/version.dart';
import 'package:bit/environment.dart';
import 'package:bit/file_utils.dart';
import 'package:bit/process.dart';
import 'package:path/path.dart' as path;

final _kBuildIpaRegex = RegExp(r'Built IPA to (.*)\.');
final _kBuildXcarchiveRegex = RegExp(r'Built (.*)\.xcarchive\.');
final _kBuildAppbundleRegex = RegExp(r'Built (.*)app-release\.aab ');

Future<BuildVersionBumpResult> buildForVersionBump(
    Version version, String target) async {
  writeln('Preparing $target build...', TextStyle.highlighted);
  final build = await startProcess('bit', [
    'build',
    target,
    '--no-header',
    '--symbols-output',
    '$kDataRepositoryDirectory/symbols/v$version/$target',
  ]);
  var ipaPath = '';
  var xcarchivePath = '';
  var appbundlePath = '';
  var appSize = 0;

  build.stdout.listen((event) {
    stdout.add(event);
    final split = utf8.decoder.convert(event).split('\n');
    for (final element in split) {
      switch (target) {
        case 'bit4ios':
          final xcarchiveMatch = _kBuildXcarchiveRegex.firstMatch(element);
          if (xcarchiveMatch != null) {
            xcarchivePath = '${xcarchiveMatch.group(1)!}.xcarchive';
          }
          final ipaMatch = _kBuildIpaRegex.firstMatch(element);
          if (ipaMatch != null) {
            ipaPath = ipaMatch.group(1)!.split(' ').first;
          }
          break;
        case 'bit4a':
          final appbundleMatch = _kBuildAppbundleRegex.firstMatch(element);
          if (appbundleMatch != null) {
            appbundlePath = '${appbundleMatch.group(1)!}app-release.aab';
          }
          break;
      }
    }
  });

  final result = await build.exitCode;
  if (result == 0 && (ipaPath.isEmpty && appbundlePath.isEmpty)) {
    // Process ended successfully, but not really.
    return BuildVersionBumpResult(2);
  }

  if (result != 0) {
    return BuildVersionBumpResult(result);
  }

  writeln('\nCopying output to archive directory ...', TextStyle.highlighted);

  final outputPath = '${version.outputPath}/$target';
  Directory(outputPath).createSync(recursive: true);

  if (ipaPath.isNotEmpty) {
    copyDirectory(Directory(ipaPath), Directory(outputPath));
    appSize = File(path.join(outputPath, 'Bitacora.io.ipa')).lengthSync();
  }

  if (xcarchivePath.isNotEmpty) {
    final outputXcarchiveDir =
        Directory(path.join(outputPath, path.basename(xcarchivePath)));
    outputXcarchiveDir.createSync(recursive: true);
    copyDirectory(Directory(xcarchivePath), outputXcarchiveDir);
  }

  if (appbundlePath.isNotEmpty) {
    File(appbundlePath)
        .copySync(path.join(outputPath, path.basename(appbundlePath)));
    appSize = File(appbundlePath).lengthSync();
  }

  writeln('$target app size $appSize');
  writeln('$target build output in $outputPath');
  return BuildVersionBumpResult(0, appSize);
}

class BuildVersionBumpResult {
  final int exitCode;
  final int appSize;

  BuildVersionBumpResult(this.exitCode, [this.appSize = 0]);
}
