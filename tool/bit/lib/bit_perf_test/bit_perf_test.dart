import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/process.dart';
import 'package:collection/collection.dart';

const _kFlagHelp = '-h';
const _kFlagProfile = '--profile';
const _kFlagRunCount = '--run-count';
const _kFlagKeepDb = '--keep-db';
const _kFlagAbTest = '--ab-test';

const _help = '''
A tool for running performance tests.

Runs `bit integration-test` with correct arguments for performance tests.
Hot-restart is available in debug mode.

Available arguments:
 
 -h              Prints help.
 
 --profile       Runs in profile mode.
 
 --run-count 3   Runs test `n` times.
                 Default is 1 (debug) and 5 (profile).
                 
 --keep-db       Keeps a copy of the database before nuking.
                 
 --ab-test       Runs A|B test. 

Usage:

  bit perf-test <name> [--profile | --run-count <n>] 
  bit perf-test sync --profile
  bit perf-test sync --run-count 2
  bit perf-test sync --run-count 2 --ab-test
  bit perf-test sync --run-count 2 --keep-db

''';

const _integrationTests = {
  'sync':
      'integration_test/presentation/login/login_page_integration_test.dart',
  'login':
      'integration_test/presentation/login/login_page_integration_test.dart',
};

Future<int> bitPerfTest([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  final isProfile = args.contains(_kFlagProfile);
  final runCountFlagIndex = args.indexOf(_kFlagRunCount);
  final runCount = runCountFlagIndex >= 0
      ? (int.parse(args[runCountFlagIndex + 1]))
      : (isProfile ? 5 : 1);

  final name = args.firstWhereIndexedOrNull((i, e) =>
          !e.startsWith('--') &&
          (runCountFlagIndex < 0 || i != runCountFlagIndex + 1)) ??
      _integrationTests.keys.first;
  final integrationTest = _integrationTests[name];
  if (integrationTest == null) {
    writeln('Unknown performance test $name', TextStyle.error);
    return 1;
  }

  writeln(
    'Testing `$name` performance${isProfile ? ' in profile mode' : ''}'
    ' (n=$runCount)...',
    TextStyle.highlighted,
  );

  final test = await startProcess(
    'bit',
    [
      'integration-test',
      _integrationTests[name]!,
      '--no-header',
      if (isProfile) '--profile',
      '--dart-define=PERF_TEST=$name',
      if (runCount > 1) '--dart-define=PERF_TEST_RUN_COUNT=$runCount',
      if (args.contains(_kFlagKeepDb)) '--dart-define=PERF_TEST_KEEP_DB=true',
      if (args.contains(_kFlagAbTest)) '--dart-define=PERF_TEST_AB_TEST=true',
    ],
    ProcessStartMode.inheritStdio,
  );
  return test.exitCode;
}
