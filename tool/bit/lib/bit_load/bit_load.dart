import 'dart:convert';
import 'dart:io';

import 'package:archive/archive.dart';
import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

// FIXME: load preferences
// FIXME: iOS
const _help = '''
A tool for loading app data from a bug report. 
Copies data to app directory.

Works for:
 Android Device
*A debuggable com.bitacora.locust app must be installed on device/simulator.

Available arguments:

 -h               Prints help.
 
Usage:
 
  bit load bug_report.zip

''';

const _kFriendlyAndroidDirectory = '/sdcard';

const _kDbFilename = 'bitacora.db';

Future<int> bitLoad([List<String> args = const <String>[]]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  if (args.length != 1) {
    writeln(_help, TextStyle.help);
    return 1;
  }

  return _load(args.first);
}

Future<int> _load(String bugReportZip) async {
  writeln('Loading app data into device...', TextStyle.highlighted);

  final extractOut = Directory.systemTemp;
  final extract = await _extractData(bugReportZip, extractOut);
  if (extract != 0) {
    return extract;
  }
  final pushed = await _pushDataToDevice(extractOut);
  if (pushed != 0) {
    return pushed;
  }
  writeln('Done, hot-restart or open the app.', TextStyle.highlighted);
  return 0;
}

Future<int> _pushDataToDevice(Directory extracted) {
  return _androidPushToDevice(extracted);
}

Future<int> _extractData(String bugReportZip, Directory out) async {
  writeln('Extracting data from $bugReportZip ...');

  final bytes = File(bugReportZip).readAsBytesSync();
  final archive = ZipDecoder().decodeBytes(bytes);

  for (final file in archive) {
    final filename = file.name;
    if (file.isFile) {
      if (filename == _kDbFilename) {
        final data = file.content as List<int>;
        final extractedFilePath = '${out.path}/$filename';
        File(extractedFilePath)
          ..createSync(recursive: true)
          ..writeAsBytesSync(data);
      }
    }
  }
  return 0;
}

Future<int> _androidPushToDevice(Directory extracted) async {
  final pushedToFriendly = await _androidPushToFriendlyDirectory(extracted);
  if (pushedToFriendly != 0) {
    return pushedToFriendly;
  }
  return _androidPushToApplicationDirectory();
}

Future<int> _androidPushToFriendlyDirectory(Directory extracted) async {
  writeln('Pushing data into android device ...');
  return runProcess('adb', [
    'push',
    '${extracted.path}/$_kDbFilename',
    '$_kFriendlyAndroidDirectory/$_kDbFilename',
  ]);
}

Future<int> _androidPushToApplicationDirectory() async {
  writeln('Pushing data into application...');

  final shell = await startProcess('adb', ['shell']);
  shell.stdout.listen((event) {
    writeln('[shell] $event');
  });
  final commands = [
    'run-as com.bitacora.locust',
    'cd databases',
    'cp -R $_kFriendlyAndroidDirectory/$_kDbFilename $_kDbFilename',
    'chmod 600 $_kDbFilename',
    'exit',
    'exit',
  ];
  for (final command in commands) {
    writeln(' > $command');
    shell.stdin.add(utf8.encode('$command\n'));
  }

  return shell.exitCode;
}
