# GraphQLesque Sync Endpoint (Draft)


1. Get multiple collection data from one endpoint.

2. Get paging data from each individual collection.

3. Request more data based on “next_page_token” or “sync_time” from previous responses.


### First request

Not sending any parameters, other than the collections of interest, because there is no data.

POST https://api.bitacora.io/sync

```json
{
  "organization_id":1233, 
  "collections": {
    "organizations":{},
    "projects":{},
    "users":{},
    "accesses":{},
    "resources":{},
    "entries":{}
  }
}
```


Response:

```json
{
  "collections: {
    "organizations" : {
      "sync_time": 123,
      "next_page_token": 123,
      "archived_data": [1, 4, 5, ...],
      "data": [
        {
          "id":1,
          ...
        },
        ...
      ],
    },
    "projects" : {
      "sync_time": 123,
      "data": [
        {
          "id":12,
          ...
        },
        ...
      ],
    },
    ... // More collections
  ]
}
```



### Second request

We didn't finish syncing due to `next_page_token` in one collection.

POST https://api.bitacora.io/sync

```json
{
  "organization_id": 1233,
  "collections": {
    "organizations": {
      // From "next_page_token" in last response.
      "last_sync_time": 31232
    }
  }
}
```


### Subsequent requests

We finished syncing, but time has passed, and we want new data.

POST https://api.bitacora.io/sync

```json
{
  "organization_id": 1233,
  "collections": {
    "organizations": {
      // From "sync_time" in last response.
      "last_sync_time": 31232
    },
    "projects": {
      "last_sync_time": 24
    },
    "users": {
      "last_sync_time": 11
    },
    "accesses": {
      "last_sync_time": 553
    },
    "resources": {
      "last_sync_time": 12
    },
    "entries": {
      "last_sync_time": 321
    }
  }
}
```

