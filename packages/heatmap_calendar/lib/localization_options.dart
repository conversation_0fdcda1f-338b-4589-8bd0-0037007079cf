import 'dart:io';

import 'package:flutter/cupertino.dart';

class LocalizationOptions {
  final String languageCode;
  final List<String> months;
  final List<String> days;
  final String less;
  final String more;
  final String today;

  static LocalizationOptions getDefaultLocalizationOptionsForLanguage(
    BuildContext context,
  ) {
    return Platform.localeName.startsWith('es')
        ? LocalizationOptions.buildDefaultSpanishOptions()
        : LocalizationOptions.buildDefaultEnglishOptions();
  }

  LocalizationOptions(
    this.languageCode, {
    this.months = const [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ],
    this.days = const [
      'Sun',
      'Mon',
      'Tue',
      'Wed',
      'Thu',
      'Fri',
      'Sat',
    ],
    this.less = 'less',
    this.more = 'more',
    this.today = 'Today',
  });

  static LocalizationOptions buildDefaultEnglishOptions() {
    return LocalizationOptions('en');
  }

  static LocalizationOptions buildDefaultSpanishOptions() {
    return LocalizationOptions(
      'es',
      months: [
        'En<PERSON>',
        'Febrero',
        '<PERSON><PERSON>',
        '<PERSON>bri<PERSON>',
        '<PERSON>',
        '<PERSON><PERSON>',
        '<PERSON>',
        'Agosto',
        'Septiembre',
        'Octubre',
        'Noviembre',
        'Diciembre',
      ],
      days: [
        'Dom',
        'Lun',
        'Mar',
        'Mie',
        'Jue',
        'Vie',
        'Sab',
      ],
      less: 'menos',
      more: 'más',
      today: 'Hoy',
    );
  }
}
