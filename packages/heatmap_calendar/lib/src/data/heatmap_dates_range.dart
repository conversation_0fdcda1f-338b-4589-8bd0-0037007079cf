class DatesRange {
  final DateTime start;
  final DateTime end;

  DatesRange(this.start, this.end);

  bool isDateInRange(DateTime dateTime) {
    return dateTime.millisecondsSinceEpoch >= start.millisecondsSinceEpoch &&
        dateTime.millisecondsSinceEpoch <= end.millisecondsSinceEpoch;
  }

  bool isDayInRange(DateTime dateTime) {
    final startDay = DateTime(start.year, start.month, start.day);
    final endDay = DateTime(end.year, end.month, end.day);
    final day = DateTime(dateTime.year, dateTime.month, dateTime.day);
    return day.millisecondsSinceEpoch >= startDay.millisecondsSinceEpoch &&
        day.millisecondsSinceEpoch <= endDay.millisecondsSinceEpoch;
  }
}
