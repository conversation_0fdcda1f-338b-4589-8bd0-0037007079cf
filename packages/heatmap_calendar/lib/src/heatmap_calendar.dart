import 'package:flutter/material.dart';
import 'package:heatmap_calendar/heatmap_calendar.dart';
import 'package:heatmap_calendar/localization_options.dart';
import 'package:heatmap_calendar/src/util/date_util.dart';
import 'package:heatmap_calendar/src/util/size_reporting_widget.dart';
import 'package:heatmap_calendar/src/util/widget_util.dart';
import 'package:heatmap_calendar/src/widget/heatmap_calendar_page.dart';
import 'package:heatmap_calendar/src/widget/heatmap_color_tip.dart';

const _kInitialPage = 1200;
const _kSwitchPageAnimationDurationMs = Duration(milliseconds: 250);

class HeatMapCalendar extends StatefulWidget {
  final Map<DateTime, int>? datasets;
  final Color? defaultColor;
  final Map<int, Color> colorSet;
  final double? borderRadius;
  final DateTime? initDate;
  final double? size;
  final Color? textColor;
  final double? fontSize;
  final double? monthFontSize;
  final double? weekFontSize;
  final Color? weekTextColor;
  final bool? flexible;
  final EdgeInsets? margin;
  final DatesRange? enabledDatesRange;
  final bool autoHideOnSelect;

  /// ColorMode changes the color mode of blocks.
  ///
  /// [ColorMode.opacity] requires just one colorsets value and changes color
  /// dynamically based on hightest value of [datasets].
  /// [ColorMode.color] changes colors based on [colorSet] thresholds key value.
  ///
  /// Default value is [ColorMode.opacity].
  final ColorMode colorMode;

  /// Function that will be called when a date is selected.
  ///
  /// Paratmeter gives clicked [DateTime] value.
  final Function(DateTime)? onSelect;

  /// Function that will be called when month is changed.
  ///
  /// Paratmeter gives [DateTime] value of current month.
  final Function(DateTime)? onMonthChange;

  /// Show color tip which represents the color range at the below.
  ///
  /// Default value is true.
  final bool? showColorTip;

  /// Widgets which shown at left and right side of colorTip.
  ///
  /// First value is the left side widget and second value is the right side widget.
  /// Be aware that [colorTipHelper.length] have to greater or equal to 2.
  /// Give null value makes default 'less' and 'more' [Text].
  final List<Widget?>? colorTipHelper;

  /// The integer value which represents the number of [HeatMapColorTip]'s tip container.
  final int? colorTipCount;

  /// The double value of [HeatMapColorTip]'s tip container's size.
  final double? colorTipSize;

  const HeatMapCalendar({
    Key? key,
    required this.colorSet,
    this.colorMode = ColorMode.opacity,
    this.defaultColor,
    this.datasets,
    this.initDate,
    this.size = 42,
    this.fontSize,
    this.monthFontSize,
    this.textColor,
    this.weekFontSize,
    this.weekTextColor,
    this.borderRadius,
    this.flexible = false,
    this.margin,
    this.onSelect,
    this.onMonthChange,
    this.showColorTip = true,
    this.colorTipHelper,
    this.colorTipCount,
    this.colorTipSize,
    this.enabledDatesRange,
    this.autoHideOnSelect = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _HeatMapCalendar();
}

class _HeatMapCalendar extends State<HeatMapCalendar> {
  final PageController _pageController =
      PageController(initialPage: _kInitialPage);
  final ValueNotifier<DateTime?> _selectedDate = ValueNotifier(null);
  double _childHeight = 0;
  DateTime? _baseDate;
  LocalizationOptions? _localizationOptions;

  @override
  void initState() {
    super.initState();
    setState(() {
      _baseDate = DateUtil.startDayOfMonth(widget.initDate ?? DateTime.now());
      _selectedDate.value = widget.initDate ?? DateTime.now();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _localizationOptions ??=
        LocalizationOptions.getDefaultLocalizationOptionsForLanguage(context);
    return _intrinsicWidth(
      child: SizedBox(
        height: _childHeight,
        child: Stack(
          children: [
            PageView.builder(
              physics: const ClampingScrollPhysics(),
              controller: _pageController,
              onPageChanged: (index) {
                if (widget.onMonthChange == null) {
                  return;
                }
                widget.onMonthChange!(_monthDate(index));
              },
              itemBuilder: (context, index) {
                final monthDate = _monthDate(index);
                return OverflowBox(
                  minHeight: 0,
                  maxHeight: double.infinity,
                  alignment: Alignment.topCenter,
                  child: SizeReportingWidget(
                    onSizeChange: (size) {
                      if (size.height != _childHeight) {
                        setState(() => _childHeight = size.height);
                      }
                    },
                    child: Align(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _header(monthDate),
                          _weekLabel(),
                          HeatMapCalendarPage(
                            baseDate: monthDate,
                            selectedDate: _selectedDate,
                            colorMode: widget.colorMode,
                            flexible: widget.flexible,
                            size: widget.size,
                            fontSize: widget.fontSize,
                            defaultColor: widget.defaultColor,
                            textColor: widget.textColor,
                            margin: widget.margin,
                            datasets: widget.datasets,
                            colorsets: widget.colorSet,
                            borderRadius: widget.borderRadius,
                            onClick: (DateTime selected) {
                              if (widget.onSelect != null) {
                                widget.onSelect!(selected);
                              }

                              if (widget.autoHideOnSelect) {
                                Navigator.of(context).pop();
                              }
                            },
                            enabledDatesRange: widget.enabledDatesRange,
                          ),
                          if (widget.showColorTip == true)
                            HeatMapColorTip(
                              colorMode: widget.colorMode,
                              colorsets: widget.colorSet,
                              leftWidget: widget.colorTipHelper?[0],
                              rightWidget: widget.colorTipHelper?[1],
                              containerCount: widget.colorTipCount,
                              size: widget.colorTipSize,
                              localizationOptions: _localizationOptions!,
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            _switchMonthControls(),
            Positioned(
              left: 8,
              right: 4,
              bottom: 12,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () {
                      final now = DateTime.now();
                      final today = DateTime(now.year, now.month, now.day);
                      _selectedDate.value = today;
                      final monthDate =
                          _monthDate(_pageController.page!.round());
                      if (monthDate.year != today.year ||
                          monthDate.month != today.month) {
                        setState(() {
                          _baseDate = DateUtil.startDayOfMonth(today);
                          _pageController.jumpToPage(_kInitialPage);
                        });
                        if (widget.onMonthChange != null) {
                          widget.onMonthChange!(_baseDate!);
                        }
                      }
                      if (widget.onSelect != null) {
                        widget.onSelect!(today);
                      }

                      if (widget.autoHideOnSelect) {
                        Navigator.of(context).pop();
                      }
                    },
                    child: Text(LocalizationOptions
                            .getDefaultLocalizationOptionsForLanguage(context)
                        .today),
                  ),
                  if (!widget.autoHideOnSelect)
                    IconButton(
                      onPressed: Navigator.of(context).pop,
                      icon: const Icon(Icons.done),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  DateTime _monthDate(int currentIndex) {
    return currentIndex == _kInitialPage
        ? _baseDate!
        : DateUtil.changeMonth(_baseDate!, currentIndex - _kInitialPage);
  }

  /// Header widget which shows left, right buttons and year/month text.
  Widget _header(DateTime monthDate) {
    final titleLargeTheme = Theme.of(context).textTheme.titleLarge!;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            '${DateUtil.monthLabel(monthDate.month, _localizationOptions!)} ${monthDate.year}',
            style: titleLargeTheme.copyWith(
              fontSize: widget.monthFontSize ?? titleLargeTheme.fontSize,
            ),
          ),
        ],
      ),
    );
  }

  Widget _weekLabel() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          for (String label in _localizationOptions!.days)
            WidgetUtil.flexibleContainer(
              widget.flexible ?? false,
              false,
              Container(
                margin: EdgeInsets.only(
                    left: widget.margin?.left ?? 2,
                    right: widget.margin?.right ?? 2),
                width: widget.size ?? 42,
                alignment: Alignment.center,
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: widget.weekFontSize ?? 12,
                    color: widget.weekTextColor ?? const Color(0xFF758EA1),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _switchMonthControls() {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => _pageController.previousPage(
              duration: _kSwitchPageAnimationDurationMs,
              curve: Curves.easeInOut,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward_ios),
            onPressed: () => _pageController.nextPage(
              duration: _kSwitchPageAnimationDurationMs,
              curve: Curves.easeInOut,
            ),
          ),
        ],
      ),
    );
  }

  /// Expand width dynamically if [flexible] is true.
  Widget _intrinsicWidth({
    required Widget child,
  }) =>
      (widget.flexible ?? false) ? child : IntrinsicWidth(child: child);
}
