import 'package:flutter/material.dart';
import 'package:heatmap_calendar/src/data/heatmap_dates_range.dart';

class HeatMapContainer extends StatelessWidget {
  final DateTime date;
  final double? size;
  final double? fontSize;
  final double? borderRadius;
  final Color? backgroundColor;
  final Color? selectedColor;
  final Color? textColor;
  final FontWeight? fontWeight;
  final EdgeInsets? margin;
  final ValueNotifier<DateTime?> selectedDate;
  final Function(DateTime dateTime)? onClick;
  final DatesRange? enabledDatesRange;

  const HeatMapContainer({
    Key? key,
    required this.date,
    required this.selectedDate,
    this.margin,
    this.size,
    this.fontSize,
    this.fontWeight,
    this.borderRadius,
    this.backgroundColor,
    this.selectedColor,
    this.textColor,
    this.onClick,
    this.enabledDatesRange,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDisabled = enabledDatesRange != null
        ? !enabledDatesRange!.isDayInRange(date)
        : false;

    return ValueListenableBuilder<DateTime?>(
      valueListenable: selectedDate,
      builder: (context, currentSelectedDate, _) {
        final isBaseDate = date == currentSelectedDate;
        return Opacity(
          opacity: isDisabled ? 0.25 : 1,
          child: InkWell(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            onTap: isDisabled
                ? null
                : () {
                    selectedDate.value = date;
                    onClick != null ? onClick!(date) : null;
                  },
            child: FractionallySizedBox(
              widthFactor: 0.85,
              heightFactor: 0.85,
              child: Container(
                decoration: BoxDecoration(
                  color: backgroundColor ?? Colors.transparent,
                  borderRadius:
                      BorderRadius.all(Radius.circular(borderRadius ?? 5)),
                ),
                child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeInOutQuad,
                    width: size,
                    height: size,
                    alignment: Alignment.center,
                    decoration: ShapeDecoration(
                      shape: isBaseDate
                          ? const CircleBorder()
                          : RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.circular(borderRadius ?? 5)),
                      color: isBaseDate
                          ? Theme.of(context).colorScheme.primary
                          : selectedColor,
                    ),
                    child: Text(
                      date.day.toString(),
                      style: TextStyle(
                        color: isBaseDate
                            ? Theme.of(context).colorScheme.onPrimary
                            : textColor ??
                                Theme.of(context).colorScheme.onSurface,
                        fontSize: fontSize,
                        fontWeight: isBaseDate ? FontWeight.w600 : fontWeight,
                      ),
                    )),
              ),
            ),
          ),
        );
      },
    );
  }
}
