import 'package:bitacora/presentation/login/login_page.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../base_integration_test_robot.dart';
import '../../integration_test.dart';
import '../login/login_page_robot.dart';

void main() async {
  BaseIntegrationTestRobot.ensureInitialized();

  group('$LoginPage integration tests', () {
    integrationTestWidget('', (tester) async {
      final robot = LoginPageRobot(tester);
      await robot.runAppInTestMode();
      await robot.nukeIfLoggedIn();

      await robot.loginWithFreeUser();

      robot.verifyLoggedIn();

      await robot.nuke();
    });
  });
}
