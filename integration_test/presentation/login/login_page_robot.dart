import 'package:bitacora/presentation/daylog/daylog_page.dart';
import 'package:bitacora/presentation/onboarding/onboarding_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../base_integration_test_robot.dart';

class LoginPageRobot extends BaseIntegrationTestRobot {
  LoginPageRobot(super.tester);

  Future<void> _skipOnboarding() async {
    await tester.tap(find.text(appLocalizations.login));
    await tester.pumpAndSettle();
  }

  Future<void> nukeIfLoggedIn() async {
    try {
      expect(find.byType(OnboardingPage), findsOneWidget);
    } catch (_) {
      await nuke();
    }
  }

  Future<void> loginWithFreeUser() async {
    await _skipOnboarding();

    await tester.enterText(
      find.byType(TextFormField).at(0),
      '<EMAIL>',
    );
    await tester.enterText(
      find.byType(TextFormField).at(1),
      '1234aoeu',
    );

    await tester.tap(find.text(appLocalizations.login));
    await tester.pumpAndSettle();
  }

  void verifyLoggedIn() {
    expect(find.byType(DaylogPage), findsOneWidget);
    expect(activeSession.value != null, true);
  }
}
