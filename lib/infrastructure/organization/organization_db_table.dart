import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/organization/organization_repository.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/domain/report_template/report_template.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/organization/organization_db_contract.dart';
import 'package:bitacora/infrastructure/organization/organization_db_fields_builder.dart';
import 'package:bitacora/infrastructure/organization/organization_db_translator.dart';
import 'package:bitacora/infrastructure/user_organization/user_organization_db_contract.dart';
import 'package:sqflite/sqflite.dart';

class OrganizationDbTable
    extends DbTable<Organization, OrganizationDbFieldsBuilder>
    implements OrganizationRepository<DbContext, OrganizationDbFieldsBuilder> {
  @override
  OrganizationDbContract get contract => const OrganizationDbContract();

  UserOrganizationDbContract get userOrganizationContract =>
      const UserOrganizationDbContract();

  @override
  DbTranslator<Organization> get translator => const OrganizationDbTranslator();

  @override
  OrganizationDbFieldsBuilder get fieldsBuilder =>
      OrganizationDbFieldsBuilder();

  @override
  Future<List<Organization>> findAll(DbContext context) {
    return query(context, orderBy: contract.name);
  }

  @override
  void fixFailedInsertValues(Map<String, dynamic> values, DatabaseException e) {
    values['o_proStatus'] = 1;
  }

  @override
  Future<void> saveUserRelation(
      DbContext context, LocalId userId, LocalId organizationId) async {
    final executor = await context.executor;
    await executor.insert(
      userOrganizationContract.tableName,
      {
        userOrganizationContract.userId: userId.dbValue,
        userOrganizationContract.organizationId: organizationId.dbValue,
      },
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );
  }

  @override
  Future<void> deleteUserRelation(
      DbContext context, LocalId userId, LocalId organizationId) async {
    final executor = await context.executor;
    await executor.delete(
      userOrganizationContract.tableName,
      where: '${userOrganizationContract.userId} = ? '
          'AND ${userOrganizationContract.organizationId} = ?',
      whereArgs: [
        userId.dbValue,
        organizationId.dbValue,
      ],
    );
  }

  @override
  Future<void> deleteAllUserRelations(
      DbContext context, Organization organization) async {
    final executor = await context.executor;
    await executor.delete(
      userOrganizationContract.tableName,
      where: '${userOrganizationContract.organizationId} = ?',
      whereArgs: [organization.id!.dbValue],
    );
  }

  @override
  Future<void> onPostDelete(DbContext context, LocalId id) async {
    await super.onPostDelete(context, id);
    final db = context.db;

    await Future.forEach<Project>(
      await db.project.findByOrganization(
          context.copyWith(fields: db.project.fieldsBuilder.build()), id),
      (project) => db.project.delete(context, project.id!),
    );
    await Future.forEach<Tag>(
      await db.tag.findByOrganization(
          context.copyWith(fields: db.tag.fieldsBuilder.build()), id),
      (tag) => db.tag.delete(context, tag.id!),
    );
    await deleteAllUserRelations(context, Organization(id: id));
    await db.user.cleanUp(context);

    await Future.forEach<Report>(
      await db.report.findAll(
        context.copyWith(
          fields: db.report.fieldsBuilder.build(),
          queryScope: QueryScope(orgId: id),
        ),
      ),
      (report) => db.report.delete(context, report.id!),
    );

    await Future.forEach<ReportTemplate>(
      await db.reportTemplate.findAll(
        context.copyWith(
          fields: db.reportTemplate.fieldsBuilder.build(),
          queryScope: QueryScope(orgId: id),
        ),
      ),
      (reportTemplate) => db.reportTemplate.delete(context, reportTemplate.id!),
    );
    // FIXME: verify templates are deleted properly
  }
}
