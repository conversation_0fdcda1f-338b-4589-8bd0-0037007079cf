import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/auth/auth_session_user_repository_query.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/infrastructure/auth/current_api_token.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/fcm/fcm_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthRepositoryInjector {
  factory AuthRepositoryInjector() =>
      inject(() => const AuthRepositoryInjector._());

  const AuthRepositoryInjector._();

  AuthRepository get(Repository db) =>
      inject(() => AuthMixedDbRepository._(db));
}

class AuthMixedDbRepository extends AuthRepository {
  final Repository db;
  final String authTokenKey = SharedPreferencesKeys.authToken;
  final String userIdKey = SharedPreferencesKeys.activeUserId;

  AuthMixedDbRepository._(this.db);

  @override
  Future<Session?> load() async {
    final prefs = await SharedPreferences.getInstance();
    final token = await _getToken();
    if (token == null) {
      return null;
    }

    final userId = prefs.getInt(userIdKey);
    if (userId == null) {
      return null;
    }

    final user = await db.query(
      AuthSessionUserRepositoryQuery(id: LocalId(userId)),
    );
    if (user == null) {
      logger.f('Session token found but no user.');
      return null;
    }

    return Session(
      token: SessionToken(token),
      user: user,
      invalidStatusCode:
          prefs.getInt(SharedPreferencesKeys.invalidSessionStatusCode),
    );
  }

  Future<String?> _getToken() async {
    return (await CurrentApiToken().getToken()) ??
        (await SharedPreferences.getInstance()).getString(authTokenKey);
  }

  @override
  Future<void> save(Session session) async {
    final prefs = await SharedPreferences.getInstance();
    await CurrentApiToken().saveToken(session.token.dbValue);
    await prefs.setInt(userIdKey, session.user.id!.dbValue);

    await validate();
  }

  @override
  Future<void> hotSwapToken(String prevToken, String nextToken) async {
    if ((await _getToken()) == prevToken) {
      logger.i('auth:hotswap Replacing current token');
      await CurrentApiToken().saveToken(nextToken);
    }
  }

  @override
  Future<void> invalidate(int statusCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
        SharedPreferencesKeys.invalidSessionStatusCode, statusCode);
    notifyListeners();
  }

  @override
  Future<void> validate() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(SharedPreferencesKeys.invalidSessionStatusCode);
    notifyListeners();
  }

  @override
  Future<void> nuke() async {
    /// FIXME: delete from keystore/keychain ?
    /// FIXME: if we are nuking the entire shared prefs, move elsewhere...
    await FcmUtils().nuke();

    final prefs = await SharedPreferences.getInstance();
    final onboardingWasSeen =
        prefs.getBool(SharedPreferencesKeys.onboardingWasSeen) ?? false;
    await prefs.clear();
    await prefs.setBool(
        SharedPreferencesKeys.onboardingWasSeen, onboardingWasSeen);
    await CurrentApiToken().nuke();

    notifyListeners();
  }
}
