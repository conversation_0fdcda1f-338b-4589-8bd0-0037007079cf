import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

class CurrentApiToken {
  static final _instance = CurrentApiToken._();

  final Set<String> expiredTokens = {};

  factory CurrentApiToken() => inject(() => _instance);

  CurrentApiToken._();

  Future<File> _getFile() async {
    final pathFile = path.join(
        (await getApplicationSupportDirectory()).path, 'current_api_token.txt');
    return FileSystemInjector.get().file(pathFile);
  }

  Future<void> saveToken(String token) async {
    if (expiredTokens.contains(token)) {
      logger.f('current-api-token Attempting to save an expired token.');
      return;
    }
    await (await _getFile()).writeAsString(token);
  }

  Future<String?> getToken() async {
    final file = await _getFile();
    if (!(await file.exists())) {
      return null;
    }
    for (var i = 0; i < 5; i++) {
      final token = await file.readAsString();
      if (token.isNotEmpty) {
        return token;
      }
      logger.f('current-api-token Read an empty token. Trying again $i.');
      await Future.delayed(const Duration(milliseconds: 15));
    }
    logger.f('current-api-token Failed to read token.');
    return null;
  }

  Future<void> nuke() async {
    final file = await _getFile();
    if (file.existsSync()) {
      return file.deleteSync();
    }
  }
}
