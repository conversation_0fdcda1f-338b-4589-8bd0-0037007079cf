import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/sync_pending_relation/sync_pending_relation.dart';
import 'package:bitacora/domain/sync_pending_relation/sync_pending_relation_repository.dart';
import 'package:bitacora/domain/sync_pending_relation/value/sync_pending_relation_collection_type.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/sync_pending_relation/sync_pending_relation_db_contract.dart';
import 'package:bitacora/infrastructure/sync_pending_relation/sync_pending_relation_db_fields_builder.dart';
import 'package:bitacora/infrastructure/sync_pending_relation/sync_pending_relation_db_translator.dart';

class SyncPendingRelationDbTable extends DbTable<SyncPendingRelation, SyncPendingRelationDbFieldsBuilder>
    implements SyncPendingRelationRepository<DbContext, SyncPendingRelationDbFieldsBuilder> {
  
  final DbTranslator<SyncPendingRelation> _translator;

  SyncPendingRelationDbTable([this._translator = const SyncPendingRelationDbTranslator()])
      : super(isSyncable: false);

  @override
  SyncPendingRelationDbContract get contract => const SyncPendingRelationDbContract();

  @override
  SyncPendingRelationDbFieldsBuilder get fieldsBuilder => SyncPendingRelationDbFieldsBuilder();

  @override
  DbTranslator<SyncPendingRelation> get translator => _translator;

  @override
  Future<List<SyncPendingRelation>> findByCollectionType(
    DbContext context,
    SyncPendingRelationCollectionType collectionType,
  ) async {
    return query(
      context,
      where: '${contract.collectionType} = ? AND ${contract.organizationId} = ?',
      whereArgs: [
        collectionType.dbValue,
        context.queryScope!.orgId!.dbValue,
      ],
    );
  }

  @override
  Future<void> deleteByChildLocalId(
    DbContext context,
    LocalId childLocalId,
  ) async {
    final executor = await context.executor;
    await executor.delete(
      contract.tableName,
      where: '${contract.childLocalId} = ?',
      whereArgs: [childLocalId.dbValue],
    );
  }

  @override
  Future<void> deleteByCollectionType(
    DbContext context,
    SyncPendingRelationCollectionType collectionType,
  ) async {
    final executor = await context.executor;
    await executor.delete(
      contract.tableName,
      where: '${contract.collectionType} = ? AND ${contract.organizationId} = ?',
      whereArgs: [
        collectionType.dbValue,
        context.queryScope!.orgId!.dbValue,
      ],
    );
  }
}
