import 'dart:convert';

import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/domain/sync_pending_relation/sync_pending_relation.dart';
import 'package:bitacora/domain/sync_pending_relation/sync_pending_relation_fields_builder.dart';
import 'package:bitacora/domain/sync_pending_relation/value/sync_pending_relation_created_at.dart';
import 'package:bitacora/domain/sync_pending_relation/value/sync_pending_relation_pending_data.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/sync_pending_relation/sync_pending_relation_db_contract.dart';

class SyncPendingRelationDbFieldsBuilder extends DbFieldsBuilder
    implements SyncPendingRelationFieldsBuilder {
  SyncPendingRelationDbFieldsBuilder() {
    _id();
  }

  SyncPendingRelationDbContract get contract =>
      const SyncPendingRelationDbContract();

  SyncPendingRelationDbFieldsBuilder _id() {
    addField(
      SyncPendingRelationField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  SyncPendingRelationDbFieldsBuilder remoteId() {
    addField(
      SyncPendingRelationField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  SyncPendingRelationDbFieldsBuilder collectionType() {
    addField(
      SyncPendingRelationField.collectionType,
      DbField(
        column: contract.collectionType,
        valueBuilder: (v) => SyncMetadataCollectionType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  SyncPendingRelationDbFieldsBuilder parentRemoteId() {
    addField(
      SyncPendingRelationField.parentRemoteId,
      DbField(
        column: contract.parentRemoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  SyncPendingRelationDbFieldsBuilder childLocalId() {
    addField(
      SyncPendingRelationField.childLocalId,
      DbField(column: contract.childLocalId, valueBuilder: (v) => LocalId(v)),
    );
    return this;
  }

  @override
  SyncPendingRelationDbFieldsBuilder pendingData() {
    addField(
      SyncPendingRelationField.pendingData,
      DbField(
        column: contract.pendingData,
        valueBuilder: (v) =>
            SyncPendingRelationPendingData.fromMap(jsonDecode(v)),
      ),
    );
    return this;
  }

  @override
  SyncPendingRelationDbFieldsBuilder organization(Fields fields) {
    addField(
      SyncPendingRelationField.organization,
      DbField(
        key: SyncPendingRelationField.organization,
        column: contract.organizationId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  SyncPendingRelationDbFieldsBuilder createdAt() {
    addField(
      SyncPendingRelationField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) => SyncPendingRelationCreatedAt(
            DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }
}
