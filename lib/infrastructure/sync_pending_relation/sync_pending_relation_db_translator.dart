import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/sync_pending_relation/sync_pending_relation.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/sync_pending_relation/sync_pending_relation_db_contract.dart';

class SyncPendingRelationDbTranslator
    implements DbTranslator<SyncPendingRelation> {
  const SyncPendingRelationDbTranslator();

  SyncPendingRelationDbContract get contract =>
      const SyncPendingRelationDbContract();

  @override
  Set<Field> get nestedModelFields => syncPendingRelationNestedModelFields;

  @override
  Future<SyncPendingRelation> fromDb(
    DbContext context,
    Map<String, dynamic> map,
  ) async {
    final fields = context.fields!.map;
    return SyncPendingRelation(
      id: fields[SyncPendingRelationField.id]?.value(map),
      remoteId: fields[SyncPendingRelationField.remoteId]?.value(map),
      collectionType:
          fields[SyncPendingRelationField.collectionType]?.value(map),
      parentRemoteId:
          fields[SyncPendingRelationField.parentRemoteId]?.value(map),
      childLocalId: fields[SyncPendingRelationField.childLocalId]?.value(map),
      pendingData: fields[SyncPendingRelationField.pendingData]?.value(map),
      createdAt: fields[SyncPendingRelationField.createdAt]?.value(map),
      organization: await fields[SyncPendingRelationField.organization]
          ?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
    DbContext context,
    SyncPendingRelation model,
  ) async {
    final map = <String, dynamic>{};
    const contract = SyncPendingRelationDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.collectionType, model.collectionType);
    addField(map, contract.parentRemoteId, model.parentRemoteId);
    addField(map, contract.childLocalId, model.childLocalId);
    addField(map, contract.pendingData, model.pendingData);
    addField(map, contract.createdAt, model.createdAt);
    await saveNestedModel<Organization>(
      context,
      map,
      contract.organizationId,
      context.db.organization,
      model.organization,
    );

    return map;
  }
}
