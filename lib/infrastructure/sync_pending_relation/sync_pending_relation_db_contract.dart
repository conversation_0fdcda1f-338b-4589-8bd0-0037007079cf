import 'dart:async';
import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class SyncPendingRelationDbContract extends DbContract {
  static const String _ = 'spr_';
  static const String _tableName = 'syncPendingRelation';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String collectionType = '${_}collectionType';
  final String parentRemoteId = '${_}parentRemoteId';
  final String childLocalId = '${_}childLocalId';
  final String pendingData = '${_}pendingData';
  final String organizationId = '${_}organizationId';
  final String createdAt = '${_}createdAt';

  const SyncPendingRelationDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithSyncPendingRelationTable;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $collectionType INTEGER NOT NULL,
    $parentRemoteId INTEGER NOT NULL,
    $childLocalId INTEGER NOT NULL,
    $pendingData TEXT,
    $organizationId INTEGER NOT NULL,
    $createdAt INTEGER NOT NULL
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithSyncPendingRelationTable) {
      await db.execute(create);
    }
  }
}
