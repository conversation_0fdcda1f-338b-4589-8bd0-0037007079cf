import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/resource/resource_db_contract.dart';

class ResourceDbTranslator implements DbTranslator<Resource> {
  const ResourceDbTranslator();

  @override
  Set<Field> get nestedModelFields => resourceNestedModelFields;

  @override
  Future<Resource> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Resource(
      id: fields[ResourceField.id]?.value(map),
      remoteId: fields[ResourceField.remoteId]?.value(map),
      name: fields[ResourceField.name]?.value(map),
      type: fields[ResourceField.type]?.value(map),
      metadata: fields[ResourceField.metadata]?.value(map),
      s3Key: fields[ResourceField.s3Key]?.value(map),
      isDownloaded: fields[ResourceField.isDownloaded]?.value(map),
      transferState: fields[ResourceField.transferState]?.value(map),
      path: fields[ResourceField.path]?.value(map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Resource model) async {
    final map = <String, dynamic>{};
    const contract = ResourceDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.name, model.name);
    addField(map, contract.type, model.type);
    addField(map, contract.metadata, model.metadata);
    addField(map, contract.s3Key, model.s3Key);
    addField(map, contract.isDownloaded, model.isDownloaded);
    addField(map, contract.transferState, model.transferState);
    addField(map, contract.path, model.path);
    return map;
  }
}
