import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_source/entry_source.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_source/entry_source_db_contract.dart';

class EntrySourceDbTranslator implements DbTranslator<EntrySource> {
  const EntrySourceDbTranslator();

  @override
  Set<Field> get nestedModelFields => entrySourceNestedModelFields;

  @override
  Future<EntrySource> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return EntrySource(
        id: fields[EntrySourceField.id]?.value(map),
        type: fields[EntrySourceField.type]?.value(map),
        metadata: fields[EntrySourceField.metadata]?.value(map));
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, EntrySource model) async {
    final map = <String, dynamic>{};
    const contract = EntrySourceDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.type, model.type);
    addField(map, contract.metadata, model.metadata);

    await saveNestedModel<Entry>(
        context, map, contract.entryId, context.db.entry, model.entry);

    return map;
  }
}
