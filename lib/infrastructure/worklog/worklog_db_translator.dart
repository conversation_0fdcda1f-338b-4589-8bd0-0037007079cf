import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/worklog/worklog_db_contract.dart';

class WorklogDbTranslator implements DbTranslator<Worklog> {
  const WorklogDbTranslator();

  @override
  Set<Field> get nestedModelFields => worklogNestedModelFields;

  @override
  Future<Worklog> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Worklog(
      id: fields[WorklogField.id]?.value(map),
      remoteId: fields[WorklogField.remoteId]?.value(map),
      sublocation: fields[WorklogField.sublocation]?.value(map),
      type: fields[WorklogField.type]?.value(map),
      quantity: fields[WorklogField.quantity]?.value(map),
      title: fields[WorklogField.title]?.value(map),
      costPrice: fields[WorklogField.costPrice]?.value(map),
      salePrice: fields[WorklogField.salePrice]?.value(map),
      provider: fields[WorklogField.provider]?.value(map),
      paymentStatus: fields[WorklogField.paymentStatus]?.value(map),
      priceIsUnit: fields[WorklogField.priceIsUnit]?.value(map),
      project: await fields[WorklogField.project]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Worklog model) async {
    final map = <String, dynamic>{};
    const contract = WorklogDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.sublocation, model.sublocation);
    addField(map, contract.type, model.type);
    addField(map, contract.quantity, model.quantity);
    addField(map, contract.title, model.title);
    addField(map, contract.costPrice, model.costPrice);
    addField(map, contract.salePrice, model.salePrice);
    addField(map, contract.provider, model.provider);
    addField(map, contract.paymentStatus, model.paymentStatus);
    addField(map, contract.priceIsUnit, model.priceIsUnit);

    await saveNestedModel<Project>(
        context, map, contract.projectId, context.db.project, model.project);
    return map;
  }
}
