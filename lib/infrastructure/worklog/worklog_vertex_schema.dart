import 'package:firebase_vertexai/firebase_vertexai.dart';

final worklogVertexSchema = Schema.object(properties: {
  'title': Schema.string(nullable: false),
  'quantity': Schema.number(nullable: true),
  'sublocation': Schema.string(nullable: false),
  "cost_price": Schema.number(nullable: true),
  "sale_price": Schema.number(nullable: true),
  "payment_status": Schema.integer(nullable: true),
  "price_is_unit": Schema.integer(nullable: true),
  "provider": Schema.string(nullable: true),
  'worklog_type': Schema.integer(nullable: false),
  'project_id': Schema.integer(nullable: true),
  'project_name': Schema.string(nullable: false),
});
