import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/query/organization_common_repository_queries.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/project/project_repository.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_db_contract.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_fields_builder.dart';
import 'package:bitacora/infrastructure/project/project_db_translator.dart';
import 'package:bitacora/infrastructure/projectentriesproject/project_entries_project_db_contract.dart';
import 'package:bitacora/infrastructure/worklog/worklog_db_contract.dart';
import 'package:bitacora/util/clock.dart';
import 'package:sqflite/sqflite.dart';

class ProjectDbTable extends DbTable<Project, ProjectDbFieldsBuilder>
    implements ProjectRepository<DbContext, ProjectDbFieldsBuilder> {
  ProjectDbTable() : super() {
    addIdResolver(_idResolverByProjectName);
  }

  @override
  ProjectDbContract get contract => const ProjectDbContract();

  Future<LocalId?> _getLocalOrgId(
      DbContext context, Organization organization) async {
    if (organization.id != null) {
      return organization.id!;
    }

    final orgWithId = await context.db.query(
      OrganizationIdRepositoryQuery(remoteId: organization.remoteId!),
      context: context,
    );
    return orgWithId?.id;
  }

  Future<LocalId?> _idResolverByProjectName(
      DbContext context, Project model) async {
    if (model.name == null) {
      return null;
    }

    final orgId = await _getLocalOrgId(context, model.organization!);

    // This id resolver should only be used if the defaults fail (id, remoteId)
    // In which case organization id is required for disambiguation.
    final result = await (await context.executor).query(tableName,
        columns: [idColumn],
        where: '${contract.name} = ? AND ${contract.organizationId} = ?',
        whereArgs: [model.name!.dbValue, orgId!.dbValue],
        limit: 1);
    return result.isEmpty ? null : LocalId(result[0][idColumn] as int);
  }

  EntryDbContract get entryContract => const EntryDbContract();

  InventorylogDbContract get inventorylogContract =>
      const InventorylogDbContract();

  PersonnellogDbContract get personnellogContract =>
      const PersonnellogDbContract();

  WorklogDbContract get worklogContract => const WorklogDbContract();

  ProjectEntriesProjectDbContract get projectEntriesProjectContract =>
      const ProjectEntriesProjectDbContract();

  @override
  Future<List<Project>> findSyncables(
    DbContext context,
    LocalId orgId, {
    ProjectListOrder order = ProjectListOrder.lexicographic,
    bool withRemoteId = false,
  }) {
    return query(
      context,
      where: '${contract.organizationId} = ?'
          ' AND ${contract.isSyncable} = ?'
          '${withRemoteId ? ' AND ${contract.remoteId} IS NOT NULL' : ''}',
      whereArgs: [orgId.dbValue, 1],
      orderBy: _getOrderBy(order),
    );
  }

  @override
  Future<List<Project>> findWithAccess(
    DbContext context,
    LocalId orgId, {
    ProjectListOrder order = ProjectListOrder.lexicographic,
    bool withRemoteId = false,
  }) async {
    final queryScope = context.queryScope!;
    const projectEntriesProject = ProjectEntriesProjectDbContract();
    const entry = EntryDbContract();
    const access = AccessEntryDbContract();

    final projects = await rawQuery(
      context,
      '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM $tableName
      LEFT JOIN ${projectEntriesProject.tableName}
        ON ${contract.id} = ${projectEntriesProject.projectId}
      LEFT JOIN ${entry.tableName} 
        ON ${projectEntriesProject.entryId} = ${entry.id}
      LEFT JOIN ${access.tableName}
        ON ${entry.id} = ${access.entryId}
      WHERE ${contract.organizationId} = ? 
        AND ${contract.isSyncable} = 1 
        ${withRemoteId ? ' AND ${contract.remoteId} IS NOT NULL' : ''}
        AND (${access.permission} & $kAccessRead = $kAccessRead
          OR (
            ${access.permission} & $kAccessReadOwn = $kAccessReadOwn 
            AND (
              ${entry.authorId} = ${queryScope.userId!.dbValue}
              OR ${entry.assigneeId} = ${queryScope.userId!.dbValue}
            ) 
          )
        )
      GROUP BY ${contract.id} 
      ORDER BY ${_getOrderBy(order)}
      ''',
      [orgId.dbValue],
    );

    return projects;
  }

  @override
  Future<List<Project>> findByOrganization(
      DbContext context, LocalId orgId) async {
    return query(
      context,
      where: '${contract.organizationId} = ?',
      whereArgs: [orgId.dbValue],
    );
  }

  @override
  Future<Project?> findByName(DbContext context, ProjectName name) {
    return single(query(
      context,
      where: '${contract.organizationId} = ?'
          ' AND ${contract.isSyncable} = ?'
          ' AND ${contract.name} = ?',
      whereArgs: [context.queryScope!.orgId!.dbValue, 1, name.dbValue],
    ));
  }

  @override
  DbTranslator<Project> get translator => const ProjectDbTranslator();

  @override
  ProjectDbFieldsBuilder get fieldsBuilder => ProjectDbFieldsBuilder();

  @override
  List<String> get searchColumns => [contract.name];

  @override
  Future<List<String>> names(DbContext context) async {
    final executor = await context.executor;
    final results = await executor.rawQuery(
      '''
        SELECT ${contract.name}, MAX(${entryContract.updatedAt})
        ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
        WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
        AND ${contract.name} LIKE ?
        AND ${contract.name} != ?
        GROUP BY ${contract.name}
        ORDER BY MAX(${entryContract.updatedAt}) DESC, ${contract.name} ASC
        LIMIT ${context.cursor?.limit ?? 10}
        OFFSET ${context.cursor?.offset ?? 0}
      ''',
      [
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        context.queryScope!.pattern!,
        kDefaultProjectName,
      ],
    );

    return results.map<String>((e) => e[contract.name] as String).toList();
  }

  @override
  Future<List<Project>> searchAll(DbContext context) async {
    query(DbContext c) async {
      final executor = await c.executor;
      final filterOut =
          DbQueryScopeUtils().filterOutCondition(contract.id, c.queryScope!);

      final results = await executor.rawQuery(
        '''
            SELECT ${columnsForSelect(c.fields!)},
            MAX(${entryContract.updatedAt})
            ${DbQueryScopeUtils().fromJoin(c.queryScope!)}
            WHERE ${DbQueryScopeUtils().where(c.queryScope!)}
            AND ${contract.name} LIKE ?
            AND ${contract.remoteId} IS NOT NULL
            ${filterOut != null ? 'AND $filterOut' : ''}
            GROUP BY ${contract.name}
            ORDER BY MAX(${entryContract.updatedAt}) DESC, ${contract.name} ASC
            LIMIT ${c.cursor?.limit ?? 10}
            OFFSET ${c.cursor?.offset ?? 0}
          ''',
        [
          ...DbQueryScopeUtils().queryArg(c.queryScope!),
          c.queryScope!.pattern!,
          if (filterOut != null) ...c.queryScope!.filterOut!,
        ],
      );
      return Future.wait(results.map((e) => fromDb(c, e)));
    }

    if (context.hasAnyOfFields(translator.nestedModelFields)) {
      return wrapTransaction(context, (c) => query(c));
    }
    return query(context);
  }

  @override
  Future<List<String>> sublocations(DbContext context) async {
    final filterOut =
        DbQueryScopeUtils().filterOutCondition('x', context.queryScope!);

    final executor = await context.executor;
    final results = await executor.rawQuery(
      '''
        SELECT x, MAX(${entryContract.updatedAt}) FROM (
          SELECT ${worklogContract.sublocation} as x, ${entryContract.updatedAt}
          ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
          LEFT JOIN ${worklogContract.tableName} 
          ON (${worklogContract.id} = ${entryContract.extensionId} 
            AND ${entryContract.extensionType} = ?)
          WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
          AND x LIKE ?
          UNION
          SELECT ${personnellogContract.sublocation}
            as x, ${entryContract.updatedAt}
          ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
          LEFT JOIN ${personnellogContract.tableName}
          ON (${personnellogContract.id} = ${entryContract.extensionId} 
            AND ${entryContract.extensionType} = ?)
          WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
          AND x LIKE ?
          UNION
          SELECT ${inventorylogContract.sourceSublocation} as x, 
          ${entryContract.updatedAt}
          ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
          LEFT JOIN ${inventorylogContract.tableName}
          ON (${inventorylogContract.id} = ${entryContract.extensionId} 
            AND ${entryContract.extensionType} = ?)
          WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
          AND x LIKE ?
          UNION
          SELECT ${inventorylogContract.destSublocation} as x,
          ${entryContract.updatedAt}
          ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
          LEFT JOIN ${inventorylogContract.tableName}
          ON (${inventorylogContract.id} = ${entryContract.extensionId} 
            AND ${entryContract.extensionType} = ?)
          WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
          AND x LIKE ?
        )
        ${filterOut != null ? 'WHERE $filterOut' : ''}
        GROUP BY x
        ORDER BY MAX(${entryContract.updatedAt}) DESC, x ASC
        LIMIT ${context.cursor?.limit ?? 10}
        OFFSET ${context.cursor?.offset ?? 0}
      ''',
      [
        ExtensionType.worklog.dbValue,
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        context.queryScope!.pattern!,
        ExtensionType.personnellog.dbValue,
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        context.queryScope!.pattern!,
        ExtensionType.inventorylog.dbValue,
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        context.queryScope!.pattern!,
        ExtensionType.inventorylog.dbValue,
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        context.queryScope!.pattern!,
        if (filterOut != null) ...context.queryScope!.filterOut!,
      ],
    );

    return results.map<String>((e) => e['x'] as String).toList();
  }

  @override
  Future<List<String>> searchProviders(DbContext context) async {
    // FIXME: very similar to sublocations. Generalize for extension suggestions
    final filterOut =
        DbQueryScopeUtils().filterOutCondition('x', context.queryScope!);

    final executor = await context.executor;
    final results = await executor.rawQuery(
      '''
        SELECT x, MAX(${entryContract.updatedAt}) FROM (
          SELECT ${worklogContract.provider} as x, ${entryContract.updatedAt}
          ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
          LEFT JOIN ${worklogContract.tableName} 
          ON (${worklogContract.id} = ${entryContract.extensionId}
            AND ${entryContract.extensionType} = ?)
          WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
          AND x LIKE ?
          UNION
          SELECT ${inventorylogContract.provider} as x,
          ${entryContract.updatedAt}
          ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
          LEFT JOIN ${inventorylogContract.tableName}
          ON (${inventorylogContract.id} = ${entryContract.extensionId}
            AND ${entryContract.extensionType} = ?)
          WHERE ${DbQueryScopeUtils().where(context.queryScope!)}
          AND x LIKE ?
        )
        ${filterOut != null ? 'WHERE $filterOut' : ''}
        GROUP BY x
        ORDER BY MAX(${entryContract.updatedAt}) DESC, x ASC
        LIMIT ${context.cursor?.limit ?? 10}
        OFFSET ${context.cursor?.offset ?? 0}
      ''',
      [
        ExtensionType.worklog.dbValue,
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        context.queryScope!.pattern!,
        ExtensionType.inventorylog.dbValue,
        ...DbQueryScopeUtils().queryArg(context.queryScope!),
        context.queryScope!.pattern!,
        if (filterOut != null) ...context.queryScope!.filterOut!,
      ],
    );

    return results.map<String>((e) => e['x'] as String).toList();
  }

  @override
  Future<bool> hasEntries(DbContext context, LocalId projectId) async {
    return (await _getEntries(context, projectId)).isNotEmpty;
  }

  @override
  Future<void> clearRemoteIdFromEntries(
      DbContext context, LocalId projectId) async {
    final entryIds = await _getEntries(context, projectId);
    for (final id in entryIds) {
      await context.db.entry.save(
        context,
        Entry(
          id: id,
          remoteId: const RemoteId(null),
          createdAt: EntryCreatedAt(Clock().now()),
        ),
      );
    }
  }

  Future<Iterable<LocalId>> _getEntries(
      DbContext context, LocalId projectId) async {
    final executor = await context.executor;
    final results = await executor.query(
      projectEntriesProjectContract.tableName,
      columns: [projectEntriesProjectContract.entryId],
      where: '${projectEntriesProjectContract.projectId} = ?',
      whereArgs: [projectId.dbValue],
    );
    return results
        .map((e) => LocalId(e[projectEntriesProjectContract.entryId] as int));
  }

  @override
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    await super.onPreDelete(context, id);

    final executor = await (context.executor);
    await executor.delete(
      projectEntriesProjectContract.tableName,
      where: '${projectEntriesProjectContract.projectId} = ?',
      whereArgs: [id.dbValue],
    );
  }

  String _getOrderBy(ProjectListOrder order) {
    switch (order) {
      case ProjectListOrder.lexicographic:
        return contract.name;
      case ProjectListOrder.updatedAt:
        return '${contract.syncLastEntryUpdatedAt} DESC';
    }
  }

  @override
  Future<LocalId?> saveEntryRelation(
      DbContext context, Project project, Entry entry) async {
    final executor = await context.executor;

    final id = await executor.insert(
      projectEntriesProjectContract.tableName,
      {
        projectEntriesProjectContract.projectId: project.id!.dbValue,
        projectEntriesProjectContract.entryId: entry.id!.dbValue,
      },
      conflictAlgorithm: ConflictAlgorithm.ignore,
    );

    return LocalId(id);
  }
}
