import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class ProjectDbContract extends DbContract {
  static const String _ = 'p_';
  static const String _tableName = 'project';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String organizationId = '${_}organizationId';
  final String name = '${_}name';
  final String description = '${_}description';
  final String address = '${_}address';
  final String locLongitude = '${_}loc_longitude';
  final String locLatitude = '${_}loc_latitude';
  final String type = '${_}type';
  final String isSyncable = '${_}isSyncable';
  final String syncLastEntryUpdatedAt = '${_}syncLastEntryUpdatedAt';
  final String syncLastSyncTime = '${_}syncLastSyncTime';
  final String syncNextPageToken = '${_}syncNextPageToken';

  const ProjectDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersion;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $organizationId INTEGER NOT NULL,
    $name TEXT NOT NULL,
    $description TEXT,
    $address TEXT,
    $locLongitude REAL,
    $locLatitude REAL,
    $type TEXT,
    $isSyncable INTEGER,
    $syncLastEntryUpdatedAt INTEGER,
    $syncLastSyncTime INTEGER,
    $syncNextPageToken TEXT
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithProjectExtraFields) {
      await db.execute('ALTER TABLE $tableName ADD $description TEXT');
      await db.execute('ALTER TABLE $tableName ADD $address TEXT');
      await db.execute('ALTER TABLE $tableName ADD $locLongitude REAL');
      await db.execute('ALTER TABLE $tableName ADD $locLatitude REAL');
      await db.execute('ALTER TABLE $tableName ADD $type TEXT');
    }
  }
}
