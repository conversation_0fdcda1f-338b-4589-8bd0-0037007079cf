import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/templatelog/templatelog_fields_builder.dart';
import 'package:bitacora/domain/templatelog/value/templatelog_template_group_name.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_contract.dart';

class TemplatelogDbFieldsBuilder extends DbFieldsBuilder
    implements TemplatelogFieldsBuilder {
  TemplatelogDbFieldsBuilder() {
    _id();
  }

  TemplatelogDbContract get contract => const TemplatelogDbContract();

  TemplatelogDbFieldsBuilder _id() {
    addField(
      TemplatelogField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  TemplatelogDbFieldsBuilder remoteId() {
    addField(
      TemplatelogField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  TemplatelogDbFieldsBuilder templateName() {
    addField(
      TemplatelogField.templateName,
      DbField(
        column: contract.templateName,
        valueBuilder: (v) => TemplatelogTemplateName(v),
      ),
    );
    return this;
  }

  @override
  TemplatelogDbFieldsBuilder fieldsMetadata(Fields fields) {
    addField(
      TemplatelogField.fieldsMetadata,
      DbField(
        key: TemplatelogField.fieldsMetadata,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext
            .db.customFieldMetadata
            .findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  TemplatelogFieldsBuilder template(Fields fields) {
    addField(
      TemplatelogField.template,
      DbField(
        key: TemplatelogField.template,
        column: contract.templateId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.template.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  TemplatelogFieldsBuilder defaultProject(Fields fields) {
    addField(
      TemplatelogField.defaultProject,
      DbField(
        key: TemplatelogField.defaultProject,
        column: contract.defaultProjectId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) {
          return nestedContext.db.project.find(nestedContext, LocalId(value));
        },
      ),
    );
    return this;
  }
}
