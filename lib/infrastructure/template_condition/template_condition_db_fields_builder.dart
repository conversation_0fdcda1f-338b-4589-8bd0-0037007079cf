import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/template_condition/template_condition.dart';
import 'package:bitacora/domain/template_condition/template_condition_fields_builder.dart';
import 'package:bitacora/domain/template_condition/value/template_condition_operator.dart';
import 'package:bitacora/domain/template_condition/value/template_condition_value.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/template_condition/template_condition_db_contract.dart';

class TemplateConditionDbFieldsBuilder extends DbFieldsBuilder
    implements TemplateConditionFieldsBuilder {
  TemplateConditionDbFieldsBuilder() {
    _id();
  }

  TemplateConditionDbContract get contract =>
      const TemplateConditionDbContract();

  TemplateConditionFieldsBuilder _id() {
    addField(
      TemplateConditionField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  TemplateConditionFieldsBuilder value() {
    addField(
      TemplateConditionField.value,
      DbField(
        column: contract.value,
        valueBuilder: (v) => TemplateConditionValue(v),
      ),
    );
    return this;
  }

  @override
  TemplateConditionFieldsBuilder operator() {
    addField(
      TemplateConditionField.operator,
      DbField(
        column: contract.operator,
        valueBuilder: (v) => TemplateConditionOperator.fromDb(v),
      ),
    );
    return this;
  }

  @override
  TemplateConditionFieldsBuilder whenBlock(Fields fields) {
    addField(
      TemplateConditionField.whenBlock,
      DbField(
        key: TemplateConditionField.whenBlock,
        column: contract.whenBlockId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.templateBlock.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  TemplateConditionFieldsBuilder templateBlock(Fields fields) {
    addField(
      TemplateConditionField.templateBlock,
      DbField(
        key: TemplateConditionField.templateBlock,
        column: contract.templateBlockId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.templateBlock.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  TemplateConditionFieldsBuilder templateGroup(Fields fields) {
    addField(
      TemplateConditionField.templateGroup,
      DbField(
        key: TemplateConditionField.templateGroup,
        column: contract.templateGroupId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.templateGroup.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
