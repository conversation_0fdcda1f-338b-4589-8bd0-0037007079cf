import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/feed_post/feed_post.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/feed_post/feed_post_db_contract.dart';

class FeedPostDbTranslator implements DbTranslator<FeedPost> {
  const FeedPostDbTranslator();

  @override
  Set<Field> get nestedModelFields => feedPostNestedModelFields;

  @override
  Future<FeedPost> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return FeedPost(
      id: fields[FeedPostField.id]?.value(map),
      remoteId: fields[FeedPostField.remoteId]?.value(map),
      title: fields[FeedPostField.title]?.value(map),
      content: fields[FeedPostField.content]?.value(map),
      pinned: fields[FeedPostField.pinned]?.value(map),
      createdAt: fields[FeedPostField.createdAt]?.value(map),
      updatedAt: fields[FeedPostField.updatedAt]?.value(map),
      readAt: fields[FeedPostField.readAt]?.value(map),
      author: await fields[FeedPostField.author]?.nested(context, map),
      organization:
          await fields[FeedPostField.organization]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, FeedPost model) async {
    final map = <String, dynamic>{};
    const contract = FeedPostDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.title, model.title);
    addField(map, contract.content, model.content);
    addField(map, contract.pinned, model.pinned);
    addField(map, contract.createdAt, model.createdAt);
    addField(map, contract.updatedAt, model.updatedAt);
    addField(map, contract.readAt, model.readAt);

    await saveNestedModel<User>(
        context, map, contract.authorId, context.db.user, model.author);
    await saveNestedModel<Organization>(context, map, contract.organizationId,
        context.db.organization, model.organization);

    return map;
  }
}
