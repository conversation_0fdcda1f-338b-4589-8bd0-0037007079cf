import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_created_at.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/util/date_utils.dart';

class LocationTrackingApiTranslator
    implements ModelTranslator<LocationTracking> {
  const LocationTrackingApiTranslator();

  @override
  LocationTracking fromMap(Map<String, dynamic> data) {
    final apiTranslator = ApiTranslator();
    return LocationTracking(
      remoteId: data['id'] == null ? null : RemoteId(data['id']),
      uuid: data['identifier'] == null
          ? null
          : LocationTrackingUuid(data['identifier']),
      status: data['status'] == null
          ? null
          : LocationTrackingStatus.fromApiValue(data['status']),
      createdAt: data['created_at'] == null
          ? null
          : LocationTrackingCreatedAt(
              getDateTimeFromApi(data['created_at']).toLocal()),
      points: data['points'] is List
          ? data['points']
              .map<LocationPoint>((e) => apiTranslator.locationPoint.fromMap(e))
              .toList(growable: false)
          : [],
    );
  }

  @override
  Map<String, dynamic> toMap(LocationTracking model) => {
        'identifier': model.uuid!.value,
        'status':
            model.status?.apiValue ?? LocationTrackingStatus.idle.apiValue,
        // FIXME: Remove this. Should be handled by server.
        'created_at': model.createdAt?.value.toUtc().toIso8601String() ??
            DateTime.now().toUtc().toIso8601String(),
      };
}
