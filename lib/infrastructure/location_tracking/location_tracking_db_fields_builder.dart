import 'package:bitacora/application/sync/machine/steps/upload/location_tracking/entry_by_location_tracking_id_repository_query.dart';
import 'package:bitacora/domain/common/query/entry_common_db_queries.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/location_tracking/location_tracking_fields_builder.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_created_at.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_owner_type.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/location_tracking/location_tracking_db_contract.dart';
import 'package:bitacora/infrastructure/user_location_tracking/user_location_tracking_db_contract.dart';

class LocationTrackingDbFieldsBuilder extends DbFieldsBuilder
    implements LocationTrackingFieldsBuilder {
  LocationTrackingDbFieldsBuilder() {
    _id();
  }

  LocationTrackingDbContract get contract => const LocationTrackingDbContract();

  LocationTrackingDbFieldsBuilder _id() {
    addField(
      LocationTrackingField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  LocationTrackingDbFieldsBuilder remoteId() {
    addField(
      LocationTrackingField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  LocationTrackingDbFieldsBuilder status() {
    addField(
      LocationTrackingField.status,
      DbField(
        column: contract.status,
        valueBuilder: (v) => LocationTrackingStatus.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  LocationTrackingDbFieldsBuilder uuid() {
    addField(
      LocationTrackingField.uuid,
      DbField(
        column: contract.uuid,
        valueBuilder: (v) => LocationTrackingUuid(v),
      ),
    );
    return this;
  }

  @override
  LocationTrackingDbFieldsBuilder isLocal() {
    addField(
      LocationTrackingField.isLocal,
      DbField(
        column: contract.isLocal,
        valueBuilder: (v) => LocationTrackingIsLocal(v == 1),
      ),
    );
    return this;
  }

  @override
  LocationTrackingFieldsBuilder ownerType() {
    addField(
      LocationTrackingField.ownerType,
      DbField(
        column: contract.ownerType,
        valueBuilder: (v) => LocationTrackingOwnerType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  LocationTrackingDbFieldsBuilder createdAt() {
    addField(
      LocationTrackingField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) => v == null
            ? null
            : LocationTrackingCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  LocationTrackingFieldsBuilder points(Fields fields) {
    addField(
      LocationTrackingField.points,
      DbField(
        key: LocationTrackingField.points,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.locationPoint
            .findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  LocationTrackingFieldsBuilder entry(Fields fields) {
    addField(
      LocationTrackingField.entry,
      DbField(
        key: LocationTrackingField.entry,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.entry
            .findByLocationTrackingId(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  LocationTrackingFieldsBuilder organization(Fields fields) {
    addField(
      LocationTrackingField.organization,
      DbField(
        key: LocationTrackingField.organization,
        columnAdder: (list) {
          list.add(contract.id);
          list.add(contract.ownerType);
        },
        nestedFields: fields as DbFields,
        multiNestedBuilder: (context, values, _) async {
          final id = LocalId(values[contract.id]);
          final ownerType =
              LocationTrackingOwnerType.fromDbValue(values[contract.ownerType]);

          final orgId = ownerType == LocationTrackingOwnerType.entry
              ? await _getOrgIdFromEntryLocationTracking(context, id)
              : await _getOrgIdFromUserLocationTracking(context, id);

          final nestedContext =
              context.nested(LocationTrackingField.organization)!;
          return nestedContext.db.organization.find(nestedContext, orgId);
        },
      ),
    );
    return this;
  }

  Future<LocalId> _getOrgIdFromEntryLocationTracking(
    DbContext context,
    LocalId id,
  ) async {
    final entry = await context.db.query(
      EntryByLocationTrackingIdRepositoryQuery(locationTrackingId: id),
      context: context,
    );

    return context.db.query(
      OrganizationIdFromEntryRepositoryQuery(entryId: entry!.id!),
      context: context,
    );
  }

  Future<LocalId> _getOrgIdFromUserLocationTracking(
    DbContext context,
    LocalId id,
  ) async {
    const userLocationDbContract = UserLocationTrackingDbContract();
    final executor = await context.db.executor(context);

    final result = await executor.rawQuery(
      '''
        SELECT ${userLocationDbContract.organizationId} 
        FROM ${userLocationDbContract.tableName}
        WHERE ${userLocationDbContract.locationTrackingId} = ?
      ''',
      [id.dbValue],
    );

    return LocalId(result.first[userLocationDbContract.organizationId] as int);
  }
}
