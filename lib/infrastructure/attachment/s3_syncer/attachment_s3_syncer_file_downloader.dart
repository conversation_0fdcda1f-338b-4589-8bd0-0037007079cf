import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/amplify/amplify_storage_util.dart';
import 'package:bitacora/infrastructure/attachment/attachment_sync_repository_query.dart';
import 'package:bitacora/infrastructure/attachment/s3_syncer/attachment_s3_syncer.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:path/path.dart' as path;

class AttachmentS3SyncerFileDownloader {
  final Repository db;
  final SaveTransferStateCallback saveTransferState;

  AttachmentS3SyncerFileDownloader({
    required this.db,
    required this.saveTransferState,
  });

  Future<void> maybeDownload(Attachment inputAttachment) async {
    final attachment = await db.query(
      AttachmentSyncRepositoryQuery(id: inputAttachment.id!),
    );
    if (attachment == null) {
      logger.f('attachment:download Attachment is gone.');
      return;
    }

    logger.i('attachment:download ${attachment.s3Key!.value}');

    if (attachment.isDownloaded!.value ||
        attachment.transferState!.value == AttachmentTransferState.inProgress) {
      /// Allow retrying failed transfers, since this is triggered from an event
      /// that shouldn't feed back (unlike upload).
      return;
    }

    final attachmentStorageUtils = AttachmentUtils();
    final storageDirectory = (await attachmentStorageUtils.getDirectory()).path;
    final filename = StorageUtils()
        .maybeRenameUntilNew(storageDirectory, attachment.name!.value);
    final s3Key = attachment.s3Key!;

    await saveTransferState(
      s3Key,
      AttachmentTransferState.inProgress,
      path: AttachmentPath(filename),
    );

    try {
      await AmplifyStorageUtil().downloadFile(
        path: path.join(storageDirectory, filename),
        key: attachment.s3Key!.value,
      );
      logger.i('attachment:download done');
      await saveTransferState(s3Key, AttachmentTransferState.done);
    } catch (error) {
      logger.f('attachment:download failed $error');
      await saveTransferState(s3Key, AttachmentTransferState.failed);
      rethrow;
    }
  }
}
