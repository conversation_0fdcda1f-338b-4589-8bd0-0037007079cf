import 'dart:async';

import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/attachment/attachment_sync_repository_query.dart';
import 'package:bitacora/infrastructure/attachment/pending_attachment_upload_repository_query.dart';
import 'package:bitacora/infrastructure/attachment/s3_syncer/attachment_s3_syncer_file_downloader.dart';
import 'package:bitacora/infrastructure/attachment/s3_syncer/attachment_s3_syncer_file_uploader.dart';
import 'package:bitacora/util/connectivity/connectivity.dart';
import 'package:bitacora/util/duration_tracker.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';

typedef SaveTransferStateCallback = Function(
  AttachmentS3Key s3key,
  AttachmentTransferState transferState, {
  AttachmentPath? path,
  AttachmentIsDownloaded? isDownloaded,
  RepositoryQueryContext? context,
});

class AttachmentS3SyncerInjector {
  factory AttachmentS3SyncerInjector() =>
      inject(() => const AttachmentS3SyncerInjector._());

  const AttachmentS3SyncerInjector._();

  AttachmentS3Syncer get({
    required Repository db,
    required SyncTrigger syncTrigger,
    required SyncState syncState,
    required AnalyticsLogger analyticsLogger,
  }) =>
      inject(() => AttachmentS3Syncer._(
            db: db,
            syncTrigger: syncTrigger,
            syncState: syncState,
            analyticsLogger: analyticsLogger,
          ));
}

class AttachmentS3Syncer {
  static const Set<SyncTriggerSource> _triggers = <SyncTriggerSource>{
    SyncTriggerSource.session,
    SyncTriggerSource.connectivity,
    SyncTriggerSource.user,
    SyncTriggerSource.background,
    SyncTriggerSource.db,
    SyncTriggerSource.notification,
  };

  final Repository db;
  final SyncTrigger syncTrigger;
  final SyncState syncState;
  final AnalyticsLogger analyticsLogger;

  late final AttachmentS3SyncerFileUploader _uploader;
  late final AttachmentS3SyncerFileDownloader _downloader;
  late final StreamSubscription<Attachment> _downloadRequestsSubscription;
  late final StreamSubscription<SyncTriggerEvent> _syncTriggerSubscription;

  int _busyTasksCount = 0;
  bool _isUploadSyncLoopRunning = false;
  bool _ignoreRetryLimit = false;

  AttachmentS3Syncer._({
    required this.db,
    required this.syncTrigger,
    required this.syncState,
    required this.analyticsLogger,
  }) {
    _uploader = AttachmentS3SyncerFileUploader(
      db: db,
      analyticsLogger: analyticsLogger,
      saveTransferState: _saveTransferState,
    );
    _downloader = AttachmentS3SyncerFileDownloader(
      db: db,
      saveTransferState: _saveTransferState,
    );

    _downloadRequestsSubscription =
        db.attachment.downloadRequests.listen((event) {
      _wrapBusyTask(() => _downloader.maybeDownload(event));
    });
    _syncTriggerSubscription = syncTrigger.stream().listen(
      (event) {
        if (_triggers.contains(event.source)) {
          if (event.mode == SyncTriggerMode.superSync) {
            _ignoreRetryLimit = true;
          }
          _wrapBusyTask(_runUploadSyncLoop);
        }
      },
    );

    // FIXME: reset inProgress -> fail
  }

  void dispose() {
    _downloadRequestsSubscription.cancel();
    _syncTriggerSubscription.cancel();
  }

  Future<void> _wrapBusyTask(Future<void> Function() f) async {
    if (++_busyTasksCount == 1) {
      syncState.isS3SyncBusy = true;
    }

    try {
      await f();
    } catch (_) {}

    if (--_busyTasksCount == 0) {
      syncState.isS3SyncBusy = false;
    }
  }

  Future<void> _runUploadSyncLoop() async {
    if (_isUploadSyncLoopRunning) {
      return;
    }

    final tracker = DurationTracker();
    tracker.markStart();
    logger.i('attachment:upload:sync Loop Starting');

    _isUploadSyncLoopRunning = true;
    try {
      final failedUploads = <LocalId>[];
      while (true) {
        if (!Connectivity().hasConnectivity()) {
          break;
        }

        final nextAttachment = await db.query<Attachment?>(
          PendingAttachmentUploadRepositoryQuery(
            ignoreRetryLimit: _ignoreRetryLimit,
          ),
          context: db.context(queryScope: QueryScope(filterOut: failedUploads)),
        );
        if (nextAttachment == null) {
          logger.i('attachment:upload:sync$tracker no attachment found');
          break;
        }
        logger.i('attachment:upload:sync$tracker found attachment '
            '${nextAttachment.id}');

        await _uploader.upload(
          await _getAttachmentForUpload(nextAttachment.id!),
          failedUploads,
        );
      }
    } catch (_) {}

    logger.i('attachment:upload:sync$tracker Loop Ended');
    _isUploadSyncLoopRunning = false;
    _ignoreRetryLimit = false;
  }

  Future<Attachment> _getAttachmentForUpload(LocalId id) async {
    final attachment =
        await db.query<Attachment?>(AttachmentSyncRepositoryQuery(id: id));
    return attachment!;
  }

  Future<void> _saveTransferState(
    AttachmentS3Key s3key,
    AttachmentTransferState transferState, {
    AttachmentPath? path,
    AttachmentIsDownloaded? isDownloaded,
    RepositoryQueryContext? context,
  }) {
    return db.attachment.saveTransferDetails(
      context ?? db.context(),
      Attachment(
        s3Key: s3key,
        transferState: AttachmentTransferStateValueObject(transferState),
        path: path,
        isUploaded: transferState == AttachmentTransferState.done
            ? AttachmentIsUploaded(true)
            : null,
        isDownloaded: isDownloaded ??
            (transferState == AttachmentTransferState.done
                ? AttachmentIsDownloaded(true)
                : null),
      ),
    );
  }
}
