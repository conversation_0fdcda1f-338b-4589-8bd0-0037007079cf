import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/email/email.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';
import 'package:bitacora/util/date_utils.dart';

class EmailApiTranslator implements ModelTranslator<Email> {
  const EmailApiTranslator();

  @override
  Email fromMap(Map<String, dynamic> data) {
    return Email(
      remoteId: RemoteId(data['id']),
      value: EmailValue(data['email']),
      type: PersonDetailContactType.fromApiValue(data['email_type']),
      updatedAt: EmailUpdatedAt(getDateTimeFromApi(data['updated_at'])),
      createdAt: EmailCreatedAt(getDateTimeFromApi(data['created_at'])),
      personDetail: PersonDetail(id: LocalId(data['person_detail_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(Email model) {
    throw UnimplementedError();
  }
}
