import 'dart:async';

import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_flutter/amplify_flutter.dart' as amp;
import 'package:amplify_storage_s3/amplify_storage_s3.dart';
import 'package:bitacora/amplifyconfiguration.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

const kUploadTimeoutDuration = Duration(seconds: 15);

class Amplify {
  factory Amplify() => inject(() => const Amplify._());

  const Amplify._();

  bool get isConfigured {
    return amp.Amplify.isConfigured;
  }

  amp.StorageCategory get storage {
    return amp.Amplify.Storage;
  }

  Future<void> addPlugins(List<amp.AmplifyPluginInterface> plugins) {
    return amp.Amplify.addPlugins(plugins);
  }

  Future<void> configure(String configuration) {
    return amp.Amplify.configure(configuration);
  }
}

class AmplifyStorageUtil {
  Completer configCompleter = Completer();
  bool isConfiguring = false;

  static final AmplifyStorageUtil _instance = AmplifyStorageUtil._();

  factory AmplifyStorageUtil() => inject(() => _instance);

  AmplifyStorageUtil._();

  @visibleForTesting
  void reset() {
    isConfiguring = false;
    configCompleter = Completer();
  }

  Future<void> uploadFile({
    required String path,
    required String key,
  }) async {
    await _maybeShortCircuit();
    await _maybeConfigure();

    final startTime = Clock().now();
    final timeout = Completer();
    var timer = Timer(kUploadTimeoutDuration, timeout.complete);
    final operation = Amplify().storage.uploadFile(
          localFile: amp.AWSFile.fromPath(path),
          path: amp.StoragePath.fromString(key),
          onProgress: (progress) {
            timer.cancel();
            timer = Timer(kUploadTimeoutDuration, timeout.complete);
          },
        );

    final operationCompleter = Completer();
    unawaited(operation.result.then((_) => operationCompleter.complete()));

    await Future.any([timeout.future, operationCompleter.future]);
    if (operationCompleter.isCompleted) {
      timer.cancel();
      return;
    }

    unawaited(operation.cancel());
    throw TimeoutException(
      'Upload timed out.',
      Clock().now().difference(startTime),
    );
  }

  Future<void> downloadFile({
    required String path,
    required String key,
  }) async {
    logger.i('amplify:download will download key $key');
    await _maybeShortCircuit();
    await _maybeConfigure();
    await Amplify()
        .storage
        .downloadFile(
          localFile: amp.AWSFile.fromPath(path),
          path: amp.StoragePath.fromString(key),
        )
        .result;
  }

  Future<List<int>> downloadData({
    required String key,
  }) async {
    logger.i('amplify:download will download key $key');
    await _maybeShortCircuit();
    await _maybeConfigure();
    final result = await Amplify()
        .storage
        .downloadData(
          path: amp.StoragePath.fromString(key),
        )
        .result;
    return result.bytes;
  }

  Future<void> _maybeConfigure() async {
    if (Amplify().isConfigured) {
      return;
    }

    if (isConfiguring) {
      await configCompleter.future;
      return;
    }

    isConfiguring = true;
    await Amplify().addPlugins([AmplifyAuthCognito(), AmplifyStorageS3()]);
    await Amplify().configure(amplifyconfig);
    configCompleter.complete();
  }

  Future<void> _maybeShortCircuit() async {
    final prefs = await SharedPreferences.getInstance();
    final statusCode = prefs.getInt(SharedPreferencesKeys.devApiMitmStatusCode);
    if (statusCode != null) {
      final slowdownMs =
          prefs.getInt(SharedPreferencesKeys.devApiMitmSlowdownMs);
      await Future.delayed(Duration(milliseconds: slowdownMs ?? 0));
      throw 'amplify:transfer short circuit';
    }
  }
}
