import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_contract.dart';

class EntryGroupDbTranslator implements DbTranslator<EntryGroup> {
  const EntryGroupDbTranslator();

  @override
  Set<Field> get nestedModelFields => tagNestedModelFields;

  @override
  Future<EntryGroup> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return EntryGroup(
      id: fields[EntryGroupField.id]?.value(map),
      remoteId: fields[EntryGroupField.remoteId]?.value(map),
      name: fields[EntryGroupField.name]?.value(map),
      createdAt: fields[EntryGroupField.createdAt]?.value(map),
      entries: await fields[EntryGroupField.entries]?.nested(context, map),
      organization:
          await fields[EntryGroupField.organization]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, EntryGroup model) async {
    final map = <String, dynamic>{};
    const contract = EntryGroupDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.name, model.name);
    addField(map, contract.createdAt, model.createdAt);

    await saveNestedModel(context, map, contract.organizationId,
        context.db.organization, model.organization);

    return map;
  }
}
