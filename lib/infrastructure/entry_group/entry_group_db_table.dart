import 'dart:async';

import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/query/organization_common_repository_queries.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group/entry_group_repository.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_key.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_translator.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_for_outgoing_mutation_repository_query.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_contract.dart';

class EntryGroupDbTable extends DbTable<EntryGroup, EntryGroupDbFieldsBuilder>
    implements EntryGroupRepository<DbContext, EntryGroupDbFieldsBuilder> {
  EntryGroupDbTable() : super() {
    addIdResolver(_idResolverByCreatedAt);
  }

  @override
  EntryGroupDbContract get contract => const EntryGroupDbContract();

  @override
  DbTranslator<EntryGroup> get translator => const EntryGroupDbTranslator();

  @override
  EntryGroupDbFieldsBuilder get fieldsBuilder => EntryGroupDbFieldsBuilder();

  Future<LocalId?> _idResolverByCreatedAt(
    DbContext context,
    EntryGroup model,
  ) async {
    if (model.name == null) {
      return null;
    }

    final orgId = await _getLocalOrgId(context, model.organization!);
    final result = await (await context.executor).query(
      tableName,
      columns: [idColumn],
      where: '${contract.createdAt} = ? AND ${contract.organizationId} = ?',
      whereArgs: [model.createdAt!.dbValue, orgId!.dbValue],
      limit: 1,
    );
    return result.isEmpty ? null : LocalId(result[0][idColumn] as int);
  }

  Future<LocalId?> _getLocalOrgId(
      DbContext context, Organization organization) async {
    if (organization.id != null) {
      return organization.id!;
    }

    final orgWithId = await context.db.query(
      OrganizationIdRepositoryQuery(remoteId: organization.remoteId!),
      context: context,
    );
    return orgWithId?.id;
  }

  @override
  Future<void> onSaved(DbContext context, Mutation<EntryGroup> mutation) async {
    final model = mutation.model!;

    if (model.entries != null) {
      await context.db.entryGroupEntry.saveAll(context, mutation);
    }

    return super.onSaved(context, mutation);
  }

  @override
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    final db = context.db;

    await db.entryGroupEntry.deleteAll(context, entryGroupId: id);
  }

  @override
  Future<EntryGroup?> findByEntryId(DbContext context, LocalId entryId) {
    const entry = EntryDbContract();
    const entryGroupEntry = EntryGroupEntryDbContract();
    return takeFirst(
      rawQuery(
        context,
        '''
        SELECT ${columnsForSelect(context.fields!)} FROM $tableName
        LEFT JOIN ${entryGroupEntry.tableName} 
          ON ${contract.id} = ${entryGroupEntry.entryGroupId}
        LEFT JOIN ${entry.tableName}
          ON ${entryGroupEntry.entryId} = ${entry.id}
        WHERE ${entry.id} = ?
        ''',
        [entryId.dbValue],
      ),
    );
  }

  @override
  Future<void> onSyncRequested(
      DbContext context, Mutation<EntryGroup> mutation) async {
    final entryGroup = await context.db.query(
      EntryGroupForOutgoingMutationRepositoryQuery(id: mutation.id!),
      context: context,
    );

    if (mutation.type == MutationType.delete &&
        entryGroup?.remoteId?.value == null) {
      return;
    }

    if (entryGroup == null) {
      return;
    }

    await context.db.outgoingMutation.save(
      context,
      OutgoingMutation(
        key: OutgoingMutationKey(DateTime.now().microsecondsSinceEpoch),
        mutationType: mutation.type,
        model: OutgoingMutationModel(
            entryGroup, OutgoingMutationModelType.entryGroup),
        organization: entryGroup.organization,
      ),
    );
  }
}
