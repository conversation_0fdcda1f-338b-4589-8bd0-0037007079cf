import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata_fields_builder.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_last_sync_time.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_next_page_token.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/sync_metadata/sync_metadata_db_contract.dart';

class SyncMetadataDbFieldsBuilder extends DbFieldsBuilder
    implements SyncMetadataFieldsBuilder {
  SyncMetadataDbFieldsBuilder() {
    _id();
  }

  SyncMetadataDbContract get contract => const SyncMetadataDbContract();

  SyncMetadataDbFieldsBuilder _id() {
    addField(
      ProjectField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  SyncMetadataFieldsBuilder collectionType() {
    addField(
      SyncMetadataField.collectionType,
      DbField(
        column: contract.collectionType,
        valueBuilder: (v) => SyncMetadataCollectionType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  SyncMetadataFieldsBuilder nextPageToken() {
    addField(
      SyncMetadataField.nextPageToken,
      DbField(
        column: contract.nextPageToken,
        valueBuilder: (v) => SyncMetadataNextPageToken(v),
      ),
    );
    return this;
  }

  @override
  SyncMetadataFieldsBuilder lastSyncTime() {
    addField(
      SyncMetadataField.lastSyncTime,
      DbField(
        column: contract.lastSyncTime,
        valueBuilder: (v) => SyncMetadataLastSyncTime(
            v == null ? null : DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  SyncMetadataFieldsBuilder syncVersion() {
    addField(
      SyncMetadataField.syncVersion,
      DbField(
        column: contract.syncVersion,
        valueBuilder: (v) => SyncMetadataSyncVersion(v),
      ),
    );
    return this;
  }

  @override
  SyncMetadataFieldsBuilder organization(Fields fields) {
    addField(
      SyncMetadataField.organization,
      DbField(
        key: SyncMetadataField.organization,
        column: contract.organizationId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
