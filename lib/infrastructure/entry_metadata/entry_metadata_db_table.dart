import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_translator.dart';

class EntryMetadataDbTable
    extends DbTable<EntryMetadata, EntryMetadataDbFieldsBuilder>
    implements
        EntryMetadataRepository<DbContext, EntryMetadataDbFieldsBuilder> {
  @override
  EntryMetadataDbContract get contract => const EntryMetadataDbContract();

  @override
  DbTranslator<EntryMetadata> get translator => EntryMetadataDbTranslator();

  @override
  EntryMetadataDbFieldsBuilder get fieldsBuilder =>
      EntryMetadataDbFieldsBuilder();

  @override
  Future<List<EntryMetadata>> findAll(DbContext context, LocalId entryId) {
    return query(
      context,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );
  }

  @override
  Future<void> saveAll(DbContext context, Mutation<Entry> entryMutation) async {
    assert(context.txn != null);
    final entryId = entryMutation.id!;

    await deleteAll(context, entryId);

    final metadataList = entryMutation.model!.metadata!;

    for (final metadata in metadataList) {
      assert(metadata.entry?.id == null || metadata.entry?.id == entryId);
      await save(
        context,
        EntryMetadata(
          type: metadata.type,
          value: metadata.value,
          entry: Entry(id: entryId),
        ),
      );
    }
  }

  @override
  Future<void> deleteAll(DbContext context, LocalId entryId) async {
    final metadataList = await findAll(
      context.copyWith(fields: context.db.entryMetadata.fieldsBuilder.build()),
      entryId,
    );

    for (final metadata in metadataList) {
      await delete(context, metadata.id!);
    }
  }
}
