import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class CustomFieldOptionsDbContract extends DbContract {
  static const String _ = 'cfo_';
  static const String _tableName = 'customFieldOptions';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String placeholder = '${_}placeholder';
  final String isRequired = '${_}isRequired';
  final String customFieldId = '${_}customFieldId';

  const CustomFieldOptionsDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithTemplatelogTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $placeholder TEXT,
    $isRequired INTEGER NOT NULL,
    $customFieldId INTEGER NOT NULL
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) {
    if (oldVersion < kDbVersionWithTemplatelogTables) {
      return db.execute(create);
    }
  }
}
