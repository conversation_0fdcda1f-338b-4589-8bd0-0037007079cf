import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/entry_source/entry_source.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_owner_type.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/extension/api_extension_type.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_api_translator.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_api_translator.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_api_translator.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_api_translator.dart';
import 'package:bitacora/infrastructure/user/user_api_translator.dart';
import 'package:bitacora/infrastructure/worklog/worklog_api_translator.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:latlong2/latlong.dart';

class EntryApiTranslator implements ModelTranslator<Entry> {
  final ApiTranslator apiTranslator;

  const EntryApiTranslator(this.apiTranslator);

  @override
  Entry fromMap(Map<String, dynamic> data) {
    final createdAt = data['created_at'] == null
        ? EntryCreatedAt(DateTime.now())
        : EntryCreatedAt(getDateTimeFromApi(data['created_at']));
    final updatedAt = data['updated_at'] == null
        ? EntryUpdatedAt(createdAt.value)
        : EntryUpdatedAt(getDateTimeFromApi(data['updated_at']));

    return Entry(
      remoteId: data['id'] == null ? null : RemoteId(data['id']),
      day: LogDay(data['day']),
      time: _getTime(data, createdAt),
      startDate: NullableLogDay.fromStringDate(data['start_date']),
      endDate: NullableLogDay.fromStringDate(data['end_date']),
      startTime: NullableLogTime.fromStringTime(data['proper_start_time']),
      endTime: NullableLogTime.fromStringTime(data['proper_end_time']),
      location: data['loc_latitude'] == null || data['loc_longitude'] == null
          ? null
          : LatLngValueObject(
              LatLng(data['loc_latitude'], data['loc_longitude'])),
      comments: EntryComments(data['comments']),
      createdAt: createdAt,
      updatedAt: updatedAt,
      assignee: _getAssignee(data),
      author: User(
        remoteId: RemoteId(data['author_id']),
        name: UserName(data['author_name']),
      ),
      extension: _createExtensionFromApi(data),
      openState: data['open'] != null
          ? apiTranslator.openState.fromMap(data['open'])
          : null,
      attachments: data['attachments'] != null
          ? (data['attachments'] as List)
              .map((e) => apiTranslator.attachment.fromMap(e))
              .toList(growable: false)
          : [],
      tags: data['tags'] != null
          ? (data['tags'] as List).map((e) {
              e['organization_id'] = data['organization_id'];
              return apiTranslator.tag.fromMap(e);
            }).toList(growable: false)
          : [],
      locationTracking: data['tracking_uuid'] != null
          ? LocationTracking(
              uuid: LocationTrackingUuid(data['tracking_uuid']),
              ownerType: LocationTrackingOwnerType.entry,
            )
          : null,
      source: _getSourceFromApi(data),
      metadata: data['metadata'] != null
          ? _getMetadataFromApi(apiTranslator, data['metadata'])
          : [],
    );
  }

  EntrySource? _getSourceFromApi(Map<String, dynamic> data) {
    final source = data['source'];
    if (source == null) {
      return null;
    }

    if (source is String) {
      return EntrySource(
        type: EntrySourceType.fromApiValue(source),
        metadata: data['source_metadata'] != null
            ? EntrySourceMetadata.fromMap(data['source_metadata'])
            : null,
      );
    }

    if (source is Map<String, dynamic>) {
      return EntrySource(
        type: EntrySourceType.fromApiValue(source['type']),
        metadata: source['metadata'] != null
            ? EntrySourceMetadata.fromMap(source['metadata'])
            : null,
      );
    }

    return null;
  }

  List<EntryMetadata> _getMetadataFromApi(
      ApiTranslator apiTranslator, List<dynamic> data) {
    final List<EntryMetadata> result = [];

    for (final metadataItem in data) {
      if (metadataItem['value'].isEmpty || metadataItem['value'] == null) {
        continue;
      }

      switch (metadataItem['type']) {
        case 'sentiment':
          result.add(apiTranslator.entryMetadata.fromMap(metadataItem));
          break;
        case 'keywords':
        case 'actionItems':
        case 'healthItems':
        case 'healthTags':
          result.addAll((metadataItem['value'] as String).split(',').map((e) {
            metadataItem['value'] = e;
            return apiTranslator.entryMetadata.fromMap(metadataItem);
          }).toList(growable: false));
          break;
      }
    }

    return result;
  }

  @override
  Map<String, dynamic> toMap(Entry model) {
    final map = <String, dynamic>{
      'day': model.day!.apiValue,
      'time': model.time!.apiValue,
      'start_date': model.startDate!.apiValue,
      'end_date': model.endDate!.apiValue,
      'start_time': model.startTime!.apiValue,
      'end_time': model.endTime!.apiValue,
      'comments': model.comments!.apiValue,
    };

    if (model.location?.value != null) {
      map['loc_longitude'] = model.location!.value!.longitude;
      map['loc_latitude'] = model.location!.value!.latitude;
    }

    if (model.remoteId?.value != null) {
      map['id'] = model.remoteId!.apiValue;
    } else {
      map['created_at'] = model.createdAt!.apiValue;
    }

    map.addAll(const UserApiTranslator()
        .toMapWithPrefix(model.assignee ?? const User(), prefix: 'assignee_'));

    map.addAll(_getExtensionApiTranslator(model).toMap(model.extension!));

    if (model.openState != null) {
      map['open'] = apiTranslator.openState.toMap(model.openState!);
    }

    map['attachments'] = model.attachments!
        .map((e) => apiTranslator.attachment.toMap(e))
        .toList(growable: false);

    map['tags'] =
        model.tags!.map((e) => e.name!.displayValue).toList(growable: false);

    map['tracking_uuid'] = model.locationTracking?.uuid!.apiValue;

    if (model.source != null) {
      map['source'] = model.source!.type!.apiValue;
      map['source_metadata'] = model.source!.metadata?.apiValue;
    }

    if (model.metadata != null) {
      map['metadata'] = model.metadata!
          .map((e) => apiTranslator.entryMetadata.toMap(e))
          .toList(growable: false);
    }

    return map;
  }

  /// We currently use this exclusively for updating due to syncVersion.
  /// Add new fields here, nothing else will be updated.
  Entry updateFromMap(Entry original, Map<String, dynamic> data) {
    return Entry(
      id: original.id,
      startDate: NullableLogDay.fromStringDate(data['start_date']),
      endDate: NullableLogDay.fromStringDate(data['end_date']),
      startTime: NullableLogTime.fromStringTime(data['start_time']),
      endTime: NullableLogTime.fromStringTime(data['end_time']),
    );
  }

  Extension _createExtensionFromApi(Map<String, dynamic> data) {
    // Forward organization_id for projects.
    final extensionMap = data['extension'];
    extensionMap['organization_id'] = data['organization_id'];

    final type = ApiExtensionType.fromApiString(data['extension_type']);
    switch (type) {
      case ExtensionType.worklog:
        return apiTranslator.worklog.fromMap(extensionMap);
      case ExtensionType.inventorylog:
        return apiTranslator.inventorylog.fromMap(extensionMap);
      case ExtensionType.personnellog:
        return apiTranslator.personnellog.fromMap(extensionMap);
      case ExtensionType.progresslog:
        return apiTranslator.progresslog.fromMap(extensionMap);
      case ExtensionType.templatelog:
        return apiTranslator.templatelog.fromMap(extensionMap);
      default:
        throw 'Unrecognized extension type $type';
    }
  }

  ModelTranslator _getExtensionApiTranslator(Entry model) {
    final type = model.extension!.extensionType;
    switch (type) {
      case ExtensionType.worklog:
        return const WorklogApiTranslator();
      case ExtensionType.inventorylog:
        return const InventorylogApiTranslator();
      case ExtensionType.personnellog:
        return const PersonnellogApiTranslator();
      case ExtensionType.progresslog:
        return const ProgresslogApiTranslator();
      case ExtensionType.templatelog:
        return TemplatelogApiTranslator(apiTranslator);
      default:
        throw 'Unrecognized extension type $type';
    }
  }

  User? _getAssignee(Map<String, dynamic> data) {
    // Server might respond name with no id. This would be stored in User
    // table without remote id.
    // Server might respond id with no name. Make sure we don't overwrite
    // name with empty. Users table would require sync.
    final assigneeId = data['assignee_id'];
    final assigneeName = data['assignee_name'];
    final hasAssigneeId = assigneeId != null && assigneeId > 0;
    final hasAssigneeName =
        assigneeName != null && assigneeName.toString().isNotEmpty;

    if (!hasAssigneeId && !hasAssigneeName) {
      return null;
    }

    return User(
      remoteId: hasAssigneeId ? RemoteId(data['assignee_id']) : null,
      name: hasAssigneeName ? UserName(data['assignee_name']) : null,
    );
  }

  LogTime _getTime(Map<String, dynamic> data, EntryCreatedAt createdAt) {
    final apiTime = data['time'];
    if (apiTime is int) {
      return LogTime(apiTime);
    }

    final localCreatedAt = createdAt.value.toLocal();
    final hour = localCreatedAt.hour;
    final minute = localCreatedAt.minute;
    // FIXME: local date could mismatch logday due to timezone
    return LogTime(hour * 100 + minute);
  }
}
