import 'package:bitacora/application/remote_config.dart';
import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/entry_ai_generator.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_vertex_schema.dart';
import 'package:bitacora/infrastructure/vertex_ai_generator.dart';
import 'package:bitacora/infrastructure/vertex_ai_model_generator.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';

import 'entry_api_translator.dart';

class EntryVertexGenerator extends VertexAiModelGenerator<Entry>
    implements EntryAiGenerator<VertexAiGenerator> {
  @override
  ModelTranslator<Entry> get apiTranslator =>
      EntryApiTranslator(ApiTranslator());

  @override
  Schema get responseSchema => entryVertexSchema;

  @override
  Future<String> get systemPrompt => RemoteConfig().getString('worklogPrompt');
}
