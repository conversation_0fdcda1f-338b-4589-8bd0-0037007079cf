import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_contract.dart';
import 'package:bitacora/infrastructure/projectentriesproject/project_entries_project_db_contract.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_contract.dart';
import 'package:bitacora/infrastructure/user/user_db_contract.dart';
import 'package:bitacora/util/inject/inject.dart';

class DbQueryScopeUtils {
  factory DbQueryScopeUtils() => inject(() => const DbQueryScopeUtils._());

  const DbQueryScopeUtils._();

  EntryDbContract get entryContract => const EntryDbContract();

  AccessEntryDbContract get accessEntryContract =>
      const AccessEntryDbContract();

  ProjectDbContract get projectContract => const ProjectDbContract();

  ProjectEntriesProjectDbContract get projectEntriesProjectContract =>
      const ProjectEntriesProjectDbContract();

  TemplatelogDbContract get templatelogContract =>
      const TemplatelogDbContract();

  CustomFieldMetadataDbContract get metadataContract =>
      const CustomFieldMetadataDbContract();

  UserDbContract get userContract => const UserDbContract();

  String fromJoin(QueryScope queryScope, {String? entryAlias}) {
    if (queryScope.byUser) {
      return '''
        FROM ${userContract.tableName}
        LEFT JOIN ${entryContract.tableName} ${entryAlias ?? ''}
        ON ${entryContract.authorId} = ${userContract.id}
        ''';
    }

    return '''
      FROM ${projectEntriesProjectContract.tableName}
      LEFT JOIN ${entryContract.tableName} ${entryAlias ?? ''}
      ON ${entryContract.id} = ${projectEntriesProjectContract.entryId}
      ${queryScope.byOrg ? '''
      LEFT JOIN ${projectContract.tableName}
      ON ${projectContract.id} = ${projectEntriesProjectContract.projectId}
      ''' : ''}
      ${queryScope.byMetadataFilter ? '''
      LEFT JOIN ${templatelogContract.tableName} 
        ON ${entryContract.extensionId} = ${templatelogContract.id} 
      LEFT JOIN ${metadataContract.tableName} 
        ON ${templatelogContract.id} = ${metadataContract.templatelogId}
      ''' : ''}
    ''';
  }

  String where(QueryScope queryScope) {
    if (queryScope.byUser) {
      return '${userContract.id} = ?';
    }
    if (queryScope.byOrg) {
      return '${projectContract.organizationId} = ? '
          'AND ${projectContract.isSyncable} = 1';
    }
    if (queryScope.byMetadataFilter) {
      return '${entryContract.extensionType} = ? '
          'AND ${metadataContract.customFieldId} = ? '
          'AND ${metadataContract.value} = ?';
    }
    return '${projectEntriesProjectContract.projectId} = ?';
  }

  List<dynamic> queryArg(QueryScope queryScope) {
    if (queryScope.byUser) {
      return [queryScope.userId!.dbValue];
    }
    if (queryScope.byOrg) {
      return [queryScope.orgId!.dbValue];
    }
    if (queryScope.byMetadataFilter) {
      return [
        ExtensionType.templatelog.dbValue,
        queryScope.metadataFilter!.customField!.id!.dbValue,
        queryScope.metadataFilter!.value!.dbValue
      ];
    }
    return [queryScope.projectId!.dbValue];
  }

  String accessFromJoin(QueryScope queryScope, {String? entryAlias}) {
    return '''
      ${fromJoin(queryScope, entryAlias: entryAlias)}
      LEFT JOIN ${accessEntryContract.tableName}
      ON ${entryContract.id} = ${accessEntryContract.entryId}
    ''';
  }

// FIXME: consider extending queryArg to queryArgs []
  String accessWhere(QueryScope queryScope) => '''
    ${where(queryScope)}
    AND (
      ${accessEntryContract.permission} & $kAccessRead = $kAccessRead
      OR (
        ${accessEntryContract.permission} & $kAccessReadOwn = $kAccessReadOwn 
        AND (
          ${entryContract.authorId} = ${queryScope.userId!.dbValue}
          OR ${entryContract.assigneeId} = ${queryScope.userId!.dbValue}
        ) 
      )
    )
    ''';

  String? filterOutCondition(String field, QueryScope queryScope) {
    if (queryScope.filterOut?.isEmpty ?? true) {
      return null;
    }
    return '$field NOT IN (${List.generate(
      queryScope.filterOut!.length,
      (index) => '?',
    ).join(',')})';
  }
}
