import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/person/person_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/person/person_db_contract.dart';
import 'package:bitacora/infrastructure/person/person_db_fields_builder.dart';
import 'package:bitacora/infrastructure/person/person_db_translator.dart';

class PersonDbTable extends DbTable<Person, PersonDbFieldsBuilder>
    implements PersonRepository<DbContext, PersonDbFieldsBuilder> {
  @override
  PersonDbContract get contract => const PersonDbContract();

  @override
  DbTranslator<Person> get translator => const PersonDbTranslator();

  @override
  PersonDbFieldsBuilder get fieldsBuilder => PersonDbFieldsBuilder();

  @override
  Future<Person?> findByUserId(DbContext context, LocalId userId) {
    return takeFirst(query(
      context,
      where: '${contract.userId} = ?',
      whereArgs: [userId.dbValue],
    ));
  }
}
