import 'package:bitacora/domain/ai/ai_credits_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesAiCreditsManager implements AiCreditsManager {
  static const _creditsUsedKeyPrefix = 'ai_credits_used_count';
  static const _lastResetDateKeyPrefix = 'ai_credits_last_reset_date';

  final SharedPreferences _prefs;

  SharedPreferencesAiCreditsManager._(this._prefs);

  static Future<SharedPreferencesAiCreditsManager> init() async {
    final prefs = await SharedPreferences.getInstance();
    return SharedPreferencesAiCreditsManager._(prefs);
  }

  String _getCreditsUsedKey(int organizationId) {
    return '${_creditsUsedKeyPrefix}_org_$organizationId';
  }

  String _getLastResetDateKey(int organizationId) {
    return '${_lastResetDateKeyPrefix}_org_$organizationId';
  }

  Future<void> _resetCreditsIfNewDay(int organizationId) async {
    final lastResetDateKey = _getLastResetDateKey(organizationId);
    final lastResetDate = DateTime.fromMillisecondsSinceEpoch(
      _prefs.getInt(lastResetDateKey) ?? 0,
    );
    final now = DateTime.now();

    if (lastResetDate.day != now.day ||
        lastResetDate.month != now.month ||
        lastResetDate.year != now.year) {
      await resetCredits(organizationId);
    }
  }

  @override
  Future<bool> canUseFeature(int organizationId) async {
    await _resetCreditsIfNewDay(organizationId);
    final usedCredits = await getUsedCredits(organizationId);
    return usedCredits < AiCreditsManager.dailyCredits;
  }

  @override
  Future<int> getUsedCredits(int organizationId) async {
    final creditsUsedKey = _getCreditsUsedKey(organizationId);
    return _prefs.getInt(creditsUsedKey) ?? 0;
  }

  @override
  Future<int> getRemainingCredits(int organizationId) async {
    await _resetCreditsIfNewDay(organizationId);
    final creditsUsedKey = _getCreditsUsedKey(organizationId);
    final usedCredits = _prefs.getInt(creditsUsedKey) ?? 0;
    return AiCreditsManager.dailyCredits - usedCredits;
  }

  @override
  Future<void> resetCredits(int organizationId) async {
    final creditsUsedKey = _getCreditsUsedKey(organizationId);
    final lastResetDateKey = _getLastResetDateKey(organizationId);
    await _prefs.setInt(creditsUsedKey, 0);
    await _prefs.setInt(
        lastResetDateKey, DateTime.now().millisecondsSinceEpoch);
  }

  @override
  Future<void> incrementUsedCredits(int organizationId) async {
    final creditsUsedKey = _getCreditsUsedKey(organizationId);
    final currentCredits = _prefs.getInt(creditsUsedKey) ?? 0;
    await _prefs.setInt(creditsUsedKey, currentCredits + 1);
  }

  @override
  Future<String> getCreditsExhaustedMessage() async {
    return 'Has alcanzado el límite diario de ${AiCreditsManager.dailyCredits} generaciones de IA. Los créditos se renovarán al final del día.';
  }
}
