import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/template_group/template_group.dart';
import 'package:bitacora/domain/template_group/template_group_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/template_group/template_group_db_contract.dart';

class TemplateGroupDbFieldsBuilder extends DbFieldsBuilder
    implements TemplateGroupFieldsBuilder {
  TemplateGroupDbFieldsBuilder() {
    _id();
  }

  TemplateGroupDbContract get contract => const TemplateGroupDbContract();

  TemplateGroupDbFieldsBuilder _id() {
    addField(
      TemplateGroupField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  TemplateGroupDbFieldsBuilder remoteId() {
    addField(
      TemplateGroupField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  TemplateGroupDbFieldsBuilder name() {
    addField(
      TemplateGroupField.name,
      DbField(
        column: contract.name,
        valueBuilder: (v) => TemplateGroupName(v),
      ),
    );
    return this;
  }

  @override
  TemplateGroupDbFieldsBuilder order() {
    addField(
      TemplateGroupField.order,
      DbField(
        column: contract.order,
        valueBuilder: (v) => TemplateGroupOrder(v),
      ),
    );
    return this;
  }

  @override
  TemplateGroupDbFieldsBuilder conditions(Fields fields) {
    addField(
      TemplateGroupField.conditions,
      DbField(
        key: TemplateGroupField.conditions,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) {
          return nestedContext
            .db.templateCondition
            .findAllByGroup(nestedContext, LocalId(value));
        },
      ),
    );
    return this;
  }

  @override
  TemplateGroupDbFieldsBuilder blocks(Fields fields) {
    addField(
      TemplateGroupField.blocks,
      DbField(
        key: TemplateGroupField.blocks,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.templateBlock
            .findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  TemplateGroupDbFieldsBuilder template(Fields fields) {
    addField(
      TemplateGroupField.template,
      DbField(
        key: TemplateGroupField.template,
        column: contract.templateId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.template.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
