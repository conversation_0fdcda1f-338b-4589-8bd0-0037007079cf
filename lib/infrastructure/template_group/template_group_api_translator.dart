import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template_group/template_group.dart';

class TemplateGroupApiTranslator implements ModelTranslator<TemplateGroup> {
  const TemplateGroupApiTranslator();

  @override
  TemplateGroup fromMap(Map<String, dynamic> data) {
    return TemplateGroup(
      remoteId: RemoteId(data['id']),
      name: TemplateGroupName(data['name']),
      order: TemplateGroupOrder(data['order']),
      template: Template(id: LocalId(data['template_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(TemplateGroup model) {
    throw UnimplementedError();
  }
}
