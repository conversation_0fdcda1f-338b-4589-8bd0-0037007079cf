import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/query/organization_common_repository_queries.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/tag/tag_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/tag/tag_db_contract.dart';
import 'package:bitacora/infrastructure/tag/tag_db_fields_builder.dart';
import 'package:bitacora/infrastructure/tag/tag_db_translator.dart';
import 'package:bitacora/infrastructure/tag/tag_entry_db_contract.dart';

class TagDbTable extends DbTable<Tag, TagDbFieldsBuilder>
    implements TagRepository<DbContext, TagDbFieldsBuilder> {
  TagDbTable() : super() {
    addIdResolver(_idResolverByTagName);
  }

  Future<LocalId?> _getLocalOrgId(
      DbContext context, Organization organization) async {
    if (organization.id != null) {
      return organization.id!;
    }

    final orgWithId = await context.db.query(
      OrganizationIdRepositoryQuery(remoteId: organization.remoteId!),
      context: context,
    );
    return orgWithId?.id;
  }

  Future<LocalId?> _idResolverByTagName(DbContext context, Tag model) async {
    if (model.name == null) {
      return null;
    }

    final orgId = await _getLocalOrgId(context, model.organization!);

    // This id resolver should only be used if the defaults fail (id, remoteId)
    // In which case organization id is required for disambiguation.
    final result = await (await context.executor).query(tableName,
        columns: [idColumn],
        where: '${contract.name} = ? AND ${contract.organizationId} = ?',
        whereArgs: [model.name!.dbValue, orgId!.dbValue],
        limit: 1);
    return result.isEmpty ? null : LocalId(result[0][idColumn] as int);
  }

  @override
  TagDbContract get contract => const TagDbContract();

  TagEntryDbContract get tagEntryContract => const TagEntryDbContract();

  @override
  DbTranslator<Tag> get translator => const TagDbTranslator();

  @override
  TagDbFieldsBuilder get fieldsBuilder => TagDbFieldsBuilder();

  @override
  List<String> get searchColumns => [contract.name];

  @override
  Future<List<Tag>> findByOrganization(DbContext context, LocalId orgId) {
    return query(
      context,
      where: '${contract.organizationId} = ?',
      whereArgs: [orgId.dbValue],
    );
  }

  @override
  Future<List<Tag>> findAll(DbContext context, LocalId entryId) async {
    return rawQuery(
      context,
      '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM ${contract.tableName}
      LEFT JOIN ${tagEntryContract.tableName}
      ON ${contract.id} = ${tagEntryContract.tagId}
      WHERE ${tagEntryContract.entryId} = ?
      ORDER BY ${contract.name} 
      ''',
      [entryId.dbValue],
    );
  }

  @override
  Future<void> saveAll(
    DbContext context,
    Mutation<Entry> entryMutation,
  ) async {
    assert(context.txn != null);
    final tags = entryMutation.model!.tags!;
    final entryId = entryMutation.id!;

    final saved = <LocalId>[];
    for (final tag in tags) {
      saved.add((await save(context, tag))!);
    }

    final executor = await context.executor;
    if (entryMutation.type != MutationType.insert) {
      await deleteAll(context, entryId);
    }

    for (final id in saved) {
      await executor.insert(
        tagEntryContract.tableName,
        {
          tagEntryContract.entryId: entryId.dbValue,
          tagEntryContract.tagId: id.dbValue,
        },
      );
    }
  }

  @override
  Future<void> deleteAll(DbContext context, LocalId entryId) async {
    final executor = await context.executor;
    await executor.delete(
      tagEntryContract.tableName,
      where: '${tagEntryContract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );
  }

  @override
  Future<List<String>> names(DbContext context, List<String> filterOut) async {
    final executor = await context.executor;

    final results = await executor.rawQuery(
      '''
        SELECT ${contract.name} FROM ${contract.tableName}
        WHERE ${contract.organizationId} = ?
        AND ${contract.name} LIKE ?
        AND ${contract.name} NOT IN (${_questionMarks(filterOut)})
        ORDER BY ${contract.name} ASC
        LIMIT ${context.cursor?.limit ?? 10}
        OFFSET ${context.cursor?.offset ?? 0}
      ''',
      [
        context.queryScope!.orgId!.dbValue,
        context.queryScope!.pattern,
        ...filterOut,
      ],
    );

    return results
        .map<String>((e) => e[contract.name] as String)
        .toList(growable: false);
  }

  @override
  Future<List<Tag>> searchAll(DbContext context) {
    query(DbContext c) async {
      final executor = await c.executor;
      final filterOut =
          DbQueryScopeUtils().filterOutCondition(contract.id, c.queryScope!);

      final results = await executor.rawQuery(
        '''
        SELECT ${columnsForSelect(c.fields!)} 
        FROM ${contract.tableName}
        WHERE ${contract.organizationId} = ?
        AND ${contract.name} LIKE ?
        AND ${contract.remoteId} IS NOT NULL
        ${filterOut != null ? 'AND $filterOut' : ''}
        ORDER BY ${contract.name} ASC
        LIMIT ${c.cursor?.limit ?? 10}
        OFFSET ${c.cursor?.offset ?? 0}
      ''',
        [
          c.queryScope!.orgId!.dbValue,
          c.queryScope!.pattern,
          if (filterOut != null) ...c.queryScope!.filterOut!,
        ],
      );

      return Future.wait(results.map((e) => fromDb(c, e)));
    }

    if (context.hasAnyOfFields(translator.nestedModelFields)) {
      return wrapTransaction(context, (c) => query(c));
    }
    return query(context);
  }

  String _questionMarks(List list) {
    return list.map((e) => '?').join(',');
  }
}
