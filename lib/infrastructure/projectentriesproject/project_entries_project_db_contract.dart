import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class ProjectEntriesProjectDbContract extends DbContract {
  static const String _ = 'pep_';
  static const String _tableName = 'projectEntriesProject';

  final String projectId = '${_}projectId';
  final String entryId = '${_}entryId';

  const ProjectEntriesProjectDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersion;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $projectId INTEGER NOT NULL,
    $entryId INTEGER NOT NULL,
    PRIMARY KEY ($projectId, $entryId)
  )
  ''';
}
