import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_point/value/location_point_created_at.dart';
import 'package:bitacora/domain/location_point/value/location_point_speed.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:latlong2/latlong.dart';

class LocationPointApiTranslator implements ModelTranslator<LocationPoint> {
  const LocationPointApiTranslator();

  @override
  LocationPoint fromMap(Map<String, dynamic> data) {
    return LocationPoint(
      remoteId: RemoteId(data['id']),
      latLong: LatLngValueObject(LatLng(data['latitude'], data['longitude'])),
      speed: LocationPointSpeed(data['speed']),
      createdAt: LocationPointCreatedAt(
          getDateTimeFromApi(data['created_at']).toLocal()),
    );
  }

  @override
  Map<String, dynamic> toMap(LocationPoint model) => {
        'latitude': model.latLong!.value!.latitude,
        'longitude': model.latLong!.value!.longitude,
        'speed': model.speed!.value,
        'created_at': model.createdAt!.value.toUtc().toIso8601String(),
      };
}
