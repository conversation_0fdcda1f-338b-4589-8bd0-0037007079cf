import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_point/location_point_fields_builder.dart';
import 'package:bitacora/domain/location_point/value/location_point_created_at.dart';
import 'package:bitacora/domain/location_point/value/location_point_speed.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/location_point/location_point_db_contract.dart';
import 'package:latlong2/latlong.dart';

class LocationPointDbFieldsBuilder extends DbFieldsBuilder
    implements LocationPointFieldsBuilder {
  LocationPointDbFieldsBuilder() {
    _id();
  }

  LocationPointDbContract get contract => const LocationPointDbContract();

  LocationPointDbFieldsBuilder _id() {
    addField(
      LocationPointField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  LocationPointDbFieldsBuilder remoteId() {
    addField(
      LocationPointField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  LocationPointFieldsBuilder latLong() {
    addField(
      LocationPointField.latLong,
      DbField(
        columnAdder: (columns) =>
            columns.addAll([contract.latitude, contract.longitude]),
        multiValueBuilder: (values) {
          if (values[contract.latitude] == null ||
              values[contract.longitude] == null) {
            return const LatLngValueObject(null);
          }

          return LatLngValueObject(LatLng(
            (values[contract.latitude] as num).toDouble(),
            (values[contract.longitude] as num).toDouble(),
          ));
        },
      ),
    );
    return this;
  }

  @override
  LocationPointDbFieldsBuilder speed() {
    addField(
      LocationPointField.speed,
      DbField(
        column: contract.speed,
        valueBuilder: (v) => LocationPointSpeed(v),
      ),
    );
    return this;
  }

  @override
  LocationPointDbFieldsBuilder tracking(Fields fields) {
    addField(
      LocationPointField.tracking,
      DbField(
        key: LocationPointField.tracking,
        column: contract.trackingId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext
            .db.locationTracking
            .find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  LocationPointDbFieldsBuilder createdAt() {
    addField(
      LocationPointField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) =>
            LocationPointCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }
}
