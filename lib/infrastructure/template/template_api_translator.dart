import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/util/date_utils.dart';

class TemplateApiTranslator implements ModelTranslator<Template> {
  const TemplateApiTranslator();

  @override
  Template fromMap(Map<String, dynamic> data) {
    return Template(
      remoteId: RemoteId(data['id']),
      name: Template<PERSON><PERSON>(data['name']),
      active: TemplateActive(data['active']),
      version: TemplateVersion(data['version']),
      deletedAt: TemplateDeletedAt(data['discarded_at'] == null
          ? null
          : getDateTimeFromApi((data['discarded_at']))),
      updatedAt: TemplateUpdatedAt(getDateTimeFromApi(data['updated_at'])),
      createdAt: TemplateCreatedAt(getDateTimeFromApi(data['created_at'])),
      previousVersion: data['previousVersionId'] == null
          ? null
          : Template(remoteId: RemoteId(data['previousVersionId'])),
      organization: Organization(id: LocalId(data['organization_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(Template model) {
    throw UnimplementedError();
  }
}
