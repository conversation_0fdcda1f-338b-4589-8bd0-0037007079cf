import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template/template_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/template/template_db_contract.dart';
import 'package:bitacora/infrastructure/template/template_db_fields_builder.dart';
import 'package:bitacora/infrastructure/template/template_db_translator.dart';

class TemplateDbTable extends DbTable<Template, TemplateDbFieldsBuilder>
    implements TemplateRepository<DbContext, TemplateDbFieldsBuilder> {
  TemplateDbTable();

  @override
  TemplateDbContract get contract => const TemplateDbContract();

  @override
  DbTranslator<Template> get translator => const TemplateDbTranslator();

  @override
  TemplateDbFieldsBuilder get fieldsBuilder => TemplateDbFieldsBuilder();

  @override
  Future<Template?> find(DbContext context, LocalId id) {
    final whereClause = context.queryScope?.byOrg ?? false
        ? '${contract.id} = ? AND ${contract.organizationId} = ?'
        : '${contract.id} = ? ';

    return takeFirst(query(
      context,
      where: whereClause,
      whereArgs: [
        id.dbValue,
        if (context.queryScope?.byOrg ?? false)
          context.queryScope!.orgId!.dbValue,
      ],
    ));
  }

  @override
  Future<List<Template>> findAll(DbContext context) {
    return query(
      context,
      where: '${contract.organizationId} = ?',
      whereArgs: [context.queryScope!.orgId!.dbValue],
      orderBy: '${contract.name} ASC',
    );
  }

  @override
  Future<void> onSaved(DbContext context, Mutation<Template> mutation) async {
    final model = mutation.model!;

    if (model.groups != null) {
      await context.db.templateGroup.saveAll(context, mutation);
    }
    return super.onSaved(context, mutation);
  }
}
