import 'package:bitacora/application/sync/machine/steps/download/collection/entry_groups/entry_group_entries_repository_query.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry_repository.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_key.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group/entry_group_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_contract.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_translator.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_for_outgoing_mutation_repository_query.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_by_entry_group_entry_for_mutation_repository_query.dart';

class EntryGroupEntryDbTable
    extends DbTable<EntryGroupEntry, EntryGroupEntryDbFieldsBuilder>
    implements
        EntryGroupEntryRepository<DbContext, EntryGroupEntryDbFieldsBuilder> {
  EntryGroupEntryDbTable() : super() {
    addIdResolver(_idResolverByGroupIdAndEntry);
  }

  Future<LocalId?> _idResolverByGroupIdAndEntry(
      DbContext context, EntryGroupEntry model) async {
    if (model.entryGroup?.id == null) {
      return null;
    }

    const entryContract = EntryDbContract();
    final entryQuery = model.entry?.id != null
        ? '${entryContract.id} == ?'
        : model.entry?.remoteId != null
            ? '${entryContract.remoteId} == ?'
            : null;
    if (entryQuery == null) {
      return null;
    }

    const entryGroupContract = EntryGroupDbContract();
    final result = await (await context.executor).rawQuery(
      '''
      SELECT ${contract.id} FROM $tableName
      LEFT JOIN ${entryContract.tableName}
        ON ${contract.entryId} = ${entryContract.id}
      LEFT JOIN ${entryGroupContract.tableName}
        ON ${contract.entryGroupId} = ${entryGroupContract.id}
      WHERE ${entryGroupContract.id} = ? 
      AND $entryQuery
      LIMIT 1 
      ''',
      [
        model.entryGroup!.id!.dbValue,
        model.entry!.id?.dbValue ?? model.entry!.remoteId!.dbValue,
      ],
    );
    return result.isEmpty ? null : LocalId(result[0][idColumn] as int);
  }

  @override
  EntryGroupEntryDbContract get contract => const EntryGroupEntryDbContract();

  @override
  DbTranslator<EntryGroupEntry> get translator =>
      const EntryGroupEntryDbTranslator();

  @override
  EntryGroupEntryDbFieldsBuilder get fieldsBuilder =>
      EntryGroupEntryDbFieldsBuilder();

  @override
  Future<List<EntryGroupEntry>> findAll(
    DbContext context,
    LocalId entryGroupId,
  ) {
    return query(
      context,
      where: '${contract.entryGroupId} = ?',
      whereArgs: [entryGroupId.dbValue],
      orderBy: '${contract.id} ASC',
    );
  }

  @override
  Future<EntryGroupEntry?> findByEntry(
    DbContext context,
    LocalId entryId,
  ) {
    return takeFirst(query(
      context,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
      orderBy: '${contract.id} ASC',
    ));
  }

  @override
  Future<void> saveAll(
    DbContext context,
    Mutation<EntryGroup> entryGroupMutation, [
    bool keepLocalChanges = false,
  ]) async {
    assert(context.txn != null);
    final groupId = entryGroupMutation.id!;

    final localEntryGroupEntryIds = keepLocalChanges
        ? <LocalId>[]
        : (await context.db.query(
            EntryGroupEntriesRepositoryQuery(entryGroupId: groupId),
            context: context,
          ))
            .map<LocalId>((p) => p.id!);

    final incomingEntryGroupEntryIds = [];
    for (final entryGroupEntry in entryGroupMutation.model!.entries!) {
      incomingEntryGroupEntryIds.add(await context.db.entryGroupEntry.save(
        context,
        entryGroupEntry.copyWith(entryGroup: EntryGroup(id: groupId)),
      ));
    }

    if (!keepLocalChanges) {
      await Future.wait(
        localEntryGroupEntryIds
            .where((p) => !incomingEntryGroupEntryIds.contains(p))
            .map((e) => context.db.entryGroupEntry.delete(context, e)),
      );
    }
  }

  @override
  Future<void> deleteAll(DbContext context,
      {LocalId? entryGroupId, LocalId? entryId}) async {
    final executor = await context.executor;

    if (entryGroupId != null) {
      final entryGroupEntries = await findAll(
        context.copyWith(
            fields: context.db.entryGroupEntry.fieldsBuilder.build()),
        entryGroupId,
      );

      for (final entryGroupEntry in entryGroupEntries) {
        await delete(context, entryGroupEntry.id!);
      }

      return;
    }

    await executor.rawDelete(
      '''
      DELETE FROM ${contract.tableName} 
      WHERE ${contract.entryId} = ?
    ''',
      [entryId!.dbValue],
    );
  }

  @override
  Future<void> onSyncRequested(
      DbContext context, Mutation<EntryGroupEntry> mutation) async {
    final entryGroupEntry = await context.db.query(
      EntryGroupEntryForOutgoingMutationRepositoryQuery(id: mutation.id!),
      context: context,
    );

    if (mutation.type == MutationType.delete &&
        entryGroupEntry?.remoteId?.value == null) {
      return;
    }

    if (entryGroupEntry == null) {
      return;
    }

    await context.db.outgoingMutation.save(
      context,
      OutgoingMutation(
        key: OutgoingMutationKey(DateTime.now().microsecondsSinceEpoch),
        mutationType: mutation.type,
        model: OutgoingMutationModel(
            entryGroupEntry, OutgoingMutationModelType.entryGroupEntry),
        organization: entryGroupEntry.entryGroup!.organization,
      ),
    );
  }

  @override
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    final db = context.db;

    final entryId = await db.query(
      EntryByEntryGroupEntryForMutationRepositoryQuery(entryGroupEntryId: id),
      context: context,
    );

    context.db.entry.broadcastMutation(Mutation(
        id: entryId, type: MutationType.update, model: Entry(id: entryId)));
  }
}
