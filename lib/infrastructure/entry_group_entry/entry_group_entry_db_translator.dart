import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_group_entry/entry_group_entry_db_contract.dart';

class EntryGroupEntryDbTranslator implements DbTranslator<EntryGroupEntry> {
  const EntryGroupEntryDbTranslator();

  @override
  Set<Field> get nestedModelFields => tagNestedModelFields;

  @override
  Future<EntryGroupEntry> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return EntryGroupEntry(
      id: fields[EntryGroupEntryField.id]?.value(map),
      remoteId: fields[EntryGroupEntryField.remoteId]?.value(map),
      entry: await fields[EntryGroupEntryField.entry]?.nested(context, map),
      createFrom:
          await fields[EntryGroupEntryField.createFrom]?.nested(context, map),
      entryGroup:
          await fields[EntryGroupEntryField.entryGroup]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
    DbContext context,
    EntryGroupEntry model,
  ) async {
    final map = <String, dynamic>{};
    const contract = EntryGroupEntryDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);

    await saveNestedModel<Entry>(
        context, map, contract.entryId, context.db.entry, model.entry);
    await saveNestedModel<EntryGroup>(context, map, contract.entryGroupId,
        context.db.entryGroup, model.entryGroup);
    await saveNestedModel<Entry>(context, map, contract.createFromId,
        context.db.entry, model.createFrom);

    return map;
  }
}
