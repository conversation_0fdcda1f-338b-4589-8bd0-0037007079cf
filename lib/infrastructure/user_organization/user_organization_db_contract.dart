import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class UserOrganizationDbContract extends DbContract {
  static const String _ = 'uo_';
  static const String _tableName = 'userOrganization';

  final String userId = '${_}userId';
  final String organizationId = '${_}organizationId';

  const UserOrganizationDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersion;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $userId INTEGER NOT NULL,
    $organizationId INTEGER NOT NULL,
    PRIMARY KEY ($userId, $organizationId)
  )
  ''';
}
