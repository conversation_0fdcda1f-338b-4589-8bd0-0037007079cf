import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/util/date_utils.dart';

class ProductApiTranslator implements ModelTranslator<Product> {
  const ProductApiTranslator();

  @override
  Product fromMap(Map<String, dynamic> data) {
    List<User>? users;
    if (data['user_ids'] != null && data['user_ids'] is List) {
      users = (data['user_ids'] as List)
          .map((userId) => User(remoteId: RemoteId(userId)))
          .toList();
    }

    return Product(
      remoteId: null,
      uuid: data['product_uuid'] != null
          ? ProductUuid(data['product_uuid'])
          : null,
      description: data['product_description'] != null
          ? ProductDescription(data['product_description'])
          : null,
      quantity:
          data['quantity'] != null ? ProductQuantity(data['quantity']) : null,
      mode: data['mode'] != null ? ProductMode(data['mode']) : null,
      expirationDate: data['expiration_date'] != null
          ? ProductExpirationDate(getDateTimeFromApi(data['expiration_date']))
          : null,
      organization: Organization(id: LocalId(data['organization_id'])),
      users: users,
    );
  }

  @override
  Map<String, dynamic> toMap(Product model) => throw UnimplementedError();
}
