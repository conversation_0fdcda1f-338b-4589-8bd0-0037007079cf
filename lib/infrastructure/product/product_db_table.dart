import 'package:bitacora/application/sync/machine/steps/download/collection/user/user_by_remote_id_repository_query.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/product/product_repository.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/product/product_db_contract.dart';
import 'package:bitacora/infrastructure/product/product_db_fields_builder.dart';
import 'package:bitacora/infrastructure/product/product_db_translator.dart';
import 'package:bitacora/infrastructure/product_user/product_user_db_contract.dart';
import 'package:bitacora/infrastructure/user/user_db_contract.dart';
import 'package:sqflite/sqflite.dart';

class ProductDbTable extends DbTable<Product, ProductDbFieldsBuilder>
    implements ProductRepository<DbContext, ProductDbFieldsBuilder> {
  ProductDbTable() : super();

  final ProductUserDbContract productUserContract =
      const ProductUserDbContract();
  final UserDbContract userContract = const UserDbContract();

  @override
  ProductDbContract get contract => const ProductDbContract();

  @override
  DbTranslator<Product> get translator => const ProductDbTranslator();

  @override
  ProductDbFieldsBuilder get fieldsBuilder => ProductDbFieldsBuilder();

  @override
  Future<List<Product>> findAll(DbContext context) async {
    return query(context);
  }

  @override
  Future<List<Product>> findByOrganization(
    DbContext context,
    LocalId organizationId,
  ) async {
    return query(
      context,
      where: '${contract.organizationId} = ?',
      whereArgs: [organizationId.dbValue],
    );
  }

  @override
  Future<List<User>> findUsersByProductId(
    DbContext context,
    LocalId productId,
  ) async {
    final executor = await context.executor;
    final maps = await executor.rawQuery(
      '''
      SELECT ${columnsForSelect(context.fields!)} FROM user u
      INNER JOIN ${productUserContract.tableName} pu
      ON u.u_id = pu.${productUserContract.userId}
      WHERE pu.${productUserContract.productId} = ?
      ''',
      [productId.dbValue],
    );
    return Future.wait(
        maps.map((map) => context.db.user.translator.fromDb(context, map)));
  }

  @override
  Future<bool> hasProduct(
    DbContext context,
    LocalId userId,
    LocalId organizationId,
    ProductUuid productUuid,
  ) async {
    final executor = await context.executor;
    final data = await executor.rawQuery(
      '''
      SELECT ${contract.id} FROM $tableName
      INNER JOIN ${productUserContract.tableName}
        ON ${contract.id} = ${productUserContract.productId}
      WHERE ${contract.uuid} = ? 
        AND ${productUserContract.userId} = ?
        AND ${contract.organizationId} = ?
      ''',
      [productUuid.dbValue, userId.dbValue, organizationId.dbValue],
    );

    return data.isNotEmpty;
  }

  @override
  Future<void> onSaved(DbContext context, Mutation<Product> mutation) async {
    final model = mutation.model!;

    if (model.users != null) {
      await _saveUserRelations(context, mutation.id!, model.users!);
    }

    return super.onSaved(context, mutation);
  }

  Future<void> _saveUserRelations(
    DbContext context,
    LocalId productId,
    List<User> users,
  ) async {
    await _deleteUserRelations(context, productId);

    final executor = await context.executor;
    for (final user in users) {
      final dbUser = await context.db.query(
        UserByRemoteIdRepositoryQuery(user.remoteId!),
        context: context,
      );

      if (dbUser == null) {
        continue;
      }

      await executor.insert(
        productUserContract.tableName,
        {
          productUserContract.productId: productId.dbValue,
          productUserContract.userId: dbUser.id!.dbValue,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
  }

  @override
  Future<void> onPostDelete(DbContext context, LocalId id) async {
    await super.onPostDelete(context, id);

    await _deleteUserRelations(context, id);
  }

  Future<void> _deleteUserRelations(DbContext context, LocalId id) async {
    final executor = await context.executor;
    await executor.delete(
      productUserContract.tableName,
      where: '${productUserContract.productId} = ?',
      whereArgs: [id.dbValue],
    );
  }
}
