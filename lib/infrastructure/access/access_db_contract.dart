import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class AccessDbContract extends DbContract {
  static const String _ = 'ac_';
  static const String _tableName = 'access';

  final String id = '${_}id';
  final String resourceType = '${_}resourceType';
  final String resourceId = '${_}resourceId';
  final String permission = '${_}permission';
  final String rules = '${_}rules';

  const AccessDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersion;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $resourceType INTEGER NOT NULL,
    $resourceId INTEGER NOT NULL,
    $permission INTEGER NOT NULL,
    $rules TEXT,
    UNIQUE ($resourceId, $resourceType)
  ) 
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithAccessRules) {
      await db.execute('ALTER TABLE $tableName ADD $rules TEXT');
    }
  }
}
