import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';

class AccessListForReplaceDbQuery {
  static Fields? _fields;

  const AccessListForReplaceDbQuery();

  Future<List<Access>> run(RepositoryQueryContext context) async {
    return context.db.access
        .findAll(context.copyWith(fields: _getFields(context.db)));
  }

  Fields _getFields(Repository db) {
    if (_fields != null) {
      return _fields!;
    }

    _fields = db.access.fieldsBuilder
        .resourceId()
        .resourceType()
        .permission()
        .rules()
        .build();
    return _fields!;
  }
}
