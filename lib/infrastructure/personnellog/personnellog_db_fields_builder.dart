import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/personnellog/personnellog.dart';
import 'package:bitacora/domain/personnellog/personnellog_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';

class PersonnellogDbFieldsBuilder extends DbFieldsBuilder
    implements PersonnellogFieldsBuilder {
  PersonnellogDbFieldsBuilder() {
    _id();
  }

  PersonnellogDbContract get contract => const PersonnellogDbContract();

  PersonnellogDbFieldsBuilder _id() {
    addField(
      PersonnellogField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  PersonnellogDbFieldsBuilder remoteId() {
    addField(
      PersonnellogField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  PersonnellogDbFieldsBuilder name() {
    addField(
      PersonnellogField.name,
      DbField(
        column: contract.name,
        valueBuilder: (v) => PersonnellogName(v),
      ),
    );
    return this;
  }

  @override
  PersonnellogDbFieldsBuilder sublocation() {
    addField(
      PersonnellogField.sublocation,
      DbField(
        column: contract.sublocation,
        valueBuilder: (v) => PersonnellogSublocation(v),
      ),
    );
    return this;
  }

  @override
  PersonnellogDbFieldsBuilder entrance() {
    addField(
      PersonnellogField.entrance,
      DbField(
        column: contract.entrance,
        valueBuilder: (v) => PersonnellogEntrance(v),
      ),
    );
    return this;
  }

  @override
  PersonnellogDbFieldsBuilder exit() {
    addField(
      PersonnellogField.exit,
      DbField(
        column: contract.exit,
        valueBuilder: (v) => PersonnellogExit(v),
      ),
    );
    return this;
  }

  @override
  PersonnellogDbFieldsBuilder minutes() {
    addField(
      PersonnellogField.minutes,
      DbField(
        column: contract.minutes,
        valueBuilder: (v) => PersonnellogMinutes(v),
      ),
    );
    return this;
  }

  @override
  PersonnellogDbFieldsBuilder project(Fields nestedFields) {
    addField(
      PersonnellogField.project,
      DbField(
        key: PersonnellogField.project,
        column: contract.projectId,
        nestedFields: nestedFields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.project.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
