import 'package:bitacora/domain/common/ai_generator.dart';
import 'package:bitacora/infrastructure/entry/entry_vertex_generator.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';

class VertexAiGenerator extends AiGenerator {
  // ignore: deprecated_member_use
  final model = FirebaseVertexAI.instance
      .generativeModel(model: 'gemini-2.5-flash');

  @override
  final EntryVertexGenerator entry = EntryVertexGenerator();

  VertexAiGenerator(super.analyticsLogger);

  /// FIXME: Implement `generate` method.
}
