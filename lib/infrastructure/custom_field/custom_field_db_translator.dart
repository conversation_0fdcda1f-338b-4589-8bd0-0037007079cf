import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_db_contract.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';

class CustomFieldDbTranslator implements DbTranslator<CustomField> {
  const CustomFieldDbTranslator();

  @override
  Set<Field> get nestedModelFields => customFieldNestedModelFields;

  @override
  Future<CustomField> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return CustomField(
      id: fields[CustomFieldField.id]?.value(map),
      remoteId: fields[CustomFieldField.remoteId]?.value(map),
      name: fields[CustomFieldField.name]?.value(map),
      type: fields[CustomFieldField.type]?.value(map),
      deletedAt: fields[CustomFieldField.deletedAt]?.value(map),
      updatedAt: fields[CustomFieldField.updatedAt]?.value(map),
      createdAt: fields[CustomFieldField.createdAt]?.value(map),
      allowedValues:
          await fields[CustomFieldField.allowedValues]?.nested(context, map),
      parent: await fields[CustomFieldField.parent]?.nested(context, map),
      organization:
          await fields[CustomFieldField.organization]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, CustomField model) async {
    final map = <String, dynamic>{};
    const contract = CustomFieldDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.name, model.name);
    addField(map, contract.type, model.type);
    addField(map, contract.deletedAt, model.deletedAt);
    addField(map, contract.updatedAt, model.updatedAt);
    addField(map, contract.createdAt, model.createdAt);

    await saveNestedModel<CustomField>(
      context,
      map,
      contract.parentId,
      context.db.customField,
      model.parent,
    );

    await saveNestedModel<Organization>(context, map, contract.organizationId,
        context.db.organization, model.organization);
    return map;
  }
}
