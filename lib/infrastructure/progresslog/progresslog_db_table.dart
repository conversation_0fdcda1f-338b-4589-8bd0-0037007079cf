import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/query/entry_common_db_queries.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/progresslog/progresslog_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_fields_builder.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_translator.dart';
import 'package:bitacora/util/logger/logger.dart';

class ProgresslogDbTable
    extends DbTable<Progresslog, ProgresslogDbFieldsBuilder>
    implements ProgresslogRepository<DbContext, ProgresslogDbFieldsBuilder> {
  @override
  ProgresslogDbContract get contract => const ProgresslogDbContract();

  @override
  DbTranslator<Progresslog> get translator => const ProgresslogDbTranslator();

  @override
  ProgresslogDbFieldsBuilder get fieldsBuilder => ProgresslogDbFieldsBuilder();

  @override
  Future<void> onSaved(
      DbContext context, Mutation<Progresslog> mutation) async {
    final parentEntryId = await _getParentEntryId(context, mutation.model!);
    if (parentEntryId == null) {
      logger.f('db:progresslog Parent entry not found when saving.');
      return;
    }

    final progress = mutation.model?.progress?.value;
    if (progress != null) {
      await _fixOpenState(context, parentEntryId, progress, false);
    }
  }

  Future<LocalId?> _getParentEntryId(
      DbContext context, Progresslog model) async {
    if (model.entry!.id != null) {
      return model.entry!.id;
    } else if (model.entry!.remoteId != null) {
      final parentEntry = await context.db.query(
        EntryIdRepositoryQuery(remoteId: model.entry!.remoteId!),
        context: context,
      );
      return parentEntry!.id;
    }
    return null;
  }

  @override
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    final model = await find(
      context.copyWith(
          fields: fieldsBuilder
              .entry(context.db.entry.fieldsBuilder.build())
              .build()),
      id,
    );
    final parentEntryId = model!.entry!.id!;

    final maxProgress =
        await _findMaxProgress(context, parentEntryId, idToDelete: id);
    await _fixOpenState(context, parentEntryId, maxProgress, true);
  }

  Future<int> _findMaxProgress(
    DbContext context,
    LocalId parentEntryId, {
    LocalId? idToDelete,
  }) async {
    // FIXME: switch to a simple query when we get rid of loose extensions.
    const entryDbContract = EntryDbContract();
    final fields = fieldsBuilder.progress().build();
    final list = await rawQuery(
      context.copyWith(fields: fields),
      '''
      SELECT ${columnsForSelect(fields)} FROM $tableName
      INNER JOIN ${entryDbContract.tableName}
      ON ${entryDbContract.extensionId} = $idColumn
      AND ${entryDbContract.extensionType} = ?
      WHERE ${contract.entryId} = ?
      ORDER BY ${contract.progress} DESC
      LIMIT 2
      ''',
      [ExtensionType.progresslog.dbValue, parentEntryId.dbValue],
    );

    if (idToDelete != null) {
      list.removeWhere((e) => e.id == idToDelete);
    }

    return list.isEmpty ? 0 : list.first.progress!.value;
  }

  Future<void> _fixOpenState(
    DbContext context,
    LocalId parentEntryId,
    int progress,
    bool isForced,
  ) async {
    final parentEntry =
        await const EntryOpenProgressDbQuery().run(context, parentEntryId);
    if (parentEntry?.openState == null) {
      logger.f('db:progresslog Parent entry open state not found.');
      return;
    }
    if (isForced || parentEntry!.openState!.progress!.value < progress) {
      await context.db.entry.save(
        context,
        Entry(
          id: parentEntryId,
          openState: OpenState(
            id: parentEntry!.openState!.id,
            progress: OpenStateProgress(progress),
          ),
        ),
      );
    }
  }
}
