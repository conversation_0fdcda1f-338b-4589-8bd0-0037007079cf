import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';

class PersonDetailApiTranslator implements ModelTranslator<PersonDetail> {
  const PersonDetailApiTranslator();

  @override
  PersonDetail fromMap(Map<String, dynamic> data) {
    return PersonDetail(
      remoteId: RemoteId(data['id']),
      area: PersonDetailArea(data['area']),
      company: PersonDetailCompany(data['company']),
      description: PersonDetailDescription(data['description']),
      person: Person(remoteId: RemoteId(data['person_id'])),
      organization: Organization(remoteId: RemoteId(data['organization_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(PersonDetail model) {
    throw 'Unimplemented';
  }
}
