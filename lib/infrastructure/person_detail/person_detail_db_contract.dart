import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class PersonDetailDbContract extends DbContract {
  static const String _ = 'psd_';
  static const String _tableName = 'personDetail';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String area = '${_}area';
  final String company = '${_}company';
  final String description = '${_}description';
  final String personId = '${_}personId';
  final String organizationId = '${_}organizationId';

  const PersonDetailDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithPersonTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $area TEXT,
    $company TEXT,
    $description TEXT,
    $personId INTEGER NOT NULL,
    $organizationId INTEGER NOT NULL
  )
  ''';
}
