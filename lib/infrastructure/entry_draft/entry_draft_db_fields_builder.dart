import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/domain/entry_draft/entry_draft_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_contract.dart';

class EntryDraftDbFieldsBuilder extends DbFieldsBuilder
    implements EntryDraftFieldsBuilder {
  EntryDraftDbFieldsBuilder() {
    _id();
  }

  EntryDraftDbContract get contract => const EntryDraftDbContract();

  EntryDraftDbFieldsBuilder _id() {
    addField(
      EntryDraftField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  EntryDraftDbFieldsBuilder type() {
    addField(
      EntryDraftField.type,
      DbField(
        column: contract.type,
        valueBuilder: (v) => EntryDraftType.fromDbValue(v ?? 1),
      ),
    );
    return this;
  }

  @override
  EntryDraftDbFieldsBuilder entry(Fields fields) {
    addField(
      EntryDraftField.entry,
      DbField(
        key: EntryDraftField.entry,
        column: contract.entryId,
        nestedFields: fields as DbFields,
        multiNestedBuilder: (context, values, props) async {
          final id = values[contract.entryId];
          if (id == null) {
            return null;
          }
          return context.db.entry.find(
            context.nested(EntryDraftField.entry, props: props)!,
            LocalId(id),
          );
        },
      ),
    );
    return this;
  }

  @override
  EntryDraftDbFieldsBuilder createdAt() {
    addField(
      EntryDraftField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) =>
            EntryDraftCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  EntryDraftDbFieldsBuilder updatedAt() {
    addField(
      EntryDraftField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) =>
            EntryDraftUpdatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }
}
