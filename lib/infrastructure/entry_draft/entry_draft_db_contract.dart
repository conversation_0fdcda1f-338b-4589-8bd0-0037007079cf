import 'dart:async';
import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class EntryDraftDbContract extends DbContract {
  static const String _ = 'ed_';
  static const String _tableName = 'entryDraft';

  final String id = '${_}id';

  const EntryDraftDbContract() : super(_, _tableName);

  String get entryId => '${_}entryId';

  String get createdAt => '${_}createdAt';

  String get updatedAt => '${_}updatedAt';

  String get type => '${_}type';

  @override
  int get initialDbVersion => kDbVersionWithEntryDraftTable;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $entryId INTEGER NOT NULL,
    $createdAt INTEGER NOT NULL,
    $updatedAt INTEGER NOT NULL,
    $type INTEGER NOT NULL DEFAULT 1
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithEntryDraftTableType &&
        oldVersion >= kDbVersionWithEntryDraftTable) {
      await db.execute(
          'ALTER TABLE $_tableName ADD $type INTEGER NOT NULL DEFAULT 1');
    }
  }
}
