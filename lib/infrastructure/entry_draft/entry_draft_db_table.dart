import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/domain/entry_draft/entry_draft_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_contract.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_draft/entry_draft_db_translator.dart';

class EntryDraftDbTable extends DbTable<EntryDraft, EntryDraftDbFieldsBuilder>
    implements EntryDraftRepository<DbContext, EntryDraftDbFieldsBuilder> {
  final DbTranslator<EntryDraft> _translator;

  EntryDraftDbTable([this._translator = const EntryDraftDbTranslator()])
      : super(isSyncable: false);

  @override
  EntryDraftDbContract get contract => const EntryDraftDbContract();

  @override
  EntryDraftDbFieldsBuilder get fieldsBuilder => EntryDraftDbFieldsBuilder();

  @override
  DbTranslator<EntryDraft> get translator => _translator;

  @override
  Future<EntryDraft?> findByEntry(DbContext context, LocalId entryId) async {
    final rows = await query(
      context,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );

    if (rows.isEmpty) {
      return null;
    }
    return rows.first;
  }

  @override
  Future<void> deleteByEntry(DbContext context, LocalId entryId) async {
    final executor = await context.executor;
    await executor.delete(
      contract.tableName,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );
  }
}
