import 'package:bitacora/domain/avatar/avatar.dart';
import 'package:bitacora/domain/avatar/avatar_repository.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/infrastructure/avatar/avatar_db_contract.dart';
import 'package:bitacora/infrastructure/avatar/avatar_db_fields_builder.dart';
import 'package:bitacora/infrastructure/avatar/avatar_db_translator.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';

class AvatarDbTable extends DbTable<Avatar, AvatarDbFieldsBuilder>
    implements AvatarRepository<DbContext, AvatarDbFieldsBuilder> {
  AvatarDbTable() : super() {
    addIdResolver(_personDetailIdResolver);
  }

  Future<LocalId?> _personDetailIdResolver(
      DbContext context, Avatar model) async {
    if (model.personDetailId == null) {
      return null;
    }

    final result = await (await context.executor).query(
      tableName,
      columns: [idColumn],
      where: '${contract.personDetailId} = ?',
      whereArgs: [model.personDetailId!.dbValue],
      limit: 1,
    );

    return result.isEmpty ? null : LocalId(result[0][idColumn] as int);
  }

  @override
  AvatarDbContract get contract => const AvatarDbContract();

  @override
  AvatarDbFieldsBuilder get fieldsBuilder => AvatarDbFieldsBuilder();

  @override
  DbTranslator<Avatar> get translator => const AvatarDbTranslator();

  @override
  Future<Avatar?> findByPersonDetailId(
      DbContext context, LocalId personDetailId) {
    return takeFirst(query(
      context,
      where: '${contract.personDetailId} = ?',
      whereArgs: [personDetailId.dbValue],
    ));
  }
}
