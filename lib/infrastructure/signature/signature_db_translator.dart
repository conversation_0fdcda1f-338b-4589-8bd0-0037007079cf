import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/signature/signature_db_contract.dart';

class SignatureDbTranslator implements DbTranslator<Signature> {
  const SignatureDbTranslator();

  @override
  Set<Field> get nestedModelFields => signatureNestedModelFields;

  @override
  Future<Signature> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Signature(
      id: fields[SignatureField.id]?.value(map),
      remoteId: fields[SignatureField.remoteId]?.value(map),
      doodle: fields[SignatureField.doodle]?.value(map),
      s3Key: fields[SignatureField.s3Key]?.value(map),
      comments: fields[SignatureField.comments]?.value(map),
      status: fields[SignatureField.status]?.value(map),
      ownerName: fields[SignatureField.ownerName]?.value(map),
      ownerEmail: fields[SignatureField.ownerEmail]?.value(map),
      location: await fields[SignatureField.location]?.nested(context, map),
      entry: await fields[SignatureField.entry]?.nested(context, map),
      user: await fields[SignatureField.user]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Signature model) async {
    final map = <String, dynamic>{};
    const contract = SignatureDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.doodle, model.doodle);
    addField(map, contract.s3Key, model.s3Key);
    addField(map, contract.comments, model.comments);
    addField(map, contract.status, model.status);
    addField(map, contract.ownerEmail, model.ownerEmail);
    addField(map, contract.ownerName, model.ownerName);

    if (model.location != null) {
      map[contract.latitude] = model.location!.value?.latitude;
      map[contract.longitude] = model.location!.value?.longitude;
    }
    await saveNestedModel<Entry>(
        context, map, contract.entryId, context.db.entry, model.entry);
    await saveNestedModel<User>(
        context, map, contract.userId, context.db.user, model.user);
    return map;
  }
}
