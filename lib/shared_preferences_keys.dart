// SharedPreferences
class SharedPreferencesKeys {
  static const String authToken = 'auth_token';
  static const String activeUserId = 'local_user_id';
  static const String activeOrganizationId = 'active_org_id';
  static const String activeProjectId = 'active_projectId';
  static const String activeLogDay = 'active_log_day';
  static const String theme = 'theme';
  static const String defaultExtensionType = 'default_extension_type';
  static const String defaultWorklogType = 'default_worklog_type';
  static const String defaultTemplate = 'default_template';
  static const String defaultPersonnellogIsHours =
      'default_personnellog_is_hours';
  static const String defaultInventorylogType = 'default_inventorylog_type';
  static const String defaultPriceIsUnit = 'default_price_is_unit';
  static const String defaultCameraLens = 'default_camera_lens';
  static const String defaultFlashMode = 'default_flash_mode';
  static const String lastSyncTime = 'last_sync_time';
  static const String fcmTopicSubscriptions = 'fcm_topic_subscriptions';
  static const String hasDirtyDb = 'has_dirty_database';
  static const String hasDirtyEntryRepository = 'has_dirty_entry_repository';
  static const String hasPendingSync = 'has_pending_sync';
  static const String hasPendingSuperSync = 'has_pending_super_sync';
  static const String entriesLocationEnabled = 'entries_location_enabled';
  static const String mapType = 'map_type';
  static const String foregroundLogFilename = 'foregroundLogFilename';
  static const String isAudioRecordingSpeechToTextEnabled =
      'is_audio_recording_speech_to_text_enabled';
  static const String isOpenStateEntryDayOfNotificationsEnabled =
      'is_open_state_day_of_entry_notifications_enabled';
  static const String isOpenStateEntryDayBeforeNotificationsEnabled =
      'is_open_state_entry_day_before_notifications_enabled';
  static const String openStateEntryDayOfNotificationLogTime =
      'open_state_entry_day_of_notification_log_time';
  static const String openStateEntryDayBeforeNotificationLogTime =
      'open_state_entry_day_before_notification_log_time';
  static const String dailyReminderNotificationEnabledDays =
      'daily_reminder_notification_activated_days';
  static const String dailyReminderNotificationLogTime =
      'daily_reminder_notification_log_time';
  static const String myFirstReportWasCreated = 'my_first_report_was_created';
  static const String myFirstReportWasShown = 'my_first_report_was_shown';
  static const String showFloatingActionMenuLongPressArcText =
      'show_floating_action_menu_long_pres_arc_text';
  static const String openEntriesSlidingPanelFilter =
      'open_entries_sliding_panel_filter';
  static const String experimentalModeEnabled = 'experimental_mode_enabled';
  static const String onboardingWasSeen = 'onboarding_was_seen';
  static const String dbCreateAppVersion = 'db_create_app_version';
  static const String dbCreateDateTime = 'db_create_date_time';
  static const String remediateLostAttachments = 'remediate_lost_attachments';
  static const String maxTrackingDuration = 'max_tracking_duration';
  static const String invalidSessionStatusCode = 'invalid_session_status_code';
  static const String forceSyncUsers = 'force_sync_users';
  static const String forceSyncPersonDetails = 'force_person_details';
  static const String needForceIosNotificationPermission =
      'need_force_ios_notification_permission';
  static const String supportData = 'support_data';
  static const String currentTemplateDirectoryId =
      'current_template_directory_id';
  static const String aiInstructions = 'ai_instructions';

  static const String devApiMitmStatusCode = 'dev_api_mitm_status_code';
  static const String devApiMitmSlowdownMs = 'dev_api_mitm_slowdown_ms';
  static const String devFailEntryUpload = 'dev_fail_entry_upload';
}
