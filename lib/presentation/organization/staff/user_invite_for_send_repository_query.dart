import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/user_invite/user_invite.dart';

class UserInviteToSendRepositoryQuery extends RepositoryQuery<UserInvite?> {
  final LocalId id;

  const UserInviteToSendRepositoryQuery({required this.id});

  @override
  Future<UserInvite?> run(RepositoryQueryContext context) =>
      context.db.userInvite.find(context, id);

  @override
  Fields fields(Repository db) => db.userInvite.fieldsBuilder
      .email()
      .organization(db.organization.fieldsBuilder.remoteId().build())
      .build();
}
