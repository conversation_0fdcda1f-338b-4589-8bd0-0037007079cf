import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/router.dart';
import 'package:bitacora/presentation/feed/feed_page.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class OrganizationPage extends StatelessWidget {
  const OrganizationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final activeOrg = context.watch<ActiveOrganization>();

    return Scaffold(
      appBar: AppBar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: kPageInsets.copyWith(bottom: 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activeOrg.value!.name!.displayValue,
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16.0),
          ListTile(
            title: Text(AppLocalizations.of(context)!.staff),
            leading: const Icon(Icons.group_outlined),
            onTap: () {
              Navigator.of(context).pushNamed(kRouteStaff);
            },
          ),
          ListTile(
            title: Text(AppLocalizations.of(context)!.posts),
            leading: const Icon(Icons.newspaper_outlined),
            onTap: () {
              Navigator.of(context)
                  .pushNamed(kRouteFeed, arguments: FeedPageArguments());
            },
          ),
          ListTile(
            title: Text(AppLocalizations.of(context)!.resources),
            leading: const Icon(Icons.inventory_2_outlined),
            onTap: () {
              Navigator.of(context).pushNamed(kRouteFeed,
                  arguments: FeedPageArguments(tabIndex: 1));
            },
          ),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Divider(),
          ),
          ListTile(
            title: Text(
              AppLocalizations.of(context)!.switchOrganization,
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
            leading: Icon(Icons.swap_horiz,
                color: Theme.of(context).colorScheme.error),
            onTap: () {
              Navigator.of(context).pushNamed(kRouteOrganizations);
            },
          ),
        ],
      ),
    );
  }
}
