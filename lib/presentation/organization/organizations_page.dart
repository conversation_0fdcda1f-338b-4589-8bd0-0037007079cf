import 'dart:async';

import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/organization/value/organization_user_has_seen.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/widgets/circle_check.dart';
import 'package:bitacora/presentation/widgets/menu_list.dart';
import 'package:bitacora/presentation/widgets/menu_list_item.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/navigator_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class OrganizationsPage extends StatelessWidget {
  const OrganizationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.organizations),
      ),
      body: SafeArea(
        child: Consumer<OrganizationCache>(
          builder: (_, organizationCache, __) => MenuList(
            title: AppLocalizations.of(context)!.activeOrganization,
            itemCount: organizationCache.value?.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              final organization = organizationCache.value![index];
              final activeOrganization = context.watch<ActiveOrganization>();
              final isSelected =
                  activeOrganization.value?.id == organization.id;

              return MenuListItem(
                title: organization.name!.displayValue,
                titleLabel: organization.userHasSeen!.value!
                    ? const SizedBox()
                    : Container(
                        padding: const EdgeInsets.all(4.0),
                        decoration: BoxDecoration(
                          color: bitacoraRed,
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.new_.toUpperCase(),
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall!
                              .copyWith(
                                  color: Theme.of(context).canvasColor,
                                  fontWeight: FontWeight.bold),
                        ),
                      ),
                isSelected: isSelected,
                selectionColor: organization.color!.value,
                onTap: () => _onOrgTap(context, organization),
              );
            },
          ),
        ),
      ),
    );
  }

  void _onOrgTap(
    BuildContext context,
    Organization organization,
  ) async {
    logger.i('organization-page: switch org to [${organization.name}]');
    unawaited(context.read<ActiveOrganization>().set(organization));

    final navigatorState = Navigator.of(context);
    await _markOrgAsSeen(context.read<Repository>(), organization);

    Future.delayed(
      kCircleCheckAnimationDuration + const Duration(milliseconds: 100),
      () => NavigatorUtils().popUntilRoot(navigatorState),
    );
  }

  Future<void> _markOrgAsSeen(Repository db, Organization organization) async {
    await db.organization.save(
      db.context(),
      Organization(
        id: organization.id,
        userHasSeen: const OrganizationUserHasSeen(true),
      ),
    );
  }
}
