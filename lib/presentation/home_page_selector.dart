import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/application/notification/awesome_notifications_action_handler.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/presentation/daylog/daylog_page.dart';
import 'package:bitacora/presentation/login/login_page.dart';
import 'package:bitacora/presentation/onboarding/onboarding_page.dart';
import 'package:bitacora/presentation/organization/choose_organization_page.dart';
import 'package:bitacora/presentation/widgets/loading_indicator.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomePageSelectorWidget extends StatefulWidget {
  final LoginMode defaultLoginMode;

  const HomePageSelectorWidget({
    super.key,
    LoginMode? defaultLoginMode,
  }) : defaultLoginMode = defaultLoginMode ?? LoginMode.login;

  @override
  State<HomePageSelectorWidget> createState() => _HomePageSelectorWidgetState();
}

class _HomePageSelectorWidgetState extends State<HomePageSelectorWidget> {
  bool? onboardingWasSeen;
  LoginMode? onboardingLoginMode;

  @override
  void initState() {
    super.initState();
    _loadPrefs();
  }

  void _loadPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      onboardingWasSeen =
          prefs.getBool(SharedPreferencesKeys.onboardingWasSeen) ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final activeSession = context.watch<ActiveSession>();
    context
        .read<RecoverSessionLauncher>()
        .watchActiveSession(context, activeSession);
    AwesomeNotificationsActionHandler().registerHandlers(context);

    if (!activeSession.hasLoaded || onboardingWasSeen == null) {
      return const LoadingIndicatorPage();
    }

    if (activeSession.value == null) {
      if (!onboardingWasSeen!) {
        return OnboardingPage(
          resultHandler: (mode) {
            setState(() {
              onboardingWasSeen = true;
              onboardingLoginMode = mode;
            });
          },
        );
      }
      return LoginPage(mode: onboardingLoginMode ?? widget.defaultLoginMode);
    }

    final orgCache = context.watch<OrganizationCache>();
    if (orgCache.value == null) {
      return const LoadingIndicatorPage();
    }

    final activeOrg = context.watch<ActiveOrganization>();
    if (!activeOrg.hasLoaded ||
        (activeOrg.value == null && activeOrg.isLoading)) {
      return const LoadingIndicatorPage();
    }

    if (activeOrg.value == null) {
      // FIXME: hacky
      WidgetsBinding.instance.addPostFrameCallback(
          (_) => Navigator.of(context).popUntil(ModalRoute.withName('/')));
      return const ChooseOrganizationsPage();
    }
    return const DaylogPage();
  }
}
