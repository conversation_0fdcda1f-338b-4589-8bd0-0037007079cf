import 'package:bitacora/presentation/my_activity/my_activity_percent_chip.dart';
import 'package:flutter/material.dart';

class MyActivityCountTitle extends StatelessWidget {
  final String title;
  final double comparePercent;
  final String subtitle;

  const MyActivityCountTitle({
    super.key,
    required this.title,
    required this.comparePercent,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            MyActivityPercentChip(percent: comparePercent),
          ],
        ),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
