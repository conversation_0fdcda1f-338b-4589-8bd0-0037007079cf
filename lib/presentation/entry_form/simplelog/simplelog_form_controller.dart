import 'dart:async';

import 'package:bitacora/domain/common/payment_status.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/currency_value_object.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/value/project_name.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_context_snapshot.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/money/entry_form_money_option_controller.dart';
import 'package:bitacora/presentation/entry_form/simplelog/simplelog_form.dart';
import 'package:bitacora/presentation/entry_form/util/auto_fill_active_project.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class SimplelogFormController extends EntryFormController {
  final TextEditingController title = TextEditingController();

  late final EntryFormMoneyOptionController money;

  VoidCallback? onConvertToWorklog;

  SimplelogFormController(super.contextSnapshot, super.props);

  @override
  void dispose() {
    title.dispose();
    money.dispose();
    super.dispose();
  }

  @override
  void init(EntryFormControllerContextSnapshot contextSnapshot,
      ValueNotifier<Entry?> liveEntry) {
    super.init(contextSnapshot, liveEntry);

    money = EntryFormMoneyOptionController(liveEntry, TextEditingController());
  }

  @protected
  @override
  void read(Entry entry, [Entry? lastReadEntry]) {
    super.read(entry, lastReadEntry);

    final worklog = entry.worklog!;
    final lastReadSimple = lastReadEntry?.worklog!;
    readNotifier<TextEditingValue>(
      title,
      TextEditingValue(text: worklog.title?.displayValue ?? ''),
      TextEditingValue(text: lastReadSimple?.title!.displayValue ?? ''),
    );
    readNotifier<PaymentStatus>(
      money.paymentStatus,
      worklog.paymentStatus?.value,
      lastReadSimple?.paymentStatus!.value,
      PaymentStatus.na,
    );
    readNotifier<bool>(
      money.priceIsUnit,
      worklog.priceIsUnit?.value,
      lastReadSimple?.priceIsUnit!.value,
      false,
    );
    readNotifier<TextEditingValue>(
      money.income,
      TextEditingValue(text: worklog.salePrice?.displayValue ?? ''),
      TextEditingValue(text: lastReadSimple?.salePrice!.displayValue ?? ''),
    );
    readNotifier<TextEditingValue>(
      money.expense,
      TextEditingValue(text: worklog.costPrice?.displayValue ?? ''),
      TextEditingValue(text: lastReadSimple?.costPrice!.displayValue ?? ''),
    );
    readNotifier<TextEditingValue>(
      money.provider,
      TextEditingValue(text: worklog.provider?.displayValue ?? ''),
      TextEditingValue(text: lastReadSimple?.provider!.displayValue ?? ''),
    );
    money.isShowing.value = money.paymentStatus.value != PaymentStatus.na ||
        money.income.text.isNotEmpty ||
        money.expense.text.isNotEmpty ||
        money.provider.text.isNotEmpty;
  }

  @override
  Map<ValueNotifier, ValueObject?> get fields => {
        ...super.fields,
        title: liveEntry.value?.worklog!.title,
        comments: liveEntry.value?.comments,
      };

  @override
  Widget form() => props.isEditable
      ? SimplelogForm(controller: this)
      : NonEditableSimplelogForm(controller: this);

  @override
  List<EntryFormOptionController> get options => [
        if (signature != null) signature!,
        user,
        money,
        dateTime,
        location,
        tag,
        attachment,
      ];

  @override
  Widget header(BuildContext context) =>
      getHeader(context, ExtensionType.simplelog.displayName);

  @override
  Widget headerOption(BuildContext context, {bool isEditable = true}) =>
      TextButton(
        onPressed: onConvertToWorklog,
        child: Row(
          children: [
            const Icon(Icons.add, size: 16),
            const SizedBox(width: 2),
            Text(AppLocalizations.of(context)!.moreFields),
          ],
        ),
      );

  @override
  Future<List<Extension>> buildExtensionsForSave(
    RepositoryQueryContext context,
    Organization organization,
  ) async {
    return [
      Worklog(
        id: liveEntry.value?.worklog?.id,
        type: WorklogType(WorklogTypeValue.values.first),
        title: WorklogTitle(title.text),
        project: await buildProject(context, organization, kDefaultProjectName),
        costPrice:
            WorklogCostPrice(CurrencyValueObject.parse(money.expense.text)),
        salePrice:
            WorklogSalePrice(CurrencyValueObject.parse(money.income.text)),
        provider: WorklogProvider(money.provider.text),
        paymentStatus: WorklogPaymentStatus(money.paymentStatus.value),
        priceIsUnit: WorklogPriceIsUnit(money.priceIsUnit.value),
      )
    ];
  }

  @override
  Map<TextEditingController, AutoFillCacheGetter> get fillMap => {};
}
