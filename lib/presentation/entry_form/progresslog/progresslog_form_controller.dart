import 'package:bitacora/application/notification/open_entries/open_state_notification_scheduler.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_context_snapshot.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/personnellog/personnellog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/progresslog/progresslog_form.dart';
import 'package:bitacora/presentation/entry_form/simplelog/simplelog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/templatelog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/auto_fill_active_project.dart';
import 'package:bitacora/presentation/entry_form/worklog/worklog_form_controller.dart';
import 'package:bitacora/presentation/progress/percentage_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class ProgresslogFormController extends EntryFormController {
  late final EntryFormController proxyController;
  late final Percentage percentage;

  ProgresslogFormController(super.contextSnapshot, super.props);

  @override
  void dispose() {
    percentage.dispose();
    proxyController.dispose();
    super.dispose();
  }

  @protected
  @override
  void init(EntryFormControllerContextSnapshot contextSnapshot,
      ValueNotifier<Entry?> liveEntry) {
    super.init(contextSnapshot, liveEntry);

    proxyController = _getProxyController(contextSnapshot, liveEntry.value!);
    percentage = Percentage(liveEntry.value!.progresslog!.progress!.value);
  }

  @protected
  @override
  void read(Entry entry, [Entry? lastReadEntry]) {
    super.read(entry, lastReadEntry);

    readNotifier<int>(
      percentage,
      entry.progresslog!.progress!.value,
      lastReadEntry?.progresslog!.progress!.value,
    );
  }

  @override
  Map<ValueNotifier, ValueObject?> get fields => {
        ...super.fields,
        percentage: liveEntry.value?.id != null
            ? liveEntry.value!.progresslog?.progress
            : null,
        comments: liveEntry.value?.comments,
      };

  EntryFormController _getProxyController(
    EntryFormControllerContextSnapshot contextSnapshot,
    Entry inputEntry,
  ) {
    final parentEntry = inputEntry.progresslog!.entry!;
    final extensionType = parentEntry.extension!.extensionType;
    final props = EntryFormProps(
      inputEntry: parentEntry,
      isEditable: false,
      isOpenStateAllowed: false,
      isSignatureAllowed: false,
    );
    switch (extensionType) {
      case ExtensionType.worklog:
        if (parentEntry.hasSimplelog) {
          return SimplelogFormController(contextSnapshot, props);
        }
        return WorklogFormController(contextSnapshot, props);
      case ExtensionType.inventorylog:
        return InventorylogFormController(contextSnapshot, props);
      case ExtensionType.personnellog:
        return PersonnellogFormController(contextSnapshot, props);
      case ExtensionType.templatelog:
        final templatelog = parentEntry.extension! as Templatelog;
        return TemplatelogFormController(
          contextSnapshot,
          props,
          templatelog.template!,
        );
      default:
        throw 'Unexpected parent entry extension type';
    }
  }

  @override
  Future<List<Extension>> buildExtensionsForSave(
    RepositoryQueryContext context,
    Organization organization,
  ) async {
    return [
      Progresslog(
        id: liveEntry.value!.progresslog!.id,
        progress: ProgresslogProgress(percentage.value),
        entry: Entry(id: liveEntry.value!.progresslog!.entry!.id),
      )
    ];
  }

  @override
  Future<List<Entry>> save(EntryFormControllerSaveContextSnapshot context) {
    final scheduler = OpenStateNotificationScheduler();
    // FIXME: Cancel notifications from sync (This cancels only from UI)
    scheduler.cancelTodaysNotification(liveEntry.value!.progresslog!.entry!);
    return super.save(context);
  }

  @override
  Widget form() => ProgresslogForm(controller: this);

  @override
  Widget header(BuildContext context) => proxyController.header(context);

  @override
  Widget headerOption(BuildContext context, {bool isEditable = true}) =>
      proxyController.headerOption(context, isEditable: false);

  @override
  Map<TextEditingController, AutoFillCacheGetter> get fillMap => {};
}
