import 'package:bitacora/presentation/entry_form/templatelog/templatelog_form_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';

class TemplatelogForm extends StatefulWidget {
  final TemplatelogFormController controller;

  const TemplatelogForm({super.key, required this.controller});

  @override
  State<TemplatelogForm> createState() => _TemplatelogFormState();
}

class _TemplatelogFormState extends State<TemplatelogForm> {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.controller.isLoading,
      builder: (context, isLoading, _) {
        if (isLoading) {
          return Center(child: PlatformCircularProgressIndicator());
        }

        return _TemplatelogFormSkeleton(controller: widget.controller);
      },
    );
  }
}

class NonEditableTemplateForm extends StatelessWidget {
  final TemplatelogFormController controller;

  const NonEditableTemplateForm({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: Theme(
        data: Theme.of(context).copyWith(
          colorScheme:
              Theme.of(context).colorScheme.copyWith(surface: Colors.grey[300]),
          inputDecorationTheme: const InputDecorationTheme(
            contentPadding: EdgeInsets.only(),
            border: InputBorder.none,
            isDense: true,
          ),
        ),
        child: _TemplatelogFormSkeleton(
          controller: controller,
          isEditable: false,
        ),
      ),
    );
  }
}

class _TemplatelogFormSkeleton extends StatelessWidget {
  final TemplatelogFormController controller;
  final bool isEditable;

  const _TemplatelogFormSkeleton({
    required this.controller,
    this.isEditable = true,
  });

  @override
  Widget build(BuildContext context) {
    return isEditable
        ? SliverList.builder(
            itemBuilder: (context, i) {
              return controller.templateGroupControllers[i].form;
            },
            itemCount: controller.templateGroupControllers.length,
          )
        : Column(
            children: controller.templateGroupControllers
                .map((e) => e.form)
                .toList());
  }
}
