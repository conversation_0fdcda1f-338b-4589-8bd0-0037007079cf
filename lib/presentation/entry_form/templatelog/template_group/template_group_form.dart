import 'package:bitacora/application/template_condition/template_condition_evaluator.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/template_block_widget.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_group/template_group_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/util/parent_builder/parent_builder.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

class TemplateGroupForm extends StatelessWidget {
  final TemplateGroupFormController controller;

  const TemplateGroupForm({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return ParentBuilder(
      builder: (context, child) {
        if (controller.conditions?.isEmpty ?? true) {
          return child;
        } else {
          final condition = controller.conditions!.first;
          return ValueListenableBuilder(
            valueListenable: condition.value,
            builder: (context, value, _) {
              if (!TemplateConditionEvaluator()
                  .evaluate(value, condition.condition)) {
                return const SizedBox();
              }

              return child;
            },
          );
        }
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: kFormVerticalSpacing),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _TemplateGroupFormHeader(
                name: controller.templateGroup.name!.displayValue),
            ...formListSpacing(_buildRows()),
          ],
        ),
      ),
    );
  }

  List<Row> _buildRows() {
    int rowIndex = 0;
    final rows = <Row>[];
    final rowChildren = <Expanded>[];
    controller.blockControllers.forEachIndexed((i, blockController) {
      final isLast = i + 1 == controller.blockControllers.length;
      if (blockController.templateBlock.row!.value == rowIndex) {
        _addToRow(rowChildren, blockController, i, isLast);
      } else {
        rows.add(_buildRow(rowChildren));
        rowChildren.clear();
        _addToRow(rowChildren, blockController, i, isLast);
        rowIndex++;
      }
    });

    rows.add(_buildRow(rowChildren));
    return rows;
  }

  void _addToRow(
    List<Expanded> row,
    TemplateBlockFormController controller,
    int index,
    bool isLast,
  ) {
    row.add(
      Expanded(
        flex: controller.templateBlock.weight!.value!,
        child: TemplateBlockWidget(
          controller: controller,
          onFieldSubmitted: null,
          texInputAction: isLast ? TextInputAction.done : TextInputAction.next,
        ),
      ),
    );
  }

  Row _buildRow(List<Expanded> children) {
    return Row(children: formListSpacing([...children], false));
  }
}

class _TemplateGroupFormHeader extends StatelessWidget {
  final String name;

  const _TemplateGroupFormHeader({required this.name});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              name.isEmpty
                  ? const Divider()
                  : Row(
                      children: [
                        Expanded(
                          child: Stack(
                            children: [
                              const Positioned.fill(child: Divider()),
                              Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0),
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.surface,
                                  ),
                                  child: Text(
                                    name,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall!
                                        .copyWith(
                                          color: Theme.of(context).dividerColor,
                                        ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
              const SizedBox(height: 8.0),
            ],
          ),
        ),
      ],
    );
  }
}
