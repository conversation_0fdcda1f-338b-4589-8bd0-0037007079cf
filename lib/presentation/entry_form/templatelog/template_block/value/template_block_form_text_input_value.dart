import 'package:bitacora/presentation/entry_form/templatelog/template_block/value/template_block_form_value.dart';
import 'package:flutter/cupertino.dart';

class TemplateBlockFormTextInputValue extends TemplateBlockFormValue<String> {
  final TextEditingController inputController = TextEditingController();

  TemplateBlockFormTextInputValue(super.value) {
    inputController.addListener(_updateValue);
  }

  void _updateValue() {
    value = inputController.text;
  }

  @override
  void dispose() {
    inputController.removeListener(_updateValue);
    super.dispose();
  }
}
