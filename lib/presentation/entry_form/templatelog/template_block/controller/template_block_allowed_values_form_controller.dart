import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/value/template_block_form_allowed_value.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

class TemplateBlockAllowedValuesFormController
    extends TemplateBlockFormController<CustomFieldAllowedValue?,
        CustomFieldAllowedValue> {
  final TemplateBlockFormAllowedValueInputValue _value;

  final TextEditingController textController = TextEditingController();

  final List<CustomFieldAllowedValue> allowedValues;

  final EntryFormProps props;

  final TemplateBlockAllowedValuesFormController? parent;

  final ValueNotifier<List<CustomFieldAllowedValue>> suggestedAllowedValues =
      ValueNotifier([]);

  String _currentText = '';

  @protected
  TemplateBlockAllowedValuesFormController(
    super.templateBlock,
    this.props,
    this.parent,
  )   : _value = TemplateBlockFormAllowedValueInputValue(null),
        allowedValues =
            templateBlock.customFieldOptions!.customField!.allowedValues! {
    _value.addListener(_updateText);
    focusNode.addListener(_onFocusChange);
    textController.addListener(_onTextChanged);
    if (parent != null) {
      parent!.value.addListener(_determineAllowedValues);
    }
    _determineAllowedValues();
  }

  void dispose() {
    value.removeListener(_updateText);
    focusNode.removeListener(_onFocusChange);
    textController.removeListener(_onTextChanged);
    parent?.value.removeListener(_determineAllowedValues);
  }

  void _updateText() {
    textController.text = _value.value?.value?.displayValue ?? '';
  }

  void _onFocusChange() {
    if (!focusNode.hasFocus) {
      _maybeClear();
      return;
    }

    _determineAllowedValues();
  }

  void _maybeClear() {
    final allowedValue = allowedValues
        .firstWhereOrNull((v) => v.value!.value == textController.text);

    if (allowedValue == null) {
      if (_value.value == null) {
        textController.text = '';
      } else {
        _value.value = null;
      }
    }
  }

  void _onTextChanged() {
    if (textController.text != _currentText && focusNode.hasFocus) {
      _currentText = textController.text;
      _determineAllowedValues();
    }
  }

  void _determineAllowedValues() {
    suggestedAllowedValues.value =
        _maybeFilterByPatternName(_maybeFilterByParent(allowedValues));

    if (value.value != null &&
        !focusNode.hasFocus &&
        !suggestedAllowedValues.value
            .map((e) => e.id!)
            .contains(value.value!.id)) {
      value.value = null;
    }
  }

  List<CustomFieldAllowedValue> _maybeFilterByParent(
    List<CustomFieldAllowedValue> values,
  ) {
    if (parent == null) {
      return values;
    }

    final selectedParentAllowedValue = parent!.selectedAllowedValue;
    return selectedParentAllowedValue == null
        ? <CustomFieldAllowedValue>[]
        : allowedValues.where((e) {
            return e.parent?.id != null &&
                e.parent!.id! == selectedParentAllowedValue.id!;
          }).toList(growable: false);
  }

  List<CustomFieldAllowedValue> _maybeFilterByPatternName(
    List<CustomFieldAllowedValue> values,
  ) {
    return _currentText.isEmpty
        ? values
        : values
            .where((e) => e.value!.value
                .toLowerCase()
                .contains(_currentText.toLowerCase()))
            .toList(growable: false);
  }

  CustomFieldAllowedValue? get selectedAllowedValue =>
      allowedValues.firstWhereOrNull((e) => e.id! == _value.value?.id);

  @override
  TemplateBlockFormAllowedValueInputValue get value => _value;

  @override
  CustomFieldAllowedValue? get dbValue => _value.value;

  void setValue(CustomFieldAllowedValue? value) {
    if (value == _value.value &&
        textController.text != _value.value?.value?.displayValue) {
      _updateText();
      return;
    }

    _value.value = value;
    _updateText();
  }

  @override
  void read(
    EntryFormReadFunction<CustomFieldAllowedValue> onRead,
    CustomFieldMetadata? metadata,
    CustomFieldMetadata? lastMetadata,
    String emptyString,
  ) {
    onRead(
        _value, _buildAllowedValue(metadata), _buildAllowedValue(lastMetadata));
  }

  CustomFieldAllowedValue? _buildAllowedValue(CustomFieldMetadata? metadata) {
    if (metadata?.allowedValue != null) {
      return metadata!.allowedValue;
    }

    if ((metadata?.value?.value as String?)?.isEmpty ?? true) {
      return null;
    }

    return allowedValues.firstWhereOrNull((v) {
      return v.value!.value == metadata!.value?.value!;
    });
  }
}
