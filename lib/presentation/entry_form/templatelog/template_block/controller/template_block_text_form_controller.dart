import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/value/template_block_form_text_input_value.dart';
import 'package:bitacora/util/string/string_utils.dart';
import 'package:flutter/material.dart';

class TemplateBlockTextFormController
    extends TemplateBlockFormController<String, TextEditingValue> {
  @override
  final TemplateBlockFormTextInputValue value;

  final EntryFormProps props;

  @protected
  TemplateBlockTextFormController(super.templateBlock, this.props)
      : value = TemplateBlockFormTextInputValue('');

  @override
  String? get dbValue => value.value.isEmpty ? null : value.value;

  @override
  void read(
    EntryFormReadFunction<TextEditingValue> onRead,
    CustomFieldMetadata? metadata,
    CustomFieldMetadata? lastMetadata,
    String emptyString,
  ) {
    onRead(
      value.inputController,
      TextEditingValue(
          text: const StringUtils()
              .maybeEmptyString(metadata?.displayValue, emptyString)),
      TextEditingValue(
          text: const StringUtils()
              .maybeEmptyString(lastMetadata?.displayValue, emptyString)),
    );
  }
}
