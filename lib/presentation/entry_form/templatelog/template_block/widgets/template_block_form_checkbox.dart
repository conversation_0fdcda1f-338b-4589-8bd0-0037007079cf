import 'package:bitacora/l10n/app_localizations.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';
import 'package:flutter/material.dart';

class TemplateBlockFormCheckbox extends StatelessWidget {
  final TemplateBlockBoolFormController controller;

  const TemplateBlockFormCheckbox({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    if (!controller.props.isEditable) {
      return TextFormField(
        initialValue: controller.value.value
            ? AppLocalizations.of(context)!.yes
            : AppLocalizations.of(context)!.no,
        decoration: InputDecoration(
          labelText: getCustomFieldPlaceholder(
              controller.templateBlock.customFieldOptions!),
        ),
      );
    }

    return InkWell(
      borderRadius: const OutlineInputBorder().borderRadius,
      onTap: () {
        controller.value.value = !controller.value.value;
      },
      child: InputDecorator(
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(vertical: 2.0, horizontal: 4.0),
          errorStyle: const TextStyle(height: 0, fontSize: 0),
          prefixIconConstraints: const BoxConstraints(),
          prefixIcon: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 36.0,
                height: 40.0,
                child: ListenableBuilder(
                  listenable: controller.value,
                  builder: (_, __) => IgnorePointer(
                    child: Checkbox(
                      value: controller.value.value,
                      onChanged: (_) {},
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        child: SizedBox(
          child: Text(
            controller.templateBlock.customFieldOptions!.customField!.name!
                .displayValue,
            style: Theme.of(context).textTheme.bodySmall!,
          ),
        ),
      ),
    );
  }
}
