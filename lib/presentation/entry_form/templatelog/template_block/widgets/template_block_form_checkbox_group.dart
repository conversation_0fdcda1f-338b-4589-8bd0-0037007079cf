import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_multi_value_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';
import 'package:flutter/material.dart';

class TemplateBlockFormCheckboxGroup extends StatelessWidget {
  final TemplateBlockMultiValueFormController<CustomFieldAllowedValue>
      controller;

  const TemplateBlockFormCheckboxGroup({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final allowedValues = controller
            .templateBlock.customFieldOptions!.customField!.allowedValues ??
        [];
    if (!controller.props.isEditable) {
      return _buildReadOnlyView(context, allowedValues);
    }

    return _buildEditableView(context, allowedValues);
  }

  Widget _buildReadOnlyView(
      BuildContext context, List<CustomFieldAllowedValue> allowedValues) {
    return InputDecorator(
      decoration: InputDecoration(
        labelText: getCustomFieldPlaceholder(
            controller.templateBlock.customFieldOptions!),
      ),
      child: ValueListenableBuilder<List<CustomFieldAllowedValue>>(
        valueListenable: controller.value,
        builder: (context, selectedValues, _) {
          if (selectedValues.isEmpty) {
            return const Text('-');
          }

          final selectedAllowedValues = selectedValues.toList();

          final selectedLabels =
              selectedAllowedValues.map((av) => av.value!.value).join(', ');

          return Text(selectedLabels);
        },
      ),
    );
  }

  Widget _buildEditableView(
      BuildContext context, List<CustomFieldAllowedValue> allowedValues) {
    return InputDecorator(
      decoration: InputDecoration(
        labelText: getCustomFieldPlaceholder(
            controller.templateBlock.customFieldOptions!),
        contentPadding: const EdgeInsets.fromLTRB(12.0, 8.0, 12.0, 8.0),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4.0),
        ),
      ),
      child: ValueListenableBuilder<List<CustomFieldAllowedValue>>(
        valueListenable: controller.value,
        builder: (context, selectedValues, _) {
          return Wrap(
            spacing: 2.0,
            runSpacing: 4.0,
            alignment: WrapAlignment.start,
            children: allowedValues.map((allowedValue) {
              final isSelected = selectedValues
                  .any((av) => av.id!.value == allowedValue.id!.value);

              return _CheckboxItem(
                label: allowedValue.value!.value,
                isSelected: isSelected,
                onChanged: (_) => _toggleValue(allowedValue),
              );
            }).toList(),
          );
        },
      ),
    );
  }

  void _toggleValue(CustomFieldAllowedValue allowedValue) {
    final currentValues =
        List<CustomFieldAllowedValue>.from(controller.value.value);
    final index = currentValues
        .indexWhere((av) => av.id!.value == allowedValue.id!.value);

    if (index >= 0) {
      currentValues.removeAt(index);
    } else {
      currentValues.add(allowedValue);
    }

    controller.value.value = currentValues;
  }
}

class _CheckboxItem extends StatelessWidget {
  final String label;
  final bool isSelected;
  final Function(bool?)? onChanged;

  const _CheckboxItem({
    required this.label,
    required this.isSelected,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onChanged?.call(!isSelected),
      borderRadius: BorderRadius.circular(4.0),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 4.0),
        padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primaryContainer.withAlpha(30)
              : null,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 18.0,
              height: 18.0,
              child: Checkbox(
                value: isSelected,
                onChanged: onChanged,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              ),
            ),
            const SizedBox(width: 4.0),
            Flexible(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodySmall,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
