import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_allowed_values_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/suggestion_typeahead_allowed_values.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:flutter/material.dart';

class TemplateBlockFormSelect extends StatefulWidget {
  final TemplateBlockAllowedValuesFormController controller;
  final TextInputAction? texInputAction;
  final void Function(dynamic)? onFieldSubmitted;

  const TemplateBlockFormSelect({
    super.key,
    required this.controller,
    this.texInputAction,
    this.onFieldSubmitted,
  });

  @override
  State<TemplateBlockFormSelect> createState() =>
      _TemplateBlockFormSelectState();
}

class _TemplateBlockFormSelectState extends State<TemplateBlockFormSelect> {
  late final TemplateBlockAllowedValuesFormController? _parentController;
  late List<CustomFieldAllowedValue> _allowedValues;

  @override
  void initState() {
    super.initState();
    _parentController = widget.controller.parent;
    if (_parentController != null) {
      _parentController.value.addListener(_determineAllowedValues);
    }
    _determineAllowedValues();
  }

  @override
  void dispose() {
    super.dispose();

    _parentController?.value.removeListener(_determineAllowedValues);
    widget.controller.dispose();
  }

  void _determineAllowedValues() {
    if (_parentController == null) {
      _allowedValues = widget.controller.allowedValues;
      return;
    }

    final selectedParentAllowedValue = _parentController.selectedAllowedValue;
    _allowedValues = selectedParentAllowedValue == null
        ? <CustomFieldAllowedValue>[]
        : widget.controller.allowedValues.where((e) {
            return e.parent?.id != null &&
                e.parent!.id! == selectedParentAllowedValue.id!;
          }).toList(growable: false);

    if (widget.controller.value.value != null &&
        !_allowedValues
            .map((e) => e.id!)
            .contains(widget.controller.value.value!.id)) {
      widget.controller.value.value = null;
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.controller.props.isEditable) {
      return TextFormField(
        initialValue: widget.controller.value.value?.value?.value ?? '-',
        decoration: InputDecoration(
          labelText: getCustomFieldPlaceholder(widget.controller.templateBlock.customFieldOptions!),
        ),
      );
    }

    return _FormSelect(
      controller: widget.controller,
      allowedValues: _allowedValues,
      texInputAction: widget.texInputAction,
      onFieldSubmitted: widget.onFieldSubmitted,
    );
  }
}

class _FormSelect extends StatelessWidget {
  final TemplateBlockAllowedValuesFormController controller;
  final TextInputAction? texInputAction;
  final List<CustomFieldAllowedValue> allowedValues;
  final void Function(dynamic)? onFieldSubmitted;

  const _FormSelect({
    required this.controller,
    required this.allowedValues,
    this.texInputAction,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller.value,
      builder: (context, _) {
        return SuggestionTypeaheadAllowedValues(
          controller: controller,
          textFieldConfiguration: TypeaheadTextFieldConfiguration(
            focusNode: controller.focusNode,
            controller: controller.textController,
            textInputAction: texInputAction,
            textCapitalization: TextCapitalization.sentences,
            decoration: InputDecoration(
              labelText: controller.templateBlock.customFieldOptions!
                  .customField!.name!.displayValue,
            ),
            onSubmitted: onFieldSubmitted,
            validator: _validator,
          ),
          onSelect: controller.setValue,
        );
      },
    );
  }

  String? _validator(String? text) {
    if (text?.isEmpty ?? true) {
      return null;
    }

    return null;
  }
}
