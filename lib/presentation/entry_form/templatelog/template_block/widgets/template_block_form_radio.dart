import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_allowed_values_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

class TemplateBlockFormRadio extends StatelessWidget {
  final TemplateBlockAllowedValuesFormController controller;

  const TemplateBlockFormRadio({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return InputDecorator(
      decoration: InputDecoration(
        labelText: getCustomFieldPlaceholder(controller.templateBlock.customFieldOptions!),
        errorStyle: const TextStyle(height: 0, fontSize: 0),
      ),
      child: ListenableBuilder(
        listenable: controller.value,
        builder: (context, _) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: controller.allowedValues.map((e) {
              final groupValue = controller.allowedValues
                  .firstWhereOrNull((v) => v == controller.value.value);
              return _RadioItem(
                controller: controller,
                allowedValue: e,
                groupValue: groupValue,
              );
            }).toList(growable: false),
          );
        },
      ),
    );
  }
}

class _RadioItem extends StatelessWidget {
  final TemplateBlockAllowedValuesFormController controller;
  final CustomFieldAllowedValue allowedValue;
  final CustomFieldAllowedValue? groupValue;

  const _RadioItem({
    required this.controller,
    required this.allowedValue,
    required this.groupValue,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          height: 32,
          width: 32,
          child: Radio<CustomFieldAllowedValue?>(
            value: allowedValue,
            groupValue: groupValue,
            onChanged: (_) {
              controller.value.value = allowedValue;
            },
          ),
        ),
        Text(
          allowedValue.label!.value,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
