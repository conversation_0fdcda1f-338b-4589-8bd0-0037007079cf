import 'package:bitacora/presentation/entry_form/templatelog/custom_field_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';

class TemplateBlockFormText extends StatelessWidget {
  final TemplateBlockTextFormController controller;
  final TextInputAction? texInputAction;
  final void Function(dynamic)? onFieldSubmitted;

  const TemplateBlockFormText({
    super.key,
    required this.controller,
    this.texInputAction,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return NewableFieldSuggestionTypeahead(
      key: controller.fieldKey,
      suggestionsQuery: CustomFieldSuggestionRepositoryQuery(
          customFieldId:
              controller.templateBlock.customFieldOptions!.customField!.id!),
      queryScope: getOrgScope(context),
      onSelect: onFieldSubmitted,
      textFieldConfiguration: TypeaheadTextFieldConfiguration(
        validator: controller.validator,
        focusNode: controller.focusNode,
        controller: controller.value.inputController,
        textInputAction: texInputAction,
        textCapitalization: TextCapitalization.sentences,
        decoration: InputDecoration(
          labelText: getCustomFieldPlaceholder(controller.templateBlock.customFieldOptions!),
        ),
        onSubmitted: onFieldSubmitted,
      ),
    );
  }
}
