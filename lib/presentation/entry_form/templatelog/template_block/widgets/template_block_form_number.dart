import 'package:bitacora/domain/custom_field/value/custom_field_type.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_text_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TemplateBlockFormNumber extends StatelessWidget {
  final TemplateBlockTextFormController controller;
  final void Function(String?)? onFieldSubmitted;
  final TextInputAction? texInputAction;

  const TemplateBlockFormNumber({
    super.key,
    required this.controller,
    this.onFieldSubmitted,
    this.texInputAction,
  });

  @override
  Widget build(BuildContext context) {
    final isFloat =
        controller.templateBlock.customFieldOptions!.customField!.type ==
            CustomFieldType.floatNumber;
    return wrapWidgetForMeasure(
      TextFormField(
        controller: controller.value.inputController,
        focusNode: controller.focusNode,
        textInputAction: texInputAction,
        textCapitalization: TextCapitalization.sentences,
        keyboardType: TextInputType.numberWithOptions(decimal: isFloat),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r"[0-9.]")),
          if (!isFloat)
            TextInputFormatter.withFunction((oldValue, newValue) {
              final text = newValue.text;
              return text.isEmpty
                  ? newValue
                  : int.tryParse(text) == null
                      ? oldValue
                      : newValue;
            }),
          if (isFloat)
            TextInputFormatter.withFunction((oldValue, newValue) {
              final text = newValue.text;
              return text.isEmpty
                  ? newValue
                  : double.tryParse(text) == null
                      ? oldValue
                      : newValue;
            }),
        ],
        decoration: InputDecoration(
          labelText: getCustomFieldPlaceholder(controller.templateBlock.customFieldOptions!),
          errorStyle: const TextStyle(height: 0, fontSize: 0),
        ),
        validator: controller.validator,
        onFieldSubmitted: onFieldSubmitted,
      ),
    );
  }
}
