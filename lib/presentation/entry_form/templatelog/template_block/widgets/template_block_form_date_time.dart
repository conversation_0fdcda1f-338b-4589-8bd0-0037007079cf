import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_date_time_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';
import 'package:datetime_picker_formfield_new/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';

class TemplateBlockFormDateTime extends StatelessWidget {
  final TemplateBlockDateTimeFormController controller;
  final void Function(DateTime?)? onFieldSubmitted;
  final TextInputAction? texInputAction;

  const TemplateBlockFormDateTime({
    super.key,
    required this.controller,
    this.onFieldSubmitted,
    this.texInputAction,
  });

  @override
  Widget build(BuildContext context) {
    return DateTimeField(
      key: controller.fieldKey,
      focusNode: controller.focusNode,
      controller: controller.value.inputController,
      format: controller.dateTimeFormat,
      resetIcon: const Icon(Icons.close, size: 18),
      textCapitalization: TextCapitalization.sentences,
      keyboardType: TextInputType.text,
      textInputAction: texInputAction,
      onShowPicker: controller.onShowPicker,
      onFieldSubmitted: onFieldSubmitted,
      decoration: InputDecoration(
        labelText: getCustomFieldPlaceholder(
            controller.templateBlock.customFieldOptions!),
        contentPadding: getContentPaddingWithSuffix(),
        errorStyle: const TextStyle(height: 0, fontSize: 0),
      ),
      validator: (_) {
        if (controller.validator == null) {
          return null;
        }

        return controller.validator!(controller.value.value);
      },
    );
  }
}
