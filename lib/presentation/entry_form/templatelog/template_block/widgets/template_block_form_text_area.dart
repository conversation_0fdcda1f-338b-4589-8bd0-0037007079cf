import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/custom_field_placeholder.dart';
import 'package:bitacora/presentation/widgets/text_field_done.dart';
import 'package:bitacora/util/trim_text_on_focus_lost.dart';
import 'package:flutter/material.dart';

class TemplateBlockFormTextArea extends StatelessWidget {
  final TemplateBlockTextFormController controller;
  final TextInputAction? texInputAction;
  final void Function(dynamic)? onFieldSubmitted;

  const TemplateBlockFormTextArea({
    super.key,
    required this.controller,
    this.texInputAction,
    this.onFieldSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextFieldDone(
      focusNode: controller.focusNode,
      child: TrimTextOnFocusLost(
        focusNode: controller.focusNode,
        controller: controller.value.inputController,
        child: TextForm<PERSON>ield(
          focusNode: controller.focusNode,
          controller: controller.value.inputController,
          minLines: controller.props.inputEntry == null ? 3 : 1,
          maxLines: null,
          keyboardType: TextInputType.multiline,
          textInputAction: TextInputAction.newline,
          textCapitalization: TextCapitalization.sentences,
          decoration: InputDecoration(
            labelText: getCustomFieldPlaceholder(
                controller.templateBlock.customFieldOptions!),
            alignLabelWithHint: true,
          ),
          validator: controller.validator,
          style: const TextStyle(height: 1.2),
        ),
      ),
    );
  }
}
