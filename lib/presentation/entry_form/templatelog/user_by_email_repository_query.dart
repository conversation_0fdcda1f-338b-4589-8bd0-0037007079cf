import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/user/user.dart';

class UserByEmailRepositoryQuery extends RepositoryQuery<User?> {
  final String email;

  UserByEmailRepositoryQuery({required this.email});

  @override
  Future<User?> run(RepositoryQueryContext context) {
    return context.db.user.findByEmail(context, UserEmail(email));
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) {
    return db.user.fieldsBuilder.build();
  }
}
