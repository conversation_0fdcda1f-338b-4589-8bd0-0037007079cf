import 'package:bitacora/presentation/entry_form/options/entry_form_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_option_tabs.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

const kIconSize = 50.0;
const kIconSizeSmallScreen = 45.0;
const kIconBoxSize = 36.0;

class EntryFormOptions extends StatefulWidget {
  final List<EntryFormOptionController> options;

  const EntryFormOptions({
    super.key,
    required this.options,
  });

  @override
  State<EntryFormOptions> createState() => _EntryFormOptionsState();
}

class _EntryFormOptionsState extends State<EntryFormOptions> {
  late final EntryFormOptionTabs tabs;

  @override
  void initState() {
    super.initState();
    tabs = EntryFormOptionTabs(widget.options);
  }

  @override
  void didUpdateWidget(covariant EntryFormOptions oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_isSameList(oldWidget.options, widget.options)) {
      tabs.updateOptions(widget.options);
    }
  }

  @override
  void dispose() {
    tabs.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: kPageMargin - (kIconSize - kIconBoxSize) / 4),
          child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: widget.options
                  .map<Widget>(
                      (e) => e.isAvailable ? _getOptionIcon(e) : const SizedBox())
                  .toList(growable: false)),
        ),
        ChangeNotifierProvider.value(
            value: tabs,
            builder: (_, __) => Consumer<EntryFormOptionTabs>(
                  builder: (context, tabs, _) => Column(
                      children: tabs.tabs
                          .map<Widget>((e) =>
                              e.isAvailable ? _getOptionForm(e) : const SizedBox())
                          .toList(growable: false)),
                ))
      ],
    );
  }

  Widget _getOptionIcon(EntryFormOptionController controller) {
    final iconSize = isSmallScreen(context) ? kIconSizeSmallScreen : kIconSize;
    return ValueListenableBuilder<bool>(
      valueListenable: controller.isShowing,
      builder: (context, isShowing, child) => isShowing
          ? Stack(
              alignment: Alignment.center,
              children: [
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: controller.color[500]!.withValues(alpha: 0.15),
                    borderRadius: kBorderRadius,
                  ),
                  child:
                      const SizedBox(width: kIconBoxSize, height: kIconBoxSize),
                ),
                child!,
              ],
            )
          : child!,
      child: SizedBox(
        width: (kIconBoxSize + iconSize) / 2,
        height: iconSize,
        child: InkWell(
          onTap: () => controller.onTap(context),
          child: controller.iconWidget(context),
        ),
      ),
    );
  }

  Widget _getOptionForm(EntryFormOptionController controller) {
    return Align(
      key: controller.key,
      alignment: Alignment.centerRight,
      child: ValueListenableBuilder<bool>(
        valueListenable: controller.isShowing,
        builder: (context, isShowing, _) => !isShowing
            ? const SizedBox()
            : Theme(
                data: overrideThemeData(controller.color, context),
                child: Padding(
                  padding: const EdgeInsets.only(
                    left: kPageMargin,
                    right: kPageMargin,
                    bottom: kFormVerticalSpacing,
                  ),
                  child: Material(
                    color: controller.color[500]!.withValues(alpha: 0.15),
                    borderRadius: kBorderRadius,
                    child: controller.form(context),
                  ),
                ),
              ),
      ),
    );
  }

  bool _isSameList(List oldList, List newList) {
    if (oldList.length != newList.length) {
      return false;
    }

    for (var i = 0; i < oldList.length; i++) {
      if (oldList[i] != newList[i]) {
        return false;
      }
    }
    return true;
  }
}

class NonEditableEntryFormOptions extends StatelessWidget {
  final List<EntryFormOptionController> options;

  const NonEditableEntryFormOptions({
    super.key,
    required this.options,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: options
          .map<Widget>((e) => ValueListenableBuilder<bool>(
              valueListenable: e.hasData,
              builder: (context, hasData, child) => hasData
                  ? Padding(
                      padding: const EdgeInsets.only(
                        top: kFormVerticalSpacing,
                        left: 13,
                      ),
                      child: SizedBox(width: 15, child: Icon(e.icon)),
                    )
                  : const SizedBox()))
          .toList(growable: false),
    );
  }
}
