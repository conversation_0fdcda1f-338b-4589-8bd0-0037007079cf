import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/hardcoded_data/quick_action_button/hardcoded_quick_action_button_data.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/presentation/entry_form/options/location_tracking/entry_form_location_tracking_option_controller.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/util/map/map_widget.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';

class EntryFormLocationTrackingOption extends StatefulWidget {
  final EntryFormLocationTrackingOptionController controller;

  const EntryFormLocationTrackingOption({super.key, required this.controller});

  @override
  State<EntryFormLocationTrackingOption> createState() =>
      _EntryFormLocationTrackingOptionState();
}

class _EntryFormLocationTrackingOptionState
    extends State<EntryFormLocationTrackingOption> {
  ValueNotifier<Path<LatLng>>? pathNotifier;

  @override
  void initState() {
    super.initState();

    widget.controller.locationTracking.addListener(_locationTrackingToPath);
    _locationTrackingToPath();
  }

  @override
  void dispose() {
    widget.controller.locationTracking.removeListener(_locationTrackingToPath);
    super.dispose();
  }

  void _locationTrackingToPath() {
    if (widget.controller.locationTracking.value == null) {
      return;
    }

    final pathCoordinates = widget.controller.locationTracking.value!.points!
        .map((e) => e.latLong!.value!)
        .toList();
    final path = Path.from(pathCoordinates);
    if (pathNotifier == null) {
      pathNotifier = ValueNotifier(path);
    } else {
      pathNotifier!.value = path;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: kBorderRadius,
      child: ValueListenableBuilder<bool>(
        valueListenable: widget.controller.isLoading,
        builder: (context, isLoadingValue, _) {
          if (isLoadingValue) {
            return Padding(
              padding: kPageInsets,
              child: Center(
                child: PlatformCircularProgressIndicator(),
              ),
            );
          }
          return ValueListenableBuilder<bool>(
            valueListenable: widget.controller.hasData,
            builder: (context, hasData, _) {
              if (!hasData || pathNotifier == null) {
                return const SizedBox();
              }

              final organization = context.watch<ActiveOrganization>().value!;
              final disabledTrackingView = HardcodedQuickActionButtonData
                  .enabledOrganizationIds
                  .contains(organization.remoteId!);
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MapWidget(
                    pathNotifier: disabledTrackingView
                        ? ValueNotifier(Path())
                        : pathNotifier,
                    marker: widget.controller.location.value,
                    height: 200,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8.0,
                      horizontal: 16.0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ValueListenableBuilder(
                            valueListenable: widget.controller.locationTracking,
                            builder: (context, locationTracking, _) {
                              final averageSpeed =
                                  _buildAverageSpeedString(locationTracking!);
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _buildPathDistanceString(
                                      pathNotifier!.value,
                                    ),
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                  if (averageSpeed.isNotEmpty)
                                    Text(
                                      averageSpeed,
                                      style:
                                          Theme.of(context).textTheme.bodySmall,
                                    ),
                                ],
                              );
                            }),
                        IconButton(
                          onPressed: () {
                            widget.controller.findAndSetLocation();
                          },
                          icon: const Icon(Icons.refresh),
                        )
                      ],
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  String _buildPathDistanceString(Path path) {
    if (path.distance > 1000) {
      return AppLocalizations.of(context)!
          .distanceTraveled((path.distance / 1000).toStringAsFixed(2), 'km');
    }

    return AppLocalizations.of(context)!
        .distanceTraveled(path.distance.toStringAsFixed(0), 'm');
  }

  String _buildAverageSpeedString(LocationTracking locationTracking) {
    final points = locationTracking.points!.map((e) => e.speed?.value);
    final validSpeeds = points.whereType<double>().toList();
    if (validSpeeds.isNotEmpty) {
      var averageSpeed =
          validSpeeds.reduce((a, b) => a + b) / validSpeeds.length;
      if (averageSpeed > 7) {
        averageSpeed = averageSpeed * 3.6;
        return AppLocalizations.of(context)!.averageSpeed(averageSpeed, 'km/h');
      }
      return AppLocalizations.of(context)!.averageSpeed(averageSpeed, 'm/s');
    }

    return '';
  }
}
