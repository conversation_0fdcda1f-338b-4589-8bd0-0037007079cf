import 'dart:async';

import 'package:bitacora/application/sync/hot_download/location_tracking_hot_downloader.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/location_tracking/entry_form_location_tracking_option.dart';
import 'package:bitacora/presentation/entry_form/options/location_tracking/location_points_by_location_tracking_id_repostory_query.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/form/form_has_changes.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';

MaterialColor kTrackingColor = createMaterialColor(bitacoraRed.shade300);
const _kDownloadPollingInterval = Duration(seconds: 10);
const _kDbQueryPollingInterval = Duration(seconds: 3);

class EntryFormLocationTrackingOptionController
    extends EntryFormOptionController with HasChanges {
  late final LocationTrackingHotDownloader _downloader;
  final ValueNotifier<bool> isEnabled = ValueNotifier(false);
  final ValueNotifier<bool> isLoading = ValueNotifier(false);
  final ValueNotifier<bool> isLocationPermissionDenied = ValueNotifier(false);
  final ValueNotifier<LocationTracking?> locationTracking = ValueNotifier(null);
  final ValueNotifier<LatLng?> location = ValueNotifier(null);
  final LocationUtils _locationUtils = LocationUtils();

  final Repository db;
  final AuthRepository authRepository;
  final Organization organization;
  final ApiTranslator apiTranslator;
  Timer? _downloadPollingTimer;

  EntryFormLocationTrackingOptionController(
    ValueNotifier<Entry?> liveEntry, {
    required this.db,
    required this.authRepository,
    required this.organization,
    required this.apiTranslator,
  }) : super(liveEntry) {
    _downloader = LocationTrackingHotDownloader(
        db, authRepository, organization, apiTranslator);
    liveEntry.addListener(_read);
    isShowing.addListener(_onShowingMutated);
    isEnabled.addListener(_onIsEnabledMutated);
    isLoading.addListener(_onIsLoadingMutated);
    locationTracking.addListener(_onTrackingMutated);
    location.addListener(_onLocationMutated);
    _read();
    isShowing.value = true;
  }

  LocationTracking get liveLocationTracking =>
      liveEntry.value!.locationTracking!;

  bool get isLocal =>
      liveLocationTracking.isLocal!.value ||
      (liveLocationTracking.isSynced && !liveLocationTracking.isStarted);

  @override
  void dispose() {
    liveEntry.removeListener(_read);
    isShowing.removeListener(_onShowingMutated);
    isEnabled.removeListener(_onIsEnabledMutated);

    isLoading.dispose();
    isEnabled.dispose();

    _maybeStopPolling();

    super.dispose();
  }

  void _read() async {
    if (liveEntry.value?.id == null) {
      return;
    }

    location.value = liveEntry.value!.location!.value;

    if (liveEntry.value!.locationTracking != null) {
      isLoading.value = true;

      await _updatePoints();

      isLoading.value = false;
    }
  }

  Future<void> _updatePoints() {
    if (isLocal) {
      return _updatePointsFromDb();
    } else {
      return _updatePointsFromServer();
    }
  }

  Future<void> _updatePointsFromDb() async {
    locationTracking.value = liveEntry.value!.locationTracking!.copyWith(
      points: (await db.query(
        LocationPointsByLocationTrackingRepositoryQuery(
          liveEntry.value!.locationTracking!.id!,
        ),
      )),
    );
  }

  Future<void> _updatePointsFromServer() async {
    locationTracking.value =
        (await _downloader.download(liveEntry.value!.locationTracking!.uuid!) ??
            liveEntry.value!.locationTracking!.copyWith(points: []));
  }

  void _maybeStartPolling() {
    logger.i('entry-form-location-tracking-option: maybe start polling');
    if (!locationTracking.value!.isStarted || _downloadPollingTimer != null) {
      return;
    }

    _downloadPollingTimer = Timer.periodic(
      isLocal ? _kDbQueryPollingInterval : _kDownloadPollingInterval,
      (timer) async {
        logger.i('entry-form-location-tracking-option: download polling...');
        _downloadPollingTimer = timer;
        if (isShowing.value == false || locationTracking.value!.isFinished) {
          _maybeStopPolling();
          return;
        }

        await _updatePoints();
      },
    );
  }

  void _maybeStopPolling() {
    logger.i('entry-form-location-tracking-option: maybe stop polling');
    _downloadPollingTimer?.cancel();
    _downloadPollingTimer = null;
  }

  void _onShowingMutated() {
    if (isShowing.value && !isLoading.value) {
      _maybeStartPolling();

      if (!isEnabled.value) {
        isEnabled.value = true;
      }
    } else {
      _maybeStopPolling();
    }
  }

  Future<void> _onIsLoadingMutated() async {
    if (!isLoading.value && isShowing.value) {
      _maybeStartPolling();
    }
  }

  Future<void> _onIsEnabledMutated() async {
    if (!isEnabled.value) {
      isShowing.value = false;
      _clearData();
    }
  }

  void _clearData() {
    locationTracking.value = null;
  }

  void _onTrackingMutated() {
    hasData.value = locationTracking.value != null;
    if (!isEnabled.value) {
      isEnabled.value = hasData.value;
    }
  }

  void _onLocationMutated() {
    hasChanges.value = liveEntry.value?.location?.value != location.value;
  }

  Future<void> findAndSetLocation() async {
    location.value = null;
    isLocationPermissionDenied.value = false;

    try {
      isLoading.value = true;

      final locationData = await _locationUtils.determinePosition();

      if (mounted) {
        location.value = locationData.toLatLng();
        isLoading.value = false;
      }
    } on LocationPermissionDeniedException catch (_) {
      isLocationPermissionDenied.value = true;
      isEnabled.value = false;
      isLoading.value = false;
    } on LocationServiceDisabledException catch (_) {
      isEnabled.value = false;
      isLoading.value = false;
    }
  }

  @override
  MaterialColor get color => kTrackingColor;

  @override
  IconData get icon => Icons.route;

  @override
  Widget form(BuildContext context) =>
      EntryFormLocationTrackingOption(controller: this);
}
