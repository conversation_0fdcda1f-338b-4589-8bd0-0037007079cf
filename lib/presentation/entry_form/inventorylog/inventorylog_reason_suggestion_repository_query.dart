import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';

class InventorylogReasonSuggestionRepositoryQuery
    extends RepositoryQuery<List<String>> {
  const InventorylogReasonSuggestionRepositoryQuery();

  @override
  Future<List<String>> run(RepositoryQueryContext context) =>
      context.db.inventorylog.reasons(context);
}
