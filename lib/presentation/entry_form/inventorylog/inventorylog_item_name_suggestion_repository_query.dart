import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';

class InventorylogItemNameSuggestionRepositoryQuery
    extends RepositoryQuery<List<String>> {
  const InventorylogItemNameSuggestionRepositoryQuery();

  @override
  Future<List<String>> run(RepositoryQueryContext context) =>
      context.db.inventorylog.itemNames(context);
}
