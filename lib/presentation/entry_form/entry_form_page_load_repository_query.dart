import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';

class EntryFormPageLoadRepositoryQuery extends RepositoryQuery<Entry?> {
  final LocalId entryId;

  const EntryFormPageLoadRepositoryQuery({required this.entryId});

  @override
  Future<Entry?> run(RepositoryQueryContext context) =>
      context.db.entry.find(context, entryId);

  @override
  Fields fields(Repository db) => sFields(db);

  static Fields sFields(Repository db) {
    final project = db.project.fieldsBuilder.name().build();
    final user = db.user.fieldsBuilder.name().build();

    final worklog = db.worklog.fieldsBuilder
        .project(project)
        .type()
        .sublocation()
        .title()
        .quantity()
        .paymentStatus()
        .priceIsUnit()
        .costPrice()
        .salePrice()
        .provider()
        .build();

    final inventorylog = db.inventorylog.fieldsBuilder
        .type()
        .destProject(project)
        .sourceProject(project)
        .destSublocation()
        .sourceSublocation()
        .provider()
        .itemName()
        .quantity()
        .reason()
        .costPrice()
        .salePrice()
        .priceIsUnit()
        .paymentStatus()
        .build();

    final personnellog = db.personnellog.fieldsBuilder
        .project(project)
        .sublocation()
        .name()
        .minutes()
        .entrance()
        .exit()
        .name()
        .build();

    final allowedValues = db.customFieldAllowedValue.fieldsBuilder
        .value()
        .label()
        .parent(db.customFieldAllowedValue.fieldsBuilder.build())
        .build();

    final customField = db.customField.fieldsBuilder
        .allowedValues(allowedValues)
        .type()
        .build();
    final options =
        db.customFieldOptions.fieldsBuilder.customField(customField).build();
    final blocks = db.templateBlock.fieldsBuilder
        .role()
        .customFieldOptions(options)
        .build();
    final groups = db.templateGroup.fieldsBuilder.name().blocks(blocks).build();
    final templatelog = db.templatelog.fieldsBuilder
        .template(db.template.fieldsBuilder.name().groups(groups).build())
        .fieldsMetadata(db.customFieldMetadata.fieldsBuilder
            .project(project)
            .value()
            .user(user)
            .customField(customField)
            .allowedValue(allowedValues)
            .build())
        .build();

    return db.entry.fieldsBuilder
        .remoteId()
        .day()
        .time()
        .startDate()
        .endDate()
        .startTime()
        .endTime()
        .comments()
        .location()
        .createdAt()
        .syncVersion()
        .worklog(worklog)
        .inventorylog(inventorylog)
        .personnellog(personnellog)
        .templatelog(templatelog)
        .progresslog(db.progresslog.fieldsBuilder
            .entry(db.entry.fieldsBuilder
                .comments()
                .location()
                .worklog(worklog)
                .personnellog(personnellog)
                .inventorylog(inventorylog)
                .templatelog(templatelog)
                .openState(db.openState.fieldsBuilder
                    .progressive()
                    .startDay()
                    .endDay()
                    .build())
                .attachments(db.attachment.fieldsBuilder.build())
                .tags(db.tag.fieldsBuilder.build())
                .signatures(db.signature.fieldsBuilder.build())
                .assignee(db.user.fieldsBuilder.name().build())
                .build())
            .progress()
            .build())
        .openState(db.openState.fieldsBuilder
            .progress()
            .progressive()
            .startDay()
            .endDay()
            .build())
        .entryGroupEntry(db.entryGroupEntry.fieldsBuilder
            .entryGroup(db.entryGroup.fieldsBuilder
                .entries(db.entryGroupEntry.fieldsBuilder.build())
                .build())
            .build())
        .attachments(db.attachment.fieldsBuilder
            .s3Key()
            .name()
            .isUploaded()
            .isDownloaded()
            .transferState()
            .transferAttempts()
            .path()
            .doodle()
            .comments()
            .build())
        .tags(db.tag.fieldsBuilder.name().color().build())
        .signatures(db.signature.fieldsBuilder
            .doodle()
            .s3Key()
            .comments()
            .ownerName()
            .ownerEmail()
            .status()
            .build())
        .source(db.entrySource.fieldsBuilder.metadata().type().build())
        .metadata(db.entryMetadata.fieldsBuilder.value().type().build())
        .locationTracking(db.locationTracking.fieldsBuilder
            .remoteId()
            .uuid()
            .status()
            .isLocal()
            .build())
        .author(db.user.fieldsBuilder.name().build())
        .assignee(db.user.fieldsBuilder.name().email().build())
        .timerStatus()
        .entryDraft(db.entryDraft.fieldsBuilder.type().build())
        .build();
  }
}
