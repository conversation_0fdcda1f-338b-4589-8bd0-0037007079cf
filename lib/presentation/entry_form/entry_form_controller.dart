import 'dart:async';

import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/metadata/active_custom_field_metadata_filter.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group/value/entry_group_created_at.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/entry_source/entry_source.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_context_snapshot.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_load_repository_query.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/entry_form_save_entry_permission_notifier.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/entry_form_attachment_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_options.dart';
import 'package:bitacora/presentation/entry_form/options/location/entry_form_location_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/location_tracking/entry_form_location_tracking_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/signature/entry_form_signature_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/user/entry_form_user_option_controller.dart';
import 'package:bitacora/presentation/entry_form/util/auto_fill_active_project.dart';
import 'package:bitacora/presentation/widgets/log_date/log_time_value_notifier.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/form/form_has_changes.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:flutter/widgets.dart';

typedef EntryFormReadFunction<T> = void Function(
  ValueNotifier<T?> controller,
  T? readValue,
  T? lastReadValue, [
  T? defaultValue,
]);

abstract class EntryFormController with FormHasChanges, AutoFillerFromCache {
  final EntryFormProps props;
  final ValueNotifier<bool> initialized = ValueNotifier(false);
  final TextEditingController comments = TextEditingController();

  late final ActiveLogDay activeLogDay;
  late final LogTimeValueNotifier logTime;
  late final EntryFormSignatureOptionController? signature;
  late final EntryFormUserOptionController user;
  late final EntryFormDateTimeOptionController dateTime;
  late final EntryFormAttachmentOptionController attachment;
  late final EntryFormTagOptionController tag;
  late final EntryFormOptionController location;
  late final ValueNotifier<Entry?> liveEntry;
  late final EntryFormSaveEntryPermissionNotifier savePermission;

  Entry? _lastReadEntry;

  EntryFormController(
      EntryFormControllerContextSnapshot contextSnapshot, this.props) {
    _setup(contextSnapshot);
  }

  void _setup(EntryFormControllerContextSnapshot contextSnapshot) {
    final db = contextSnapshot.read<Repository>();

    activeLogDay = contextSnapshot.read<ActiveLogDay>();
    logTime = props.logTime != null
        ? LogTimeValueNotifier(props.logTime!)
        : LogTimeValueNotifier(LogTime.fromDateTime(DateTime.now()));

    liveEntry = LiveModel<Entry>(
      props.inputEntry,
      db.entry.getMutations(),
      () => db.query(
        EntryFormPageLoadRepositoryQuery(entryId: props.inputEntry!.id!),
      ),
    );
    liveEntry.addListener(() {
      if (liveEntry.value != null) {
        read(liveEntry.value!, _lastReadEntry);
        _lastReadEntry = liveEntry.value;
      }
    });
    init(contextSnapshot, liveEntry);
    if (liveEntry.value != null) {
      read(liveEntry.value!);
      _lastReadEntry = liveEntry.value;
    }

    initialized.value = true;

    monitorChanges();
    if (liveEntry.value == null) {
      fillFromCache(contextSnapshot);
    }

    savePermission = EntryFormSaveEntryPermissionNotifier(
      db,
      contextSnapshot.read<ActiveOrganization>().value!,
      liveEntry,
      activeLogDay,
      logTime,
    );
  }

  void dispose() {
    savePermission.dispose();
    comments.dispose();
    signature?.dispose();
    user.dispose();
    dateTime.dispose();
    attachment.dispose();
    tag.dispose();
    location.dispose();
    liveEntry.dispose();
  }

  @protected
  void init(
    EntryFormControllerContextSnapshot contextSnapshot,
    ValueNotifier<Entry?> liveEntry,
  ) {
    user = EntryFormUserOptionController(contextSnapshot, liveEntry);
    dateTime = EntryFormDateTimeOptionController(props, liveEntry);
    attachment = EntryFormAttachmentOptionController(
      contextSnapshot,
      liveEntry,
    );
    tag = EntryFormTagOptionController(liveEntry);
    signature = props.isSignatureAllowed
        ? EntryFormSignatureOptionController(contextSnapshot, liveEntry)
        : null;

    location = liveEntry.value?.locationTracking != null
        ? EntryFormLocationTrackingOptionController(
            liveEntry,
            db: contextSnapshot.read<Repository>(),
            authRepository: contextSnapshot.read<AuthRepository>(),
            organization: contextSnapshot.read<ActiveOrganization>().value!,
            apiTranslator: contextSnapshot.read<ApiTranslator>(),
          )
        : EntryFormLocationOptionController(liveEntry);
  }

  @protected
  void read(Entry entry, [Entry? lastReadEntry]) {
    readNotifier<LogTime>(
      logTime,
      entry.time,
      lastReadEntry?.time,
      LogTime.fromDateTime(DateTime.now()),
    );
    readNotifier<TextEditingValue>(
      comments,
      TextEditingValue(text: entry.comments?.displayValue ?? ''),
      TextEditingValue(text: lastReadEntry?.comments?.displayValue ?? ''),
    );
  }

  @protected
  void readNotifier<T>(
    ValueNotifier<T?> notifier,
    T? readValue,
    T? lastReadValue, [
    T? defaultValue,
  ]) {
    if (_lastReadEntry == null) {
      notifier.value = readValue ?? defaultValue;
      return;
    }

    if (notifier.value == lastReadValue) {
      notifier.value = readValue ?? defaultValue;
    }
  }

  @protected
  void readValue<T>(
    ValueNotifier<T?> notifier,
    T? readValue,
    T? lastReadValue, [
    T? defaultValue,
  ]) {
    if (_lastReadEntry == null) {
      notifier.value = readValue ?? defaultValue;
      return;
    }

    if (notifier.value == lastReadValue) {
      notifier.value = readValue ?? defaultValue;
    }
  }

  @override
  Map<ValueNotifier, ValueObject?> get fields => {
        if (liveEntry.value?.id != null) ...{
          activeLogDay: liveEntry.value?.day,
          logTime: liveEntry.value?.time,
        },
      };

  @override
  Set<HasChanges> get subForms => options.whereType<HasChanges>().toSet();

  Widget showForm() {
    return ValueListenableBuilder(
      valueListenable: initialized,
      builder: (_, isInitialized, __) {
        if (!initialized.value) {
          return const SliverToBoxAdapter(child: SizedBox());
        }

        return form();
      },
    );
  }

  Widget form();

  List<EntryFormOptionController> get options {
    return [
      if (signature != null) signature!,
      user,
      dateTime,
      location,
      tag,
      attachment,
    ];
  }

  Widget formOptions() => props.isEditable
      ? EntryFormOptions(options: options)
      : NonEditableEntryFormOptions(options: options);

  Widget header(BuildContext context);

  Widget headerOption(BuildContext context, {bool isEditable = true});

  void cancel() {
    attachment.cleanStagingDirectory();
  }

  Future<List<Entry>> save(
    EntryFormControllerSaveContextSnapshot context,
  ) async {
    final db = context.read<Repository>();
    final activeOrganization = context.read<ActiveOrganization>();
    final activeSession = context.read<ActiveSession>();
    final activeProject = context.read<ActiveProject>();
    final activeMetadataFilter =
        context.read<ActiveCustomFieldMetadataFilter>();

    if (!activeLogDay.hasLoaded ||
        !activeSession.hasLoaded ||
        !activeOrganization.hasLoaded) {
      logger.f(
        'Can\'t save. ${activeLogDay.hasLoaded} '
        'or ${activeSession.hasLoaded}'
        'or ${activeOrganization.hasLoaded}',
      );
    }

    final organization = Organization(id: activeOrganization.value!.id);

    final savedEntries = await db.transaction<List<Entry>>(
      (dbContext) async {
        final extensions =
            await buildExtensionsForSave(dbContext, organization);
        final open = dateTime.buildOpenStateForSave();
        final attachments = await Future.wait(attachment.attachments.value.map(
          (e) async {
            final attachment = e.value!;
            if (attachment.path!.value == null ||
                attachment.path!.isRelativeToStorage) {
              return attachment;
            }
            final file = FileSystemInjector.get().file(attachment.path!.value!);
            final processedRelativePath =
                await AttachmentUtils().processForSave(file);
            return attachment.copyWith(
                path: AttachmentPath(processedRelativePath));
          },
        ));
        attachment.cleanStagingDirectory();
        final isScheduleEntry = dateTime.isScheduleEntry.value;

        final savedEntries = <Entry>[];
        for (final extension in extensions) {
          if (extension is Templatelog) {
            logger.i('entry-form-controller:templatelog saving '
                'extension [${extension.id}]');
          }

          final entry = Entry(
            id: liveEntry.value?.id,
            day: open?.startDay ?? activeLogDay.value,
            time: logTime.value,
            comments: EntryComments(comments.text),
            author: liveEntry.value?.author ?? activeSession.value!.user,
            assignee: user.assignee,
            createdAt:
                liveEntry.value?.createdAt ?? EntryCreatedAt(Clock().now()),
            updatedAt: EntryUpdatedAt(Clock().now()),
            startDate: NullableLogDay.fromUiStringDate(
                isScheduleEntry ? null : dateTime.startDate.text),
            endDate: NullableLogDay.fromUiStringDate(
                isScheduleEntry ? null : dateTime.endDate.text),
            startTime: NullableLogTime.fromDateTime(
                isScheduleEntry || dateTime.startTime.text.isEmpty
                    ? null
                    : kTimeOfDayDateFormat.parse(dateTime.startTime.text)),
            endTime: NullableLogTime.fromDateTime(
                isScheduleEntry || dateTime.endTime.text.isEmpty
                    ? null
                    : kTimeOfDayDateFormat.parse(dateTime.endTime.text)),
            extension: extension,
            location: LatLngValueObject(location
                    is EntryFormLocationOptionController
                ? (location as EntryFormLocationOptionController).location.value
                : (location as EntryFormLocationTrackingOptionController)
                    .location
                    .value),
            locationTracking: liveEntry.value?.locationTracking ??
                (location is EntryFormLocationTrackingOptionController
                    ? (location as EntryFormLocationTrackingOptionController)
                        .locationTracking
                        .value
                    : null),
            openState: open,
            attachments: attachments,
            tags: tag.tags.value
                .map<Tag>((e) => e.copyWith(organization: organization))
                .toList(growable: false),
            signatures: signature?.signatures.value,
            source: liveEntry.value?.source ??
                EntrySource(
                  type: props.isFromQr
                      ? EntrySourceType.mobileQr
                      : EntrySourceType.mobile,
                  metadata: !props.isFromQr
                      ? null
                      : EntrySourceMetadata.fromQr(props.qrCode!),
                ),
            metadata: liveEntry.value?.metadata,
          );

          if (entry.id != null && liveEntry.value?.draft != null) {
            await db.entryDraft.delete(dbContext, liveEntry.value!.draft!.id!);
          }

          final savedId = await db.entry.save(
            dbContext.copyWith(
                queryScope: QueryScope(orgId: activeOrganization.value!.id)),
            entry,
            requestSync: true,
          );

          if (extension is Templatelog) {
            logger.i(
              'entry-form-controller:entry templatelog saved [$savedId] '
              '[endTime: ${dateTime.endTime.text}]'
              '[endDate: ${dateTime.endDate.text}]',
            );
          }

          await _maybeSaveEntryGroup(db, dbContext, organization, savedId!);

          savedEntries.add(
              entry.copyWith(id: savedId, remoteId: liveEntry.value?.remoteId));
        }
        return savedEntries;
      },
    );

    activeProject.maybeSelectActiveProjectAfterSave(
      db,
      organization,
      savedEntries.first,
    );

    activeMetadataFilter.maybeSelectActiveCustomFieldMetadataAfterSave(
      db,
      organization,
      savedEntries.first,
    );

    return savedEntries;
  }

  Future<void> _maybeSaveEntryGroup(
    Repository db,
    RepositoryQueryContext context,
    Organization organization,
    LocalId savedId,
  ) async {
    if (props.relatedEntry == null) {
      return;
    }

    if (props.relatedEntry!.entryGroupEntry?.entryGroup != null) {
      await db.entryGroupEntry.save(
        context,
        EntryGroupEntry(
          entry: Entry(id: savedId),
          entryGroup: EntryGroup(
            id: props.relatedEntry!.entryGroupEntry!.entryGroup!.id,
          ),
          createFrom: Entry(id: props.relatedEntry!.id),
        ),
        requestSync: true,
      );
      return;
    }

    final relatedEntry = Entry(id: props.relatedEntry!.id);
    await db.entryGroup.save(
      context,
      EntryGroup(
          name: EntryGroupName(''),
          entries: [
            EntryGroupEntry(
              entry: relatedEntry,
            ),
            EntryGroupEntry(
              entry: Entry(id: savedId),
              createFrom: relatedEntry,
            )
          ],
          organization: organization,
          createdAt: EntryGroupCreatedAt(Clock().now())),
      requestSync: true,
    );
  }

  @protected
  Future<Project?> buildProject(
    RepositoryQueryContext context,
    Organization organization,
    String? name,
  ) async {
    if (name?.isEmpty ?? true) {
      return null;
    }

    final scopedContext = context.copyWith(
        queryScope: context.db.queryScope(orgId: organization.id));
    return (await context.db.query(
            ProjectByNameRepositoryQuery(ProjectName(name!)),
            context: scopedContext)) ??
        Project(name: ProjectName(name), organization: organization)
            .copyWith(isSyncable: ProjectIsSyncable(true));
  }

  @protected
  Future<List<Extension>> buildExtensionsForSave(
    RepositoryQueryContext context,
    Organization organization,
  );

  String emptyString() => props.isEditable ? '' : '-';
}

class EntryFormControllerSaveContextSnapshot extends ContextSnapshot {
  EntryFormControllerSaveContextSnapshot(super.context);

  @override
  List<ValueShot> get valueShots => [
        ValueShot.provider<Repository>(),
        ValueShot.provider<ActiveOrganization>(),
        ValueShot.provider<ActiveSession>(),
        ValueShot.provider<ActiveProject>(),
        ValueShot.provider<ActiveCustomFieldMetadataFilter>(),
      ];
}
