import 'package:bitacora/presentation/widgets/section_title.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AISettings extends StatefulWidget {
  const AISettings({super.key});

  @override
  State<AISettings> createState() => _AISettingsState();
}

class _AISettingsState extends State<AISettings> {
  final TextEditingController _instructionsController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPrefs();
  }

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  Future<void> _loadPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _instructionsController.text = prefs.getString(
            SharedPreferencesKeys.aiInstructions,
          ) ??
          '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(15.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          SectionTitle(title: AppLocalizations.of(context)!.aiSettings),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16.0),
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0, left: 4.0),
                  child: Text(
                    '${AppLocalizations.of(context)!.aiInstructions}:',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                TextField(
                  controller: _instructionsController,
                  decoration: InputDecoration(
                    hintText: AppLocalizations.of(context)!.aiInstructionsHint,
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  onChanged: _onInstructionsChanged,
                  textInputAction: TextInputAction.done,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _onInstructionsChanged(String value) async {
    final prefs = await SharedPreferences.getInstance();
    if (value.trim().isEmpty) {
      await prefs.remove(SharedPreferencesKeys.aiInstructions);
    } else {
      await prefs.setString(
        SharedPreferencesKeys.aiInstructions,
        value,
      );
    }
  }
}
