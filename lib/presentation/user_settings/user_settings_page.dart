import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/auth_app_service.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/nuke.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/attachment/has_pending_attachment_uploads_repository_query.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/theme/active_theme_mode.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/theme/named_theme_mode.dart';
import 'package:bitacora/presentation/user_settings/app_info.dart';
import 'package:bitacora/presentation/user_settings/ai_settings.dart';
import 'package:bitacora/presentation/user_settings/has_pending_outgoing_mutations_repository_query.dart';
import 'package:bitacora/presentation/user_settings/location_settings.dart';
import 'package:bitacora/presentation/user_settings/notifications/notification_settings.dart';
import 'package:bitacora/presentation/user_settings/other_settings.dart';
import 'package:bitacora/presentation/user_settings/speech_to_text_settings.dart';
import 'package:bitacora/presentation/widgets/menu_list.dart';
import 'package:bitacora/presentation/widgets/menu_list_item.dart';
import 'package:bitacora/util/dialog.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class UserSettingsPage extends StatefulWidget {
  const UserSettingsPage({super.key});

  @override
  State<UserSettingsPage> createState() => _UserSettingsPageState();
}

class _UserSettingsPageState extends State<UserSettingsPage> {
  final GlobalKey<_UserSettingsPageState> _widgetKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    final activeThemeMode = context.watch<ActiveThemeMode>();

    return GestureDetector(
      onTap: FocusScope.of(context).unfocus,
      child: Scaffold(
        key: _widgetKey,
        appBar: AppBar(
          title: Text(AppLocalizations.of(context)!.userSettings),
          centerTitle: false,
          actions: [
            IconButton(
              onPressed: () => _onLogoutPressed(context),
              icon: const Icon(Icons.logout, color: bitacoraRed),
            )
          ],
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                MenuList(
                  title: AppLocalizations.of(context)!.theme,
                  itemCount: ThemeMode.values.length,
                  itemBuilder: (BuildContext context, int index) {
                    final themeMode = ThemeMode.values[index];
                    return MenuListItem(
                      title: themeMode.getDisplayName(context),
                      isSelected: themeMode == activeThemeMode.value,
                      onTap: () {
                        activeThemeMode.set(themeMode);
                      },
                    );
                  },
                ),
                if (AppConfig().isSpeechToTextInAudioRecorderEnabled)
                  const SpeechToTextSettings(),
                const LocationSettings(),
                const NotificationSettings(),
                const AISettings(),
                const OtherSettings(),
                const AppInfo(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onLogoutPressed(BuildContext context) async {
    final activeSession = context.read<ActiveSession>();
    final db = context.read<Repository>();
    final authAppService = context.read<AuthAppService>();
    final contextSnapshot = NukeContextSnapshot(context);

    final currentContext = _widgetKey.currentContext;
    if (currentContext == null) {
      return;
    }

    showDestructiveDialog(
      context: _widgetKey.currentContext!,
      title: AppLocalizationsResolver.get().logoutTitle,
      message: await _getLogoutMessage(db, activeSession),
      destroyText: AppLocalizationsResolver.get().logout,
      destroyer: () => _logout(authAppService, contextSnapshot),
    );
  }

  void _logout(
    AuthAppService authAppService,
    NukeContextSnapshot contextSnapshot,
  ) {
    authAppService.logout(contextSnapshot);
  }

  Future<String> _getLogoutMessage(
      Repository db, ActiveSession activeSession) async {
    final hasPendingOutgoingMutations = await db.query(
      const HasPendingOutgoingMutationsRepositoryQuery(ignoreRetryLimit: true),
    );

    final attachmentPendingUpload = await db.query(
      const HasPendingAttachmentUploadsRepositoryQuery(),
      context: db.context(
          queryScope: db.queryScope(userId: activeSession.value!.user.id)),
    );

    final appLocalizations = AppLocalizationsResolver.get();
    final hasPendingUploads =
        hasPendingOutgoingMutations || attachmentPendingUpload;
    final bottomText =
        hasPendingUploads ? '\n\n${appLocalizations.logoutWillLoseData}' : '';
    return '${appLocalizations.logoutMessage}$bottomText';
  }
}
