import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';

class HasPendingOutgoingMutationsRepositoryQuery extends RepositoryQuery<bool> {
  final bool ignoreRetryLimit;

  const HasPendingOutgoingMutationsRepositoryQuery({
    required this.ignoreRetryLimit,
  });

  @override
  Future<bool> run(RepositoryQueryContext context) =>
      context.db.outgoingMutation.hasPendingChanges(context, ignoreRetryLimit);
}
