import 'package:bitacora/presentation/user_settings/notifications/notification_daily_reminder_settings.dart';
import 'package:bitacora/presentation/user_settings/notifications/notification_scheduled_entries_settings.dart';
import 'package:bitacora/presentation/widgets/section_title.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class NotificationSettings extends StatefulWidget {
  const NotificationSettings({super.key});

  @override
  State<NotificationSettings> createState() => _NotificationSettingsState();
}

class _NotificationSettingsState extends State<NotificationSettings> {
  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: Container(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            SectionTitle(title: AppLocalizations.of(context)!.notifications),
            const NotificationDailyReminderSettings(),
            const SizedBox(height: 8),
            const NotificationScheduledEntriesSettings(),
          ],
        ),
      ),
    );
  }
}
