import 'package:bitacora/presentation/ai_assistant/config/ai_generation_config.dart';
import 'package:bitacora/presentation/ai_assistant/file_picker/base_file_picker.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:hl_image_picker/hl_image_picker.dart';

class GalleryFilePicker extends BaseFilePicker {
  final FileSystem _fs = FileSystemInjector.get();

  @override
  Future<List<File>> pickFiles() async {
    final pickerOptions = HLPickerOptions(
      maxSelectedAssets: AiGenerationConfig.maxSelectedAssets,
      maxDuration: AiGenerationConfig.maxVideoDuration,
      convertLivePhotosToJPG: AiGenerationConfig.convertLivePhotosToJPG,
      usedCameraButton: AiGenerationConfig.usedCameraButton,
    );

    final result =
        await HLImagePicker().openPicker(pickerOptions: pickerOptions);
    return result.map((e) => _fs.file(e.path)).toList(growable: false);
  }
}
