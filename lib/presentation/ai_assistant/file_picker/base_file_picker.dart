import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:file/file.dart';
import 'package:flutter/services.dart';

abstract class BaseFilePicker {
  Future<List<File>> pickFiles();

  Future<List<File>> handlePickerExecution() async {
    try {
      final files = await pickFiles();

      if (files.isEmpty) {
        throw AppLocalizationsResolver.get().aiResourceNotSelected;
      }

      return files;
    } on PlatformException catch (_) {
      throw AppLocalizationsResolver.get().aiResourceNotSelected;
    }
  }
}
