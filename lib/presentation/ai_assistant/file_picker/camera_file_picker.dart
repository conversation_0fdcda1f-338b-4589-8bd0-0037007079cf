import 'package:bitacora/presentation/ai_assistant/config/ai_generation_config.dart';
import 'package:bitacora/presentation/ai_assistant/file_picker/base_file_picker.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:hl_image_picker/hl_image_picker.dart';

class CameraFilePicker extends BaseFilePicker {
  final FileSystem _fs = FileSystemInjector.get();

  @override
  Future<List<File>> pickFiles() async {
    final cameraOptions = HLCameraOptions(
      recordVideoMaxSecond: AiGenerationConfig.recordVideoMaxSecond,
    );

    final result =
        await HLImagePicker().openCamera(cameraOptions: cameraOptions);
    return [_fs.file(result.path)];
  }
}
