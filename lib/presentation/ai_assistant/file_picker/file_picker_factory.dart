import 'package:bitacora/presentation/ai_assistant/file_picker/base_file_picker.dart';
import 'package:bitacora/presentation/ai_assistant/file_picker/camera_file_picker.dart';
import 'package:bitacora/presentation/ai_assistant/file_picker/document_file_picker.dart';
import 'package:bitacora/presentation/ai_assistant/file_picker/gallery_file_picker.dart';
import 'package:bitacora/presentation/ai_assistant/file_picker/video_file_picker.dart';
import 'package:bitacora/presentation/ai_assistant/generator/entry_from_generator.dart';

class FilePickerFactory {
  static BaseFilePicker create(EntryFromSource source) {
    switch (source) {
      case EntryFromSource.file:
        return DocumentFilePicker();
      case EntryFromSource.gallery:
        return GalleryFilePicker();
      case EntryFromSource.camera:
        return CameraFilePicker();
      case EntryFromSource.video:
        return VideoFilePicker();
      case EntryFromSource.audio:
        throw UnsupportedError(
            'Audio source is not supported by file picker factory');
    }
  }

  FilePickerFactory._();
}
