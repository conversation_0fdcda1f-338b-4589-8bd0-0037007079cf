import 'package:bitacora/presentation/ai_assistant/file_picker/base_file_picker.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:file_picker/file_picker.dart';

class DocumentFilePicker extends BaseFilePicker {
  final FileSystem _fs = FileSystemInjector.get();

  @override
  Future<List<File>> pickFiles() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
      allowMultiple: true,
    );

    return result?.files
            .map((e) => _fs.file(e.path!))
            .toList(growable: false) ??
        [];
  }
}
