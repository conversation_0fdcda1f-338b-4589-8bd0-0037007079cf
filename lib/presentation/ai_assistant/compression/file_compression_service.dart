import 'package:bitacora/presentation/ai_assistant/compression/compression_handler.dart';
import 'package:bitacora/presentation/ai_assistant/compression/image_compression_handler.dart';
import 'package:bitacora/presentation/ai_assistant/compression/video_compression_handler.dart';
import 'package:bitacora/presentation/ai_assistant/config/ai_generation_config.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:file/file.dart';
import 'package:flutter/material.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;

class FileCompressionService {
  final List<CompressionHandler> _handlers = [
    ImageCompressionHandler(),
    VideoCompressionHandler(),
  ];

  Future<List<File>> compressFiles(List<File> originalFiles) async {
    logger.i(
        '${AiGenerationConfig.compressionLogPrefix}:compressing ${originalFiles.length} items');

    final compressedFiles = await Future.wait(
      originalFiles.map((file) => _compressFile(file)),
    );

    logger.i('${AiGenerationConfig.compressionLogPrefix}:batch completed');
    return compressedFiles;
  }

  Future<File> _compressFile(File file) async {
    logger.i(
        '${AiGenerationConfig.compressionLogPrefix}:processing file: ${path.basename(file.path)}');

    final stagingDirectory = await _createStagingDirectory();
    final mimeType = lookupMimeType(file.path);

    if (mimeType == null) {
      logger.i(
          '${AiGenerationConfig.compressionLogPrefix}:unknown MIME type, using original: ${path.basename(file.path)}');
      return file;
    }

    final handler = _findHandler(mimeType);
    if (handler == null) {
      logger.i(
          '${AiGenerationConfig.compressionLogPrefix}:no handler found, using original: ${path.basename(file.path)}');
      return file;
    }

    final compressedFile = await handler.compress(file, stagingDirectory.path);
    return compressedFile ?? file;
  }

  Future<Directory> _createStagingDirectory() async {
    final key =
        ValueKey<String>(DateTime.now().millisecondsSinceEpoch.toString());
    return StorageUtils().getStagingDirectory(key);
  }

  CompressionHandler? _findHandler(String mimeType) {
    try {
      return _handlers.firstWhere((handler) => handler.canHandle(mimeType));
    } catch (e) {
      return null;
    }
  }
}
