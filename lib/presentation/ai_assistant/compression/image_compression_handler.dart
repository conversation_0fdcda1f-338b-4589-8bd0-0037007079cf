import 'package:bitacora/presentation/ai_assistant/compression/compression_handler.dart';
import 'package:bitacora/presentation/ai_assistant/config/ai_generation_config.dart';
import 'package:bitacora/util/attachment/attachment_compression.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:file/file.dart';
import 'package:path/path.dart' as path;

class ImageCompressionHandler implements CompressionHandler {
  final AttachmentCompression _imageCompressor = AttachmentCompression();

  @override
  bool canHandle(String mimeType) {
    return mimeType.startsWith(AiGenerationConfig.imageMimePrefix);
  }

  @override
  Future<File?> compress(File file, String stagingDirectoryPath) async {
    logger.i('${AiGenerationConfig.compressionLogPrefix}:compressing image: '
        '${path.basename(file.path)}');

    final compressedFile = await _imageCompressor.maybeCompressImage(
      file,
      stagingDirectoryPath,
      AiGenerationConfig.imageCompressionQuality,
    );

    if (compressedFile != null) {
      logger.i(
          '${AiGenerationConfig.compressionLogPrefix}:image compressed successfully: '
          '${path.basename(compressedFile.path)}');
      return compressedFile;
    } else {
      logger.i(
          '${AiGenerationConfig.compressionLogPrefix}:image compression skipped, using original: '
          '${path.basename(file.path)}');
      return null;
    }
  }
}
