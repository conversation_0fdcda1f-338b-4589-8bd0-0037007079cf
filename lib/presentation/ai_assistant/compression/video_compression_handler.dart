import 'package:bitacora/presentation/ai_assistant/compression/compression_handler.dart';
import 'package:bitacora/presentation/ai_assistant/config/ai_generation_config.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:path/path.dart' as path;

class VideoCompressionHandler implements CompressionHandler {
  final FileSystem _fs = FileSystemInjector.get();

  @override
  bool canHandle(String mimeType) {
    return mimeType.startsWith(AiGenerationConfig.videoMimePrefix);
  }

  @override
  Future<File?> compress(File file, String stagingDirectoryPath) async {
    logger.i(
        '${AiGenerationConfig.videoCompressionLogPrefix}:initializing compression for: '
        '${path.basename(file.path)}');

    final outputPath = _buildOutputPath(file, stagingDirectoryPath);
    logger.i(
        '${AiGenerationConfig.videoCompressionLogPrefix}:output path: $outputPath');

    final command = _buildFFmpegCommand(file.path, outputPath);

    logger.i(
        '${AiGenerationConfig.videoCompressionLogPrefix}:executing FFmpeg command');
    logger.i(
        '${AiGenerationConfig.videoCompressionLogPrefix}:FFmpeg command: $command');

    await FFmpegKit.execute(command);

    logger.i(
        '${AiGenerationConfig.videoCompressionLogPrefix}:FFmpeg execution completed');

    final compressedFile = _fs.file(outputPath);
    logger.i(
        '${AiGenerationConfig.videoCompressionLogPrefix}:compression finished: '
        '${path.basename(compressedFile.path)}');

    return compressedFile;
  }

  String _buildOutputPath(File file, String stagingDirectoryPath) {
    return '$stagingDirectoryPath/${path.basename(file.path)}${AiGenerationConfig.videoOutputExtension}';
  }

  String _buildFFmpegCommand(String inputPath, String outputPath) {
    return '-i $inputPath '
        '-vf scale=${AiGenerationConfig.videoScale} '
        '-c:v ${AiGenerationConfig.videoCodec} '
        '-preset ${AiGenerationConfig.videoPreset} '
        '-crf ${AiGenerationConfig.videoCrf} '
        '-c:a ${AiGenerationConfig.audioCodec} '
        '-b:a ${AiGenerationConfig.audioBitrate} '
        '$outputPath';
  }
}
