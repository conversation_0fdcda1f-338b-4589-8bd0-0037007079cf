import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:file/file.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:mime/mime.dart';

class SelectedFilesPreview extends StatelessWidget {
  final List<File> files;
  final void Function(File) onRemoveFile;

  const SelectedFilesPreview({
    super.key,
    required this.files,
    required this.onRemoveFile,
  });

  @override
  Widget build(BuildContext context) {
    if (files.isEmpty) return const SizedBox.shrink();

    return Container(
      height: 80,
      margin: const EdgeInsets.only(bottom: 16),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: files.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final file = files[index];
          return _FilePreviewItem(
            file: file,
            onRemove: () => onRemoveFile(file),
          );
        },
      ),
    );
  }
}

class _FilePreviewItem extends StatelessWidget {
  final File file;
  final VoidCallback onRemove;

  const _FilePreviewItem({
    required this.file,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Theme.brightnessOf(context) == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.onSurface.withAlpha(70),
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: _buildPreviewContent(context),
          ),
        ),
        Positioned(
          top: -8,
          right: -8,
          child: GestureDetector(
            onTap: onRemove,
            child: Container(
              width: 24,
              height: 24,
              decoration: const BoxDecoration(
                color: bitacoraRed,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewContent(BuildContext context) {
    final mimeType = lookupMimeType(file.path);

    if (mimeType?.startsWith('image/') == true) {
      return Image.file(
        file,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) =>
            _buildFallbackIcon(context, CupertinoIcons.photo),
      );
    } else if (mimeType?.startsWith('video/') == true) {
      return _buildFallbackIcon(context, Icons.video_camera_back_outlined);
    } else {
      return _buildFallbackIcon(context, CupertinoIcons.doc);
    }
  }

  Widget _buildFallbackIcon(BuildContext context, IconData icon) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white.withValues(alpha: 0.05),
      child: Icon(
        icon,
        color: Theme.of(context).colorScheme.onSurface,
        size: 32,
      ),
    );
  }
}
