import 'package:bitacora/l10n/app_localizations.dart';
import 'package:bitacora/presentation/ai_assistant/generator/entry_from_generator.dart';
import 'package:bitacora/presentation/ai_assistant/widgets/selected_files_preview.dart';
import 'package:bitacora/presentation/ai_assistant/ai_assistant_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class FilePickerWidget extends StatefulWidget {
  final void Function(BuildContext, EntryFromSource) onFileSelection;
  final void Function(BuildContext) onGenerate;

  const FilePickerWidget({
    super.key,
    required this.onFileSelection,
    required this.onGenerate,
  });

  @override
  State<FilePickerWidget> createState() => _FilePickerWidgetState();
}

class _FilePickerWidgetState extends State<FilePickerWidget> {
  final GlobalKey _buttonKey = GlobalKey();

  List<Widget> _buildMenuItems(BuildContext context) {
    final menuItems = [
      _MenuItemData(
        icon: CupertinoIcons.folder,
        title: AppLocalizations.of(context)!.aiAssistantFiles,
        source: EntryFromSource.file,
      ),
      _MenuItemData(
        icon: CupertinoIcons.photo_on_rectangle,
        title: AppLocalizations.of(context)!.aiAssistantGallery,
        source: EntryFromSource.gallery,
      ),
      _MenuItemData(
        icon: CupertinoIcons.camera,
        title: AppLocalizations.of(context)!.aiAssistantCamera,
        source: EntryFromSource.camera,
      ),
      _MenuItemData(
        icon: CupertinoIcons.videocam,
        title: AppLocalizations.of(context)!.aiAssistantRecordVideo,
        source: EntryFromSource.video,
      ),
      _MenuItemData(
        icon: CupertinoIcons.mic,
        title: AppLocalizations.of(context)!.aiAssistantAudio,
        source: EntryFromSource.audio,
      ),
    ];

    final List<Widget> widgets = [];
    for (int i = 0; i < menuItems.length; i++) {
      final item = menuItems[i];
      widgets.add(
        _MenuItemWidget(
          icon: item.icon,
          title: item.title,
          onTap: () {
            Navigator.pop(context);
            widget.onFileSelection(context, item.source);
          },
          isFirst: i == 0,
          isLast: i == menuItems.length - 1,
        ),
      );
      if (i < menuItems.length - 1) {
        widgets.add(const _DividerWidget());
      }
    }
    return widgets;
  }

  void _showCustomContextMenu(BuildContext context) {
    final double menuWidth = 200.0;
    final double menuHeight = 5 * 44.0;
    final double buttonCenterX = MediaQuery.sizeOf(context).width / 2;

    final RenderBox renderBox =
        _buttonKey.currentContext!.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final double menuY = offset.dy - menuHeight - 24.0;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.transparent,
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Stack(
          children: [
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.transparent,
              ),
            ),
            Positioned(
              left: buttonCenterX - (menuWidth / 2),
              top: menuY,
              child: ScaleTransition(
                scale: CurvedAnimation(
                  parent: animation,
                  curve: Curves.easeOutBack,
                ),
                alignment: Alignment.bottomCenter,
                child: Material(
                  color: const Color(0xFF2C2C2E),
                  borderRadius: BorderRadius.circular(13),
                  elevation: 8,
                  child: Container(
                    width: menuWidth,
                    decoration: BoxDecoration(
                      color: const Color(0xFF2C2C2E),
                      borderRadius: BorderRadius.circular(13),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: _buildMenuItems(context),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AiAssistantController>(
      builder: (context, controller, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SelectedFilesPreview(
              files: controller.selectedFiles,
              onRemoveFile: controller.removeSelectedFile,
            ),
            if (controller.hasSelectedFiles)
              _FilesSelectedButtons(
                buttonKey: _buttonKey,
                onAddMore: () => _showCustomContextMenu(context),
                onGenerate: () => widget.onGenerate(context),
              )
            else
              _InitialAddButton(
                buttonKey: _buttonKey,
                onTapDown: (detail) => _showCustomContextMenu(context),
              ),
          ],
        );
      },
    );
  }

  static Color _getButtonBackgroundColor(BuildContext context) {
    return Theme.brightnessOf(context) == Brightness.dark
        ? Colors.white.withValues(alpha: 0.1)
        : Theme.of(context).colorScheme.surface;
  }

  static BoxDecoration _getCircularButtonDecoration(BuildContext context) {
    return BoxDecoration(
      color: _getButtonBackgroundColor(context),
      shape: BoxShape.circle,
      border: Border.all(
        color: Theme.of(context).colorScheme.onSurface.withAlpha(70),
        width: 1,
      ),
    );
  }

  static EdgeInsets _getBottomMargin(BuildContext context) {
    return EdgeInsets.only(
      bottom: MediaQuery.paddingOf(context).bottom,
    );
  }
}

class _MenuItemData {
  final IconData icon;
  final String title;
  final EntryFromSource source;

  const _MenuItemData({
    required this.icon,
    required this.title,
    required this.source,
  });
}

class _CircularAddButton extends StatelessWidget {
  final GlobalKey? buttonKey;
  final VoidCallback? onTap;
  final void Function(TapDownDetails)? onTapDown;

  const _CircularAddButton({
    this.buttonKey,
    this.onTap,
    this.onTapDown,
  }) : assert(onTap != null || onTapDown != null,
            'Either onTap or onTapDown must be provided');

  @override
  Widget build(BuildContext context) {
    Widget button = Container(
      key: buttonKey,
      width: 56,
      height: 56,
      decoration: _FilePickerWidgetState._getCircularButtonDecoration(context),
      child: Icon(
        Icons.add,
        color: Theme.of(context).colorScheme.onSurface,
        size: 24,
      ),
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: button,
      );
    } else {
      return GestureDetector(
        onTapDown: onTapDown,
        child: button,
      );
    }
  }
}

class _MenuItemWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool isFirst;
  final bool isLast;

  const _MenuItemWidget({
    required this.icon,
    required this.title,
    required this.onTap,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(
              top: isFirst ? const Radius.circular(13) : Radius.zero,
              bottom: isLast ? const Radius.circular(13) : Radius.zero,
            ),
          ),
          child: Row(
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Icon(icon, color: Colors.white70, size: 20),
            ],
          ),
        ),
      ),
    );
  }
}

class _DividerWidget extends StatelessWidget {
  const _DividerWidget();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 0.5,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      color: Colors.white.withAlpha(50),
    );
  }
}

class _FilesSelectedButtons extends StatelessWidget {
  final GlobalKey buttonKey;
  final VoidCallback onAddMore;
  final VoidCallback onGenerate;

  const _FilesSelectedButtons({
    required this.buttonKey,
    required this.onAddMore,
    required this.onGenerate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: _FilePickerWidgetState._getBottomMargin(context),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _CircularAddButton(
            buttonKey: buttonKey,
            onTap: onAddMore,
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: onGenerate,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                color:
                    _FilePickerWidgetState._getButtonBackgroundColor(context),
                borderRadius: BorderRadius.circular(28),
                border: Border.all(
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(70),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.auto_awesome,
                    color: Theme.of(context).colorScheme.onSurface,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)!.aiAssistantGenerate,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _InitialAddButton extends StatelessWidget {
  final GlobalKey buttonKey;
  final void Function(TapDownDetails) onTapDown;

  const _InitialAddButton({
    required this.buttonKey,
    required this.onTapDown,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: _FilePickerWidgetState._getBottomMargin(context),
      child: _CircularAddButton(
        buttonKey: buttonKey,
        onTapDown: onTapDown,
      ),
    );
  }
}
