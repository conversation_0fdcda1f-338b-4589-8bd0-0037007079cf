import 'package:bitacora/presentation/ai_assistant/ai_assistant_controller.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entry_view.dart';
import 'package:flutter/material.dart';

class EntriesResultsView extends StatefulWidget {
  final List<CreatingEntry> creatingEntries;
  final ScrollController? scrollController;

  const EntriesResultsView({
    super.key,
    required this.creatingEntries,
    this.scrollController,
  });

  @override
  State<EntriesResultsView> createState() => _EntriesResultsViewState();
}

class _EntriesResultsViewState extends State<EntriesResultsView>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  final GlobalKey _latestEntryKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 30.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void didUpdateWidget(EntriesResultsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.creatingEntries.isNotEmpty &&
        oldWidget.creatingEntries.isEmpty) {
      _animationController.forward();
      _scrollToBottom();
    } else if (widget.creatingEntries.isEmpty &&
        oldWidget.creatingEntries.isNotEmpty) {
      _animationController.reverse();
    } else if (widget.creatingEntries.length !=
        oldWidget.creatingEntries.length) {
      _scrollToBottom();
    } else if (widget.creatingEntries.isNotEmpty &&
        oldWidget.creatingEntries.isNotEmpty) {
      final latestEntry = widget.creatingEntries
          .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);
      final oldLatestEntry = oldWidget.creatingEntries
          .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);

      if (latestEntry.state != oldLatestEntry.state &&
          latestEntry.state == EntryCreationState.completed) {
        _scrollToBottom();
      }
    }
  }

  void _scrollToBottom() {
    if (widget.scrollController?.hasClients == true &&
        widget.creatingEntries.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.scrollController?.hasClients == true) {
          widget.scrollController?.animateTo(
            widget.scrollController!.position.maxScrollExtent,
            duration: const Duration(milliseconds: 600),
            curve: Curves.easeOutCubic,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.creatingEntries.isEmpty) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: ShaderMask(
              shaderCallback: (Rect bounds) {
                return const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black,
                    Colors.black,
                    Colors.transparent,
                  ],
                  stops: [0.0, 0.15, 0.85, 1.0],
                ).createShader(bounds);
              },
              blendMode: BlendMode.dstIn,
              child: _buildEntriesList(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEntriesList() {
    final sortedEntries = List<CreatingEntry>.from(widget.creatingEntries)
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));

    return ListView.separated(
      controller: widget.scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      itemCount: sortedEntries.length + 2,
      separatorBuilder: (_, __) => const SizedBox(height: 16.0),
      itemBuilder: (context, index) {
        if (index == 0 || index == sortedEntries.length + 1) {
          return SizedBox(height: 16);
        }

        final newIndex = index - 1;
        final entry = sortedEntries[newIndex];
        final isLatest = newIndex == sortedEntries.length - 1;

        return _EntryResultItem(
          key: isLatest ? _latestEntryKey : null,
          entry: entry,
          isLatest: isLatest,
          index: newIndex,
        );
      },
    );
  }
}

class _EntryResultItem extends StatefulWidget {
  final CreatingEntry entry;
  final bool isLatest;
  final int index;

  const _EntryResultItem({
    super.key,
    required this.entry,
    required this.isLatest,
    required this.index,
  });

  @override
  State<_EntryResultItem> createState() => _EntryResultItemState();
}

class _EntryResultItemState extends State<_EntryResultItem>
    with TickerProviderStateMixin {
  late AnimationController _itemAnimationController;
  late Animation<double> _itemSlideAnimation;
  late Animation<double> _itemFadeAnimation;

  @override
  void initState() {
    super.initState();
    _itemAnimationController = AnimationController(
      duration: Duration(milliseconds: 400 + (widget.index * 100)),
      vsync: this,
    );

    _itemSlideAnimation = Tween<double>(
      begin: 20.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _itemAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _itemFadeAnimation = Tween<double>(
      begin: 0.0,
      end: widget.isLatest ? 1.0 : 0.4,
    ).animate(CurvedAnimation(
      parent: _itemAnimationController,
      curve: Curves.easeOut,
    ));

    Future.delayed(Duration(milliseconds: widget.index * 50), () {
      if (mounted) {
        _itemAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _itemAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _itemAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _itemSlideAnimation.value),
          child: Opacity(
            opacity: _itemFadeAnimation.value,
            child: _buildEntryCard(),
          ),
        );
      },
    );
  }

  Widget _buildEntryCard() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(
          color: Colors.grey.withAlpha(125),
          width: widget.isLatest ? 1.0 : 0.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 8.0),
        child: LogListEntryView(
          entry: widget.entry.listEntry,
          isSelectable: false,
          replaceRouteOnNavigate: true,
        ),
      ),
    );
  }
}
