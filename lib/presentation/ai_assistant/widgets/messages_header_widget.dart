import 'package:bitacora/presentation/ai_assistant/assistant_message.dart';
import 'package:bitacora/presentation/ai_assistant/config/ai_generation_config.dart';
import 'package:flutter/material.dart';

class MessagesHeaderWidget extends StatefulWidget {
  final List<AssistantMessage> assistantMessages;

  const MessagesHeaderWidget({
    super.key,
    required this.assistantMessages,
  });

  @override
  State<MessagesHeaderWidget> createState() => _MessagesHeaderWidgetState();
}

class _MessagesHeaderWidgetState extends State<MessagesHeaderWidget>
    with TickerProviderStateMixin {
  AnimationController? _fadeController;
  Animation<double>? _fadeAnimation;
  String _currentMessage = '';

  @override
  void initState() {
    super.initState();
    _updateCurrentMessage();
    _setupFadeAnimation();
    _startFadeAnimation();
  }

  @override
  void didUpdateWidget(MessagesHeaderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.assistantMessages != oldWidget.assistantMessages) {
      _updateCurrentMessage();
      _setupFadeAnimation();
      _startFadeAnimation();
    }
  }

  void _updateCurrentMessage() {
    if (widget.assistantMessages.isNotEmpty) {
      _currentMessage = widget.assistantMessages.last.content;
    } else {
      _currentMessage = '';
    }
  }

  void _setupFadeAnimation() {
    _fadeController?.dispose();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800), // Smooth fade duration
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController!,
      curve: Curves.easeOut,
    ));
  }

  void _startFadeAnimation() {
    _fadeController?.reset();
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted && _fadeController != null) {
        _fadeController!.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_fadeAnimation != null)
            FadeTransition(
              opacity: _fadeAnimation!,
              child: ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [
                    AiGenerationConfig.blue,
                    AiGenerationConfig.red,
                  ],
                  stops: [0.0, 1.0],
                ).createShader(bounds),
                child: Text(
                  _currentMessage,
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
