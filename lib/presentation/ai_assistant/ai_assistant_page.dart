import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/presentation/ai_assistant/ai_assistant_controller.dart';
import 'package:bitacora/presentation/ai_assistant/audio/simple_audio_recorder.dart';
import 'package:bitacora/presentation/ai_assistant/widgets/entries_results_view.dart';
import 'package:bitacora/presentation/ai_assistant/widgets/file_picker_widget.dart';
import 'package:bitacora/presentation/ai_assistant/widgets/messages_header_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AiAssistantPage extends StatefulWidget {
  const AiAssistantPage({super.key});

  @override
  State<AiAssistantPage> createState() => _AiAssistantPageState();
}

class _AiAssistantPageState extends State<AiAssistantPage> {
  late AiAssistantController _controller;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    final activeOrganization = context.read<ActiveOrganization>();
    _controller = AiAssistantController(activeOrganization);
    _scrollController = ScrollController();

    _controller.addListener(_onControllerChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.initializeGreeting(context);
    });
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    if (_scrollController.hasClients &&
        _controller.assistantMessages.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _controller,
      child: Scaffold(
        body: Consumer<AiAssistantController>(
          builder: (context, controller, child) {
            return SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: double.infinity,
                    height: kToolbarHeight,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: IconButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            icon: Icon(Icons.close),
                          ),
                        ),
                        Align(
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              const Text(
                                'CORA',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: 0.5,
                                ),
                              ),
                              const SizedBox(width: 4.0),
                              Opacity(
                                opacity: 0.5,
                                child: Text(
                                  '0.1',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 0.5,
                                    color: Colors.white70,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  MessagesHeaderWidget(
                    assistantMessages: controller.assistantMessages,
                  ),
                  Expanded(
                    child: EntriesResultsView(
                      creatingEntries: controller.creatingEntries,
                      scrollController: _scrollController,
                    ),
                  ),
                  SizedBox(height: 16.0),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.paddingOf(context).bottom,
                      left: 16.0,
                      right: 16.0,
                      top: 16.0,
                    ),
                    child: _BottomContent(controller: controller),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

class _BottomContent extends StatelessWidget {
  final AiAssistantController controller;

  const _BottomContent({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _AudioRecorder(controller: controller),
        const SizedBox(height: 16.0),
        Flexible(
          child: FilePickerWidget(
            onFileSelection: controller.handleFileSelection,
            onGenerate: controller.handleGeneration,
          ),
        ),
      ],
    );
  }
}

class _AudioRecorder extends StatelessWidget {
  final AiAssistantController controller;

  const _AudioRecorder({required this.controller});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: controller.audioRecorderControllerNotifier,
      builder: (context, audioRecorderController, _) {
        if (audioRecorderController != null) {
          return SizedBox(
            height: 48.0,
            child: SimpleAudioRecorder(
              controller: controller.audioRecorderControllerNotifier.value!,
              onCancel: controller.handleAudioRecordingCancel,
              onSave: controller.handleAudioRecordingSave,
            ),
          );
        }
        return const SizedBox();
      },
    );
  }
}
