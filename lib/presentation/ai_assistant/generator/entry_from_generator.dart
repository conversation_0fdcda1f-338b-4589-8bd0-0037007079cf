import 'dart:async';

import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/ai_generator.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/storage_service.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_draft/entry_draft.dart';
import 'package:bitacora/domain/entry_source/entry_source.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';

import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';
import 'package:bitacora/util/duration_tracker.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/toast/flutter_toast_utils.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/cupertino.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

enum EntryFromSource { file, gallery, camera, audio, video }

abstract class EntryFromGenerator {
  final EntryFormGeneratorContextSnapshot contextSnapshot;
  final DurationTracker durationTracker = DurationTracker();
  final FileSystem fs = FileSystemInjector.get();

  EntryFromGenerator(this.contextSnapshot);

  AiGenerator get aiGenerator => contextSnapshot.read<AiGenerator>();

  String get projectsText => contextSnapshot
      .read<ProjectCache>()
      .value!
      .where((p) => p.name!.value != '_' && p.name!.value != '?')
      .map((p) => '- id: ${p.remoteId!.apiValue}, name: ${p.name!.apiValue}')
      .join('\n');

  Future<Entry?> generateDraft() async {
    try {
      final files = await pickFiles();

      final now = Clock().now();
      final day = contextSnapshot.read<ActiveLogDay>().value!;
      final time = LogTime.fromDateTime(DateTime.now());
      final author = contextSnapshot.read<ActiveSession>().value!.user;
      final createdAt = EntryCreatedAt(now);
      final updatedAt = EntryUpdatedAt(now);
      final appLocalizations = AppLocalizationsResolver.get();
      return Entry(
        day: day,
        time: time,
        author: author,
        createdAt: createdAt,
        updatedAt: updatedAt,
        extension: Worklog(
          quantity: WorklogQuantity(null),
          title: WorklogTitle(appLocalizations.generatedWithAiTitle),
          type: WorklogType.values[0],
          project: await _buildDraftProject(),
          sublocation: WorklogSublocation(null),
        ),
        comments: EntryComments(appLocalizations.generatedWithAiDescription),
        attachments:
            await Future.wait(files.map((f) => _processFileToAttachment(f!))),
        location: await getEntryLocation(),
        source: EntrySource(
          type: EntrySourceType.mobileAi,
          metadata: null,
        ),
        draft: EntryDraft(
          createdAt: EntryDraftCreatedAt(now),
          updatedAt: EntryDraftUpdatedAt(now),
          type: EntryDraftType.ai,
        ),
      );
    } catch (e, s) {
      _handleError(e, s);
      return null;
    }
  }

  Future<Project?> _buildDraftProject() async {
    final db = contextSnapshot.read<Repository>();
    final organization = contextSnapshot.read<ActiveOrganization>().value!;
    final context = db.context();
    final scopedContext = context.copyWith(
        queryScope: context.db.queryScope(orgId: organization.id));

    return (await context.db.query(
            ProjectByNameRepositoryQuery(ProjectName(kDefaultProjectName)),
            context: scopedContext)) ??
        Project(
                name: ProjectName(kDefaultProjectName),
                organization: organization)
            .copyWith(isSyncable: ProjectIsSyncable(true));
  }

  Future<Entry?> generate(Entry draft) async {
    try {
      final entry = await generationStrategy(draft);
      return entry;
    } catch (e, s) {
      _handleError(e, s);

      final localizations = AppLocalizationsResolver.get();
      return draft.copyWith(
        extension: (draft.extension as Worklog).copyWith(
          title: WorklogTitle(localizations.generatedErrorWithAiTitle),
        ),
        comments: EntryComments(localizations.generatedErrorWithAiDescription),
        draft: null,
      );
    }
  }

  @protected
  Future<Entry> generationStrategy(Entry draft);

  @protected
  Future<List<String>> uploadTempFiles(List<File> files) async {
    final storageService = contextSnapshot.read<StorageService>();
    return Future.wait(files.map(
        (f) => storageService.uploadFile(f.readAsBytesSync(), f.basename)));
  }

  @protected
  Future<void> deleteTempFiles(List<File> files) async {
    await Future.wait(files.map(
        (f) => contextSnapshot.read<StorageService>().deleteFile(f.basename)));
  }

  Future<List<File?>> pickFiles();

  void _handleError(dynamic e, StackTrace s) {
    logger.e(e);
    logger.e(s);
    FluttertoastUtils().showToast(msg: e.toString());
  }

  @protected
  Future<Entry> doGenerate(List<Part> parts) {
    final day = contextSnapshot.read<ActiveLogDay>().value!.apiValue;
    final orgRemoteId =
        contextSnapshot.read<ActiveOrganization>().value!.remoteId!.apiValue;
    return aiGenerator.entry.generate(
      aiGenerator,
      Content.multi([TextPart(projectsText), ...parts]),
      {
        'day': day,
        'organization_id': orgRemoteId,
      },
    );
  }

  @protected
  Future<Attachment> _processFileToAttachment(File file) async {
    logger.i('daylog-ai-button: processRecordingToAttachment');

    final processedRelativePath = await AttachmentUtils().processForSave(file);

    return Attachment(
      s3Key: AttachmentS3Key(const Uuid().v4()),
      name: AttachmentName(path.basename(processedRelativePath)),
      isUploaded: AttachmentIsUploaded(false),
      isDownloaded: AttachmentIsDownloaded(true),
      transferState:
          AttachmentTransferStateValueObject(AttachmentTransferState.na),
      path: AttachmentPath(processedRelativePath),
    );
  }

  Future<LatLngValueObject?> getEntryLocation() async {
    try {
      final position = await LocationUtils().determinePosition();
      return LatLngValueObject(position.toLatLng());
    } catch (e, s) {
      logger.e(e);
      logger.e(s);
      return null;
    }
  }
}

class EntryFormGeneratorContextSnapshot extends ContextSnapshot {
  EntryFormGeneratorContextSnapshot(super.context);

  EntryFormGeneratorContextSnapshot.fromContextSnapshot(super.snapshot)
      : super.fromSnapshot();

  @override
  List<ValueShot> get valueShots => [
        ValueShot.navigator(),
        ValueShot.provider<Repository>(),
        ValueShot.provider<ApiHelper>(),
        ValueShot.provider<ApiTranslator>(),
        ValueShot.provider<AiGenerator>(),
        ValueShot.provider<StorageService>(),
        ValueShot.provider<ActiveLogDay>(),
        ValueShot.provider<ProjectCache>(),
        ValueShot.provider<AnalyticsLogger>(),
        ValueShot.provider<ActiveOrganization>(),
        ValueShot.provider<ActiveSession>(),
      ];
}
