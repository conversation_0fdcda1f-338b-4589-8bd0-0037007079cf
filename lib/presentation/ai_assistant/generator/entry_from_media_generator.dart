import 'dart:async';

import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_draft/value/entry_draft_type.dart';
import 'package:bitacora/presentation/ai_assistant/audio/simple_audio_recorder_controller.dart';
import 'package:bitacora/presentation/ai_assistant/compression/file_compression_service.dart';
import 'package:bitacora/presentation/ai_assistant/config/ai_generation_config.dart';
import 'package:bitacora/presentation/ai_assistant/file_picker/file_picker_factory.dart';
import 'package:bitacora/presentation/ai_assistant/generator/entry_from_generator.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:file/file.dart';
import 'package:file/local.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/foundation.dart';
import 'package:mime/mime.dart';

class EntryFromMediaGenerator extends EntryFromGenerator {
  final List<File>? preSelectedFiles;
  final EntryFromSource? source;
  final ValueNotifier<SimpleAudioRecorderController?>?
      audioRecorderControllerNotifier;
  final FileCompressionService _compressionService = FileCompressionService();

  EntryFromMediaGenerator(
    super.contextSnapshot, {
    this.preSelectedFiles,
    this.source,
    this.audioRecorderControllerNotifier,
  });

  @override
  Future<List<File?>> pickFiles() async {
    if (preSelectedFiles != null && preSelectedFiles!.isNotEmpty) {
      return preSelectedFiles!.cast<File?>();
    }

    if (source != null) {
      if (source == EntryFromSource.audio) {
        return _pickAudioFile();
      } else {
        return _pickMediaFiles();
      }
    }

    return [];
  }

  Future<List<File?>> _pickAudioFile() async {
    if (audioRecorderControllerNotifier?.value == null) {
      throw StateError('Audio recorder controller is null');
    }

    final file = await audioRecorderControllerNotifier!.value!.startRecording();
    audioRecorderControllerNotifier!.value!.dispose();
    audioRecorderControllerNotifier!.value = null;
    return [file];
  }

  Future<List<File?>> _pickMediaFiles() async {
    final filePicker = FilePickerFactory.create(source!);
    return await filePicker.handlePickerExecution();
  }

  @override
  Future<Entry> generationStrategy(Entry draft) async {
    durationTracker.markStart();

    final files = await _getOriginalFiles(draft);
    final hasAudioFiles = _hasAudioFiles(files);
    final hasMediaFiles = _hasMediaFiles(files);

    if (hasAudioFiles && hasMediaFiles) {
      return _generateFromMixedMedia(draft, files);
    } else if (hasAudioFiles) {
      return _generateFromAudio(draft, files);
    } else {
      return _generateFromMedia(draft, files);
    }
  }

  bool _hasAudioFiles(List<File> files) {
    return files.any((file) {
      final mimeType = lookupMimeType(file.path);
      return mimeType?.startsWith('audio/') ?? false;
    });
  }

  bool _hasMediaFiles(List<File> files) {
    return files.any((file) {
      final mimeType = lookupMimeType(file.path);
      return mimeType?.startsWith('image/') == true ||
          mimeType?.startsWith('video/') == true;
    });
  }

  Future<Entry> _generateFromAudio(Entry draft, List<File> files) async {
    logger.i('ai-generation:from audio process started');

    final audioFile = files.first;
    final fileUri = (await uploadTempFiles([audioFile])).first;
    final mimeType = lookupMimeType(audioFile.path)!;
    final part = FileData(mimeType, fileUri);
    final generatedEntry = await doGenerate([part]);

    unawaited(deleteTempFiles([audioFile]));

    final finalEntry = generatedEntry.copyWith(
      id: draft.id,
      attachments: draft.attachments,
      draft: draft.draft!.copyWith(type: EntryDraftType.normal),
    );

    logger.i(
        'ai-generation:from audio process completed successfully [$durationTracker]');
    return finalEntry;
  }

  Future<Entry> _generateFromMedia(Entry draft, List<File> files) async {
    logger.i('${AiGenerationConfig.imageLogPrefix} process started');

    try {
      final compressedFiles = await _compressionService.compressFiles(files);
      final fileUris = await uploadTempFiles(compressedFiles);
      final parts = await _createFileParts(fileUris);
      final generatedEntry = await doGenerate(parts);

      unawaited(deleteTempFiles(compressedFiles));

      final finalEntry = _buildFinalEntry(draft, generatedEntry);

      logger.i(
          '${AiGenerationConfig.imageLogPrefix} process completed successfully [$durationTracker]');
      return finalEntry;
    } catch (e) {
      logger.e('${AiGenerationConfig.imageLogPrefix} process failed: $e');
      rethrow;
    }
  }

  Future<Entry> _generateFromMixedMedia(Entry draft, List<File> files) async {
    logger.i('ai-generation:from mixed media process started');

    try {
      final audioFiles = <File>[];
      final mediaFiles = <File>[];

      // Separate audio and media files
      for (final file in files) {
        final mimeType = lookupMimeType(file.path);
        if (mimeType?.startsWith('audio/') == true) {
          audioFiles.add(file);
        } else {
          mediaFiles.add(file);
        }
      }

      final List<File> processedFiles = [];

      // Process media files (compress them)
      if (mediaFiles.isNotEmpty) {
        final compressedFiles =
            await _compressionService.compressFiles(mediaFiles);
        processedFiles.addAll(compressedFiles);
      }

      // Add audio files without compression
      processedFiles.addAll(audioFiles);

      final fileUris = await uploadTempFiles(processedFiles);
      final parts = await _createFileParts(fileUris);
      final generatedEntry = await doGenerate(parts);

      unawaited(deleteTempFiles(processedFiles));

      final finalEntry = _buildFinalEntry(draft, generatedEntry);

      logger.i(
          'ai-generation:from mixed media process completed successfully [$durationTracker]');
      return finalEntry;
    } catch (e) {
      logger.e('ai-generation:from mixed media process failed: $e');
      rethrow;
    }
  }

  Future<List<File>> _getOriginalFiles(Entry draft) async {
    final fs = LocalFileSystem();
    return Future.wait(
      draft.attachments!.map((attachment) async {
        final absolutePath =
            await AttachmentUtils().getAbsolutePath(attachment.path!);
        return fs.file(absolutePath);
      }),
    );
  }

  Future<List<FileData>> _createFileParts(List<String> fileUris) async {
    return Future.wait(
      fileUris.map((uri) async {
        final mimeType = lookupMimeType(uri)!;
        return FileData(mimeType, uri);
      }),
    );
  }

  Entry _buildFinalEntry(Entry draft, Entry generatedEntry) {
    return generatedEntry.copyWith(
      id: draft.id,
      attachments: draft.attachments,
      draft: draft.draft!.copyWith(type: EntryDraftType.normal),
      author: draft.author,
      location: draft.location,
      source: draft.source,
    );
  }
}
