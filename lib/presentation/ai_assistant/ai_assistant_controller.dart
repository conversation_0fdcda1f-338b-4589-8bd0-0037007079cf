import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/ai/ai_credits_manager.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/project/value/project_is_syncable.dart';
import 'package:bitacora/infrastructure/ai/shared_preferences_ai_credits_manager.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:bitacora/presentation/ai_assistant/audio/simple_audio_recorder_controller.dart';
import 'package:bitacora/presentation/ai_assistant/generator/entry_from_generator.dart';
import 'package:bitacora/presentation/ai_assistant/generator/entry_from_media_generator.dart';
import 'package:bitacora/presentation/ai_assistant/assistant_message.dart';
import 'package:bitacora/util/list_entry/list_entry.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'package:file/file.dart';

enum EntryCreationState {
  draft,
  processing,
  completed,
  error,
}

class CreatingEntry {
  final String id;
  final ListEntry listEntry;
  final EntryCreationState state;
  final DateTime createdAt;

  const CreatingEntry({
    required this.id,
    required this.listEntry,
    required this.state,
    required this.createdAt,
  });

  CreatingEntry copyWith({
    String? id,
    ListEntry? listEntry,
    EntryCreationState? state,
    DateTime? createdAt,
  }) {
    return CreatingEntry(
      id: id ?? this.id,
      listEntry: listEntry ?? this.listEntry,
      state: state ?? this.state,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class AiAssistantController extends ChangeNotifier {
  final ValueNotifier<SimpleAudioRecorderController?>
      audioRecorderControllerNotifier = ValueNotifier(null);
  final ActiveOrganization _activeOrganization;

  final List<CreatingEntry> _creatingEntries = [];
  final List<AssistantMessage> _assistantMessages = [];
  final List<File> _selectedFiles = [];
  bool _disposed = false;

  AiCreditsManager? _creditsManager;
  int _remainingCredits = AiCreditsManager.dailyCredits;
  bool _creditsInitialized = false;

  List<CreatingEntry> get creatingEntries =>
      List.unmodifiable(_creatingEntries);

  List<AssistantMessage> get assistantMessages =>
      List.unmodifiable(_assistantMessages);

  List<File> get selectedFiles => List.unmodifiable(_selectedFiles);

  bool get hasSelectedFiles => _selectedFiles.isNotEmpty;

  void initializeGreeting(BuildContext context) {
    if (_assistantMessages.isEmpty) {
      final activeSession = context.read<ActiveSession>();
      final userName = activeSession.value?.user.name?.displayValue ??
          AppLocalizations.of(context)!.aiAssistantDefaultUser;

      final greetingMessage =
          AssistantMessage.greeting(userName.split(' ').first);
      _assistantMessages.add(greetingMessage);
      notifyListeners();
    }
  }

  bool get mounted => !_disposed;

  int get remainingCredits => _remainingCredits;

  bool get hasCredits => _remainingCredits > 0;

  bool get creditsInitialized => _creditsInitialized;

  AiAssistantController(this._activeOrganization) {
    _initializeCredits();
    _activeOrganization.addListener(_onOrganizationChanged);
  }

  void _onOrganizationChanged() {
    _initializeCredits();
  }

  int get _currentOrganizationId => _activeOrganization.value!.id!.value;

  Future<void> _initializeCredits() async {
    try {
      _creditsManager = await SharedPreferencesAiCreditsManager.init();
      await _updateRemainingCredits();
      _creditsInitialized = true;
      notifyListeners();
    } catch (e) {
      logger
          .e('ai-assistant-controller: Error initializing credits manager: $e');
      _creditsInitialized = true;
      notifyListeners();
    }
  }

  Future<void> _updateRemainingCredits() async {
    if (_creditsManager != null) {
      try {
        _remainingCredits =
            await _creditsManager!.getRemainingCredits(_currentOrganizationId);
        notifyListeners();
      } catch (e) {
        logger
            .e('ai-assistant-controller: Error updating remaining credits: $e');
      }
    }
  }

  Future<bool> _canUseFeature() async {
    if (_creditsManager == null) return false;
    try {
      return await _creditsManager!.canUseFeature(_currentOrganizationId);
    } catch (e) {
      logger
          .e('ai-assistant-controller: Error checking if can use feature: $e');
      return false;
    }
  }

  Future<void> _incrementUsedCredits() async {
    if (_creditsManager != null) {
      try {
        await _creditsManager!.incrementUsedCredits(_currentOrganizationId);
        await _updateRemainingCredits();
      } catch (e) {
        logger
            .e('ai-assistant-controller: Error incrementing used credits: $e');
      }
    }
  }

  Future<String> getCreditsExhaustedMessage() async {
    if (_creditsManager != null) {
      try {
        return await _creditsManager!.getCreditsExhaustedMessage();
      } catch (e) {
        logger.e(
            'ai-assistant-controller: Error getting credits exhausted message: $e');
        return 'Has alcanzado el límite diario de 15 generaciones de IA. Los créditos se renovarán al final del día.';
      }
    }
    return 'Has alcanzado el límite diario de 15 generaciones de IA. Los créditos se renovarán al final del día.';
  }

  Future<void> refreshCredits() async {
    await _updateRemainingCredits();
  }

  Future<void> handleFileSelection(
    BuildContext context,
    EntryFromSource source,
  ) async {
    final contextSnapshot = EntryFormGeneratorContextSnapshot(context);

    final canUse = await _canUseFeature();

    if (!canUse) {
      // Add credits exhausted message
      final message = await getCreditsExhaustedMessage();
      final creditsMessage = AssistantMessage(
        id: 'credits_exhausted_${DateTime.now().millisecondsSinceEpoch}',
        content: message,
        type: AssistantMessageType.error,
        createdAt: DateTime.now(),
      );
      _assistantMessages.add(creditsMessage);
      notifyListeners();
      return;
    }

    if (source == EntryFromSource.audio) {
      audioRecorderControllerNotifier.value = SimpleAudioRecorderController();
    }

    final generator = EntryFromMediaGenerator(
      contextSnapshot,
      source: source,
      audioRecorderControllerNotifier: audioRecorderControllerNotifier,
    );

    try {
      final files = await generator.pickFiles();
      final nonNullFiles = files.where((f) => f != null).cast<File>().toList();

      if (nonNullFiles.isNotEmpty) {
        _selectedFiles.addAll(nonNullFiles);

        _assistantMessages.removeWhere(
            (msg) => msg.type == AssistantMessageType.fileSelected);

        final fileSelectedMessage = AssistantMessage.fileSelected();
        _assistantMessages.add(fileSelectedMessage);

        notifyListeners();
      }
    } catch (e, s) {
      logger.e(e);
      logger.e(s);
    }
  }

  Future<void> handleGeneration(BuildContext context) async {
    if (_selectedFiles.isEmpty &&
        audioRecorderControllerNotifier.value == null) {
      return;
    }

    if (!hasCredits) {
      final message = await getCreditsExhaustedMessage();
      final creditsMessage = AssistantMessage(
        id: 'credits_exhausted_${DateTime.now().millisecondsSinceEpoch}',
        content: message,
        type: AssistantMessageType.error,
        createdAt: DateTime.now(),
      );
      _assistantMessages.add(creditsMessage);
      notifyListeners();
      return;
    }

    final contextSnapshot = EntryFormGeneratorContextSnapshot(context);
    final db = contextSnapshot.read<Repository>();

    final generator = EntryFromMediaGenerator(
      contextSnapshot,
      preSelectedFiles: _selectedFiles,
    );

    final draft = await generator.generateDraft();
    if (draft == null) {
      return;
    }

    final entryId = await db.entry.save(db.context(), draft);
    final draftWithId = draft.copyWith(id: entryId);

    final creatingEntryId = DateTime.now().millisecondsSinceEpoch.toString();
    final creatingEntry = CreatingEntry(
      id: creatingEntryId,
      listEntry: ListEntry(draftWithId, draftWithId.worklog?.project),
      state: EntryCreationState.draft,
      createdAt: DateTime.now(),
    );

    _creatingEntries.add(creatingEntry);

    _assistantMessages
        .removeWhere((msg) => msg.type == AssistantMessageType.invitation);

    final generatingMessage = AssistantMessage.generating(creatingEntryId);
    _assistantMessages.add(generatingMessage);
    notifyListeners();

    _selectedFiles.clear();

    _updateEntryState(creatingEntryId, EntryCreationState.processing);

    try {
      final entry = await generator.generate(draftWithId);
      if (entry == null) {
        _updateEntryState(creatingEntryId, EntryCreationState.error);
        return;
      }

      await db.entry.save(
        db.context(),
        entry.copyWith(
          extension: entry.worklog!.copyWith(
            project: entry.worklog!.project!.copyWith(
              isSyncable: ProjectIsSyncable(true),
            ),
          ),
        ),
      );

      await db.entryDraft.deleteByEntry(db.context(), entryId!);

      await _incrementUsedCredits();

      final finalCreatingEntry = creatingEntry.copyWith(
        listEntry: ListEntry(entry.copyWith(withoutDraft: true), null),
        state: EntryCreationState.completed,
      );

      final index = _creatingEntries.indexWhere((e) => e.id == creatingEntryId);
      if (index != -1) {
        _creatingEntries[index] = finalCreatingEntry;
      }

      _assistantMessages
          .removeWhere((msg) => msg.id == '${creatingEntryId}_generating');
      final completedMessage =
          AssistantMessage.completed(creatingEntryId, finalCreatingEntry);
      _assistantMessages.add(completedMessage);

      notifyListeners();
    } catch (e) {
      _updateEntryState(creatingEntryId, EntryCreationState.error);

      _assistantMessages
          .removeWhere((msg) => msg.id == '${creatingEntryId}_generating');
      final errorMessage = AssistantMessage.error(creatingEntryId);
      _assistantMessages.add(errorMessage);

      Future.delayed(const Duration(milliseconds: 2000), () {
        if (mounted) {
          final invitationMessage = AssistantMessage.invitation();
          _assistantMessages.add(invitationMessage);
          notifyListeners();
        }
      });
    }
  }

  void _updateEntryState(String entryId, EntryCreationState newState) {
    final index = _creatingEntries.indexWhere((e) => e.id == entryId);
    if (index != -1) {
      _creatingEntries[index] =
          _creatingEntries[index].copyWith(state: newState);
      if (!_disposed) {
        notifyListeners();
      }
    }
  }

  void removeCreatingEntry(String entryId) {
    _creatingEntries.removeWhere((e) => e.id == entryId);

    _assistantMessages.removeWhere((msg) => msg.id.startsWith(entryId));
    notifyListeners();
  }

  void removeSelectedFile(File file) {
    _selectedFiles.remove(file);
    notifyListeners();
  }

  void clearSelectedFiles() {
    _selectedFiles.clear();
    notifyListeners();
  }

  void handleAudioRecordingCancel() async {
    await audioRecorderControllerNotifier.value?.cancelRecording();
    audioRecorderControllerNotifier.value = null;
  }

  void handleAudioRecordingSave() async {
    await audioRecorderControllerNotifier.value!.stopRecording();
  }

  @override
  void dispose() {
    _disposed = true;
    _activeOrganization.removeListener(_onOrganizationChanged);
    audioRecorderControllerNotifier.dispose();
    super.dispose();
  }
}
