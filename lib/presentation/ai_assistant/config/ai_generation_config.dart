import 'dart:ui';

/// Configuration constants for AI generation processes
class AiGenerationConfig {
  static const int maxSelectedAssets = 20;
  static const int maxVideoDuration = 30;
  static const bool convertLivePhotosToJPG = true;
  static const bool usedCameraButton = false;
  static const int recordVideoMaxSecond = 30;

  static const int imageCompressionQuality = 75;
  static const String videoScale = '854:480';
  static const String videoCodec = 'libx264';
  static const String videoPreset = 'ultrafast';
  static const int videoCrf = 28;
  static const String audioCodec = 'aac';
  static const String audioBitrate = '96k';
  static const String videoOutputExtension = '.mp4';

  static const String logPrefix = 'ai-generation';
  static const String imageLogPrefix = '$logPrefix:from image';
  static const String compressionLogPrefix = '$logPrefix:compression';
  static const String videoCompressionLogPrefix =
      '$logPrefix:video compression';

  static const String imageMimePrefix = 'image/';
  static const String videoMimePrefix = 'video/';

  static const Color blue = Color(0xFF4784F3);
  static const Color blueLight = Color(0xFF7096EE);
  static const Color red = Color(0xFFD96670);

  AiGenerationConfig._();
}
