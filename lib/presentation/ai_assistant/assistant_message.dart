import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/ai_assistant/ai_assistant_controller.dart';

enum AssistantMessageType {
  greeting,
  fileSelected,
  generating,
  completed,
  error,
  invitation,
}

class AssistantMessage {
  final String id;
  final String content;
  final AssistantMessageType type;
  final DateTime createdAt;
  final CreatingEntry? entry;

  const AssistantMessage({
    required this.id,
    required this.content,
    required this.type,
    required this.createdAt,
    this.entry,
  });

  AssistantMessage copyWith({
    String? id,
    String? content,
    AssistantMessageType? type,
    DateTime? createdAt,
    CreatingEntry? entry,
  }) {
    return AssistantMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      entry: entry ?? this.entry,
    );
  }

  static AssistantMessage greeting(String userName) {
    final localizations = AppLocalizationsResolver.get();
    final greetingMessages = [
      localizations.aiAssistantGreeting1(userName),
      localizations.aiAssistantGreeting2(userName),
      localizations.aiAssistantGreeting3(userName),
      localizations.aiAssistantGreeting4(userName),
      localizations.aiAssistantGreeting5(userName),
    ];

    final randomMessage = greetingMessages[DateTime.now().millisecond % greetingMessages.length];

    return AssistantMessage(
      id: 'greeting_${DateTime.now().millisecondsSinceEpoch}',
      content: randomMessage,
      type: AssistantMessageType.greeting,
      createdAt: DateTime.now(),
    );
  }

  static AssistantMessage fileSelected() {
    final localizations = AppLocalizationsResolver.get();
    final fileSelectedMessages = [
      localizations.aiAssistantFileSelected1,
      localizations.aiAssistantFileSelected2,
      localizations.aiAssistantFileSelected3,
      localizations.aiAssistantFileSelected4,
      localizations.aiAssistantFileSelected5,
    ];

    final randomMessage = fileSelectedMessages[DateTime.now().millisecond % fileSelectedMessages.length];

    return AssistantMessage(
      id: 'file_selected_${DateTime.now().millisecondsSinceEpoch}',
      content: randomMessage,
      type: AssistantMessageType.fileSelected,
      createdAt: DateTime.now(),
    );
  }

  static AssistantMessage generating(String entryId) {
    final localizations = AppLocalizationsResolver.get();
    final generatingMessages = [
      localizations.aiAssistantGenerating1,
      localizations.aiAssistantGenerating2,
      localizations.aiAssistantGenerating3,
      localizations.aiAssistantGenerating4,
      localizations.aiAssistantGenerating5,
    ];

    final randomMessage = generatingMessages[DateTime.now().millisecond % generatingMessages.length];

    return AssistantMessage(
      id: '${entryId}_generating',
      content: randomMessage,
      type: AssistantMessageType.generating,
      createdAt: DateTime.now(),
    );
  }

  static AssistantMessage completed(String entryId, CreatingEntry entry) {
    final localizations = AppLocalizationsResolver.get();
    final completedMessages = [
      localizations.aiAssistantCompleted1,
      localizations.aiAssistantCompleted2,
      localizations.aiAssistantCompleted3,
      localizations.aiAssistantCompleted4,
      localizations.aiAssistantCompleted5,
    ];

    final randomMessage = completedMessages[DateTime.now().millisecond % completedMessages.length];

    return AssistantMessage(
      id: '${entryId}_completed',
      content: randomMessage,
      type: AssistantMessageType.completed,
      createdAt: DateTime.now(),
      entry: entry,
    );
  }

  static AssistantMessage error(String entryId) {
    return AssistantMessage(
      id: '${entryId}_error',
      content: AppLocalizationsResolver.get().aiAssistantError,
      type: AssistantMessageType.error,
      createdAt: DateTime.now(),
    );
  }

  static AssistantMessage invitation() {
    final localizations = AppLocalizationsResolver.get();
    final invitationMessages = [
      localizations.aiAssistantInvitation1,
      localizations.aiAssistantInvitation2,
      localizations.aiAssistantInvitation3,
      localizations.aiAssistantInvitation4,
      localizations.aiAssistantInvitation5,
    ];

    final randomMessage = invitationMessages[DateTime.now().millisecond % invitationMessages.length];

    return AssistantMessage(
      id: 'invitation_${DateTime.now().millisecondsSinceEpoch}',
      content: randomMessage,
      type: AssistantMessageType.invitation,
      createdAt: DateTime.now(),
    );
  }
}
