import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$InventorylogDbContract tests', () {
    test('Create $InventorylogDbContract', () {
      expectRemovingSpaces(
        const InventorylogDbContract().create,
        '''
        CREATE TABLE inventorylog (
          i_id INTEGER PRIMARY KEY AUTOINCREMENT,
          i_remoteId INTEGER UNIQUE,
          i_type INTEGER NOT NULL,
          i_quantity INTEGER NOT NULL,
          i_itemName TEXT NOT NULL,
          i_sourceProjectId INTEGER,
          i_destProjectId INTEGER,
          i_sourceSublocation TEXT,
          i_destSublocation TEXT,
          i_provider TEXT,
          i_costPrice INTEGER,
          i_salePrice INTEGER,
          i_reason TEXT,
          i_paymentStatus INTEGER,
          i_priceIsUnit INTEGER
        )
        ''',
      );
    });
  });
}
