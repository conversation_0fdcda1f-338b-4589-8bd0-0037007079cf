import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$PersonnellogDbContract tests', () {
    test('Create $PersonnellogDbContract', () {
      expectRemovingSpaces(
        const PersonnellogDbContract().create,
        '''
        CREATE TABLE personnellog (
          pe_id INTEGER PRIMARY KEY AUTOINCREMENT,
          pe_remoteId INTEGER UNIQUE,
          pe_projectId INTEGER NOT NULL,
          pe_name TEXT NOT NULL,
          pe_sublocation TEXT,
          pe_entrance INTEGER,
          pe_exit INTEGER,
          pe_minutes INTEGER
        )
        ''',
      );
    });
  });
}
