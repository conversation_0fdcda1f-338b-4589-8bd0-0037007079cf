import 'package:bitacora/domain/ai/ai_credits_manager.dart';
import 'package:bitacora/infrastructure/ai/shared_preferences_ai_credits_manager.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('SharedPreferencesAiCreditsManager Organization Scoping Tests', () {
    late SharedPreferencesAiCreditsManager creditsManager;

    setUpAll(() async {
      SharedPreferences.setMockInitialValues({});
      creditsManager = await SharedPreferencesAiCreditsManager.init();
    });

    test('should scope credits per organization', () async {
      const org1Id = 1;
      const org2Id = 2;
      await creditsManager.resetCredits(org1Id);
      await creditsManager.resetCredits(org2Id);

      await creditsManager.incrementUsedCredits(org1Id);
      await creditsManager.incrementUsedCredits(org1Id);

      await creditsManager.incrementUsedCredits(org2Id);

      final org1UsedCredits = await creditsManager.getUsedCredits(org1Id);
      final org2UsedCredits = await creditsManager.getUsedCredits(org2Id);

      expect(org1UsedCredits, 2);
      expect(org2UsedCredits, 1);

      final org1RemainingCredits =
          await creditsManager.getRemainingCredits(org1Id);
      final org2RemainingCredits =
          await creditsManager.getRemainingCredits(org2Id);

      expect(org1RemainingCredits, AiCreditsManager.dailyCredits - 2);
      expect(org2RemainingCredits, AiCreditsManager.dailyCredits - 1);
    });

    test('should reset credits per organization', () async {
      const org1Id = 1;
      const org2Id = 2;
      await creditsManager.resetCredits(org1Id);
      await creditsManager.resetCredits(org2Id);

      await creditsManager.incrementUsedCredits(org1Id);
      await creditsManager.incrementUsedCredits(org2Id);

      await creditsManager.resetCredits(org1Id);

      final org1UsedCredits = await creditsManager.getUsedCredits(org1Id);
      final org2UsedCredits = await creditsManager.getUsedCredits(org2Id);

      expect(org1UsedCredits, 0);
      expect(org2UsedCredits, 1);
    });

    test('should check feature availability per organization', () async {
      const orgId = 1;
      await creditsManager.resetCredits(orgId);

      for (int i = 0; i < AiCreditsManager.dailyCredits; i++) {
        await creditsManager.incrementUsedCredits(orgId);
      }

      final canUseForOrg = await creditsManager.canUseFeature(orgId);
      expect(canUseForOrg, false);
    });
  });
}
