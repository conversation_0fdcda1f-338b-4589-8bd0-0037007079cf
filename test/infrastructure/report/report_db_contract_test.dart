import 'package:bitacora/infrastructure/report/report_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$ReportDbContract tests', () {
    test('Create $ReportDbContract', () {
      expectRemovingSpaces(
        const ReportDbContract().create,
        '''
        CREATE TABLE report (
          r_id INTEGER PRIMARY KEY AUTOINCREMENT,
          r_uuid TEXT NOT NULL UNIQUE,
          r_path TEXT,
          r_isDownloaded INTEGER,
          r_createdAt INTEGER NOT NULL,
          r_postParams TEXT NOT NULL,
          r_getParams TEXT NOT NULL,
          r_organizationId INTEGER NOT NULL
        )
        ''',
      );
    });
  });
}
