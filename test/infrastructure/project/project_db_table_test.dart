import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/infrastructure/entry/entry_db_table.dart';
import 'package:bitacora/infrastructure/project/project_db_table.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sqflite/sqflite.dart';
import 'package:test/test.dart';

import '../../domain/common/mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../entry/mocks.dart';
import '../mocks.dart';

void main() {
  group('$ProjectDbTable test', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Find by Organization', () async {
      final organization = mockOrganization(withId: true);
      final executor = mockDatabaseExecutor();
      final table = ProjectDbTable();
      final context = _mockDbContext(
        executor: executor,
        fields: table.fieldsBuilder.build(),
      );

      await table.findByOrganization(context, organization.id!);

      verify(() => executor.query(table.tableName,
          columns: columnsFromFields(context.fields!.map.values),
          where: 'p_organizationId = ? AND p_isSyncable = ?',
          whereArgs: [organization.id!.dbValue, 1],
          orderBy: 'p_name'));
    });

    test('names', () async {
      const pattern = 'Awesome Sublocation';
      final dbScopeUtils = mockDbQueryScopeUtils();
      final result = List.generate(3, (i) => {'p_name': '$pattern $i'});
      final table = ProjectDbTable();

      await withInjected<DbQueryScopeUtils>(
          dbScopeUtils,
          () => testRawQuery(
                query: '''
          SELECT p_name, MAX(e_updatedAt)
          [SCOPE FROM JOIN]
          WHERE [SCOPE WHERE]
          AND p_name LIKE ?
          AND p_name != ?
          GROUP BY p_name
          ORDER BY MAX(e_updatedAt) DESC, p_name ASC
          LIMIT 10
          OFFSET 0
        ''',
                args: [
                  ...kScopedQueryArg,
                  pattern,
                  kDefaultProjectName,
                ],
                result: result,
                action: (context) => table.names(
                  context.copyWith(
                      queryScope: mockQueryScope(
                    pattern: pattern,
                    byOrg: true,
                    orgId: const LocalId(1),
                  )),
                ),
              ));
    });

    test('sublocation', () async {
      const field = 'x';
      const pattern = 'pattern';
      final result = List.generate(3, (i) => {field: '$i'});
      final dbScopeUtils = mockDbQueryScopeUtils();
      final table = ProjectDbTable();

      await withInjected<DbQueryScopeUtils>(
        dbScopeUtils,
        () => testRawQuery(
          query: '''
          SELECT x, MAX(e_updatedAt) FROM (
              SELECT w_sublocation as x, e_updatedAt
              [SCOPE FROM JOIN]
              LEFT JOIN worklog 
              ON (w_id = e_extensionId AND e_extensionType = ?)
              WHERE [SCOPE WHERE]
              AND x LIKE ?
              UNION
              SELECT pe_sublocation as x, e_updatedAt
              [SCOPE FROM JOIN]
              LEFT JOIN personnellog
              ON (pe_id = e_extensionId AND e_extensionType = ?)
              WHERE [SCOPE WHERE]
              AND x LIKE ?
              UNION
              SELECT i_sourceSublocation as x, e_updatedAt
              [SCOPE FROM JOIN]
              LEFT JOIN inventorylog
              ON (i_id = e_extensionId AND e_extensionType = ?)
              WHERE [SCOPE WHERE]
              AND x LIKE ?
              UNION
              SELECT i_destSublocation as x, e_updatedAt
              [SCOPE FROM JOIN]
              LEFT JOIN inventorylog
              ON (i_id = e_extensionId AND e_extensionType = ?)
              WHERE [SCOPE WHERE]
              AND x LIKE ?
            )
            GROUP BY x
            ORDER BY MAX(e_updatedAt) DESC, x ASC
            LIMIT 10
            OFFSET  0
          ''',
          args: [
            ExtensionType.worklog.dbValue,
            ...kScopedQueryArg,
            pattern,
            ExtensionType.personnellog.dbValue,
            ...kScopedQueryArg,
            pattern,
            ExtensionType.inventorylog.dbValue,
            ...kScopedQueryArg,
            pattern,
            ExtensionType.inventorylog.dbValue,
            ...kScopedQueryArg,
            pattern,
          ],
          result: result,
          action: (context) => table.sublocations(
            context.copyWith(
              queryScope: mockQueryScope(
                pattern: pattern,
                byOrg: true,
                orgId: const LocalId(1),
              ),
            ),
          ),
        ),
      );
    });

    test('On Post Delete', () async {
      final organization = mockOrganization(withId: true);
      final entryDbTable = mockEntryDbTable();
      final db = _mockDbRepository(entryDbTable: entryDbTable);
      final executor = mockDatabaseExecutor();
      final context = _mockDbContext(db: db, executor: executor);
      final table = ProjectDbTable();

      await table.onPreDelete(context, organization.id!);

      verify(
        () => executor.delete(
          'projectEntriesProject',
          where: 'pep_projectId = ?',
          whereArgs: [organization.id!.dbValue],
        ),
      );
    });
  });
}

DbContext _mockDbContext({
  DatabaseExecutor? executor,
  bool hasAnyOfFields = false,
  DbFields? fields,
  DbRepository? db,
  QueryScope? queryScope,
}) {
  executor ??= mockDatabaseExecutor();
  db ??= _mockDbRepository();
  final mock = MockDbContext();
  queryScope ??= mockQueryScope();
  when(() => mock.executor).thenAnswer((_) => Future.value(executor));
  when(() => mock.hasAnyOfFields(any())).thenReturn(hasAnyOfFields);
  when(() => mock.fields).thenReturn(fields);
  when(() => mock.queryScope).thenReturn(queryScope);
  when(() => mock.db).thenReturn(db);
  return mock;
}

DbRepository _mockDbRepository({
  EntryDbTable? entryDbTable,
}) {
  final mock = MockDbRepository();
  entryDbTable ??= mockEntryDbTable();
  final cache = mockRepositoryCache();
  when(() => mock.entry).thenReturn(entryDbTable);
  when(() => mock.cache).thenReturn(cache);
  return mock;
}
