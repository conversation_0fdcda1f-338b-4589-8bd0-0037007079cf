import 'dart:async';

import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/user/user_db_table.dart';
import 'package:mocktail/mocktail.dart';

import '../../domain/common/mocks.dart';

class MockUserDbTable extends Mock implements UserDbTable {}

class MockUserModelTranslator extends Mock implements ModelTranslator<User> {}

UserDbTable mockUserDbTable({
  LocalId? savedId,
  StreamController<Mutation<User>>? controller,
}) {
  final mock = MockUserDbTable();
  when(() => mock.safeSave(any(), any())).thenAnswer((invocation) {
    return Future.value(
        savedId ?? LocalId(invocation.positionalArguments[1].remoteId.value));
  });
  when(() => mock.getMutations()).thenAnswer((_) {
    final streamController =
        controller ?? StreamController<Mutation<User>>.broadcast();
    return streamController.stream;
  });
  return mock;
}

ModelTranslator<User> mockUserModelTranslator({
  bool prepareToMap = true,
  bool prepareFromMap = true,
}) {
  final mock = MockUserModelTranslator();
  prepareMockModelTranslator(
    mock,
    prepareToMap: prepareToMap,
    fromMapBuilder: prepareFromMap
        ? (map) => User(
              remoteId: RemoteId(map['id']),
              email: UserEmail(map['email']),
            )
        : null,
  );
  return mock;
}
