import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/auth/auth_session_user_repository_query.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/auth/auth_mixed_db_repository.dart';
import 'package:bitacora/infrastructure/auth/current_api_token.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../domain/common/mocks.dart';
import '../../mocktail_fallback_values.dart';
import 'mocks.dart';

void main() {
  group('$AuthMixedDbRepository tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Injects same injector', () {
      expect(
        AuthRepositoryInjector(),
        AuthRepositoryInjector(),
      );
    });

    test('Injector provides', () {
      final db = MockRepository();

      final authRepository =
          AuthRepositoryInjector().get(db) as AuthMixedDbRepository;

      expect(authRepository.db, db);
    });

    test('Load session from Shared Preferences without authToken', () async {
      SharedPreferences.setMockInitialValues({});
      final db = MockRepository();
      final currentToken = mockCurrentApiToken();
      final authRepository =
          AuthRepositoryInjector().get(db) as AuthMixedDbRepository;

      final session =
          await withInjected(currentToken, () => authRepository.load());

      expect(session, null);
    });

    test('Load session from SharedPreferences', () async {
      final user = mockUser();
      const authToken = 'aeiou';
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.authToken: authToken,
        SharedPreferencesKeys.activeUserId: user.id!.value,
      });
      final db = MockRepository();
      final currentToken = mockCurrentApiToken();
      final authRepository =
          AuthRepositoryInjector().get(db) as AuthMixedDbRepository;
      final expectedSession =
          Session(token: const SessionToken(authToken), user: user);
      whenQuery<AuthSessionUserRepositoryQuery, User?>(db, (_) => user);

      final session =
          await withInjected(currentToken, () => authRepository.load());

      expect(session!.user.id!.value, expectedSession.user.id!.value);
      expect(session.user.name!.value, expectedSession.user.name!.value);
      expect(session.user.email!.value, expectedSession.user.email!.value);
      expect(session.token.value, expectedSession.token.value);
    });

    test('Save session in SharedPreferences and CurrentApiToken', () async {
      SharedPreferences.setMockInitialValues({});
      final user = mockUser();
      const token = 'aeiou';
      final db = MockRepository();
      final currentToken = mockCurrentApiToken();
      final authRepository =
          AuthRepositoryInjector().get(db) as AuthMixedDbRepository;

      await withInjected<CurrentApiToken>(
        currentToken,
        () => authRepository.save(Session(
          token: const SessionToken(token),
          user: user,
        )),
      );

      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getInt(SharedPreferencesKeys.activeUserId), user.id!.value);
      verify(() => currentToken.saveToken(token)).called(1);
    });

    test('Successful nuke', () async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.authToken: 'aeiou',
        SharedPreferencesKeys.activeUserId: 9,
      });
      final db = MockRepository();
      final currentToken = mockCurrentApiToken();
      final authRepository =
          AuthRepositoryInjector().get(db) as AuthMixedDbRepository;

      await withInjected<CurrentApiToken>(
          currentToken, () => authRepository.nuke());

      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getInt(SharedPreferencesKeys.activeUserId), null);
      verify(() => currentToken.nuke()).called(1);
    });
  });
}
