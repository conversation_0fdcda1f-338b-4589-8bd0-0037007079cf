import 'dart:async';

import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/infrastructure/access/access_db_table.dart';
import 'package:mocktail/mocktail.dart';

import '../../domain/common/mocks.dart';

class MockAccessDbTable extends Mock implements AccessDbTable {}

class MockAccessModelTranslator extends Mock
    implements ModelTranslator<Access> {}

AccessDbTable mockAccessDbTable({
  LocalId savedId = const LocalId(1),
  StreamController<Mutation<Access>>? mutationsController,
}) {
  final mock = MockAccessDbTable();
  when(() => mock.replace(any(), any())).thenAnswer((_) => Future.value());
  when(() => mock.getMutations()).thenAnswer((_) {
    final controller =
        mutationsController ?? StreamController<Mutation<Access>>.broadcast();
    return controller.stream;
  });
  return mock;
}

ModelTranslator<Access> mockAccessModelTranslator({
  bool prepareToMap = true,
  bool prepareFromMap = true,
}) {
  final mock = MockAccessModelTranslator();
  prepareMockModelTranslator(
    mock,
    prepareToMap: prepareToMap,
    fromMapBuilder: prepareFromMap
        ? (map) => Access(resourceId: AccessResourceId(map['resource_id']))
        : null,
  );
  return mock;
}
