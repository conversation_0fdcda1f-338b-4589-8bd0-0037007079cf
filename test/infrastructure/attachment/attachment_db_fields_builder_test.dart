import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry/entry_db_fields_builder.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../db_fields_test_util.dart';
import '../entry/mocks.dart';
import '../mocks.dart';

void main() {
  group('$AttachmentDbFieldsBuilder tests', () {
    test('id', () {
      final fields = AttachmentDbFieldsBuilder().build();
      expect(fields.map[AttachmentField.id]!.value({'a_id': 12}),
          const LocalId(12));
      expectEmptyFieldThrows(fields, AttachmentField.id);
    });

    test('s3key', () {
      final s3key = AttachmentS3Key('key');
      final fields = AttachmentDbFieldsBuilder().s3Key().build();

      expectField(fields, AttachmentField.s3Key, 'a_s3Key', s3key);
      expectEmptyFieldThrows(fields, AttachmentField.s3Key);
    });

    test('name', () {
      final name = AttachmentName('Martino');
      final fields = AttachmentDbFieldsBuilder().name().build();

      expectField(fields, AttachmentField.name, 'a_name', name);
      expectEmptyFieldThrows(fields, AttachmentField.name);
    });

    test('isUploaded', () {
      const key = AttachmentField.isUploaded;
      final value = AttachmentIsUploaded(true);
      final fields = AttachmentDbFieldsBuilder().isUploaded().build();

      expectField(fields, key, 'a_isUploaded', value);
      expectEmptyField(fields, key, AttachmentIsUploaded(false));
    });

    test('isDownloaded', () {
      const key = AttachmentField.isDownloaded;
      final value = AttachmentIsDownloaded(true);
      final fields = AttachmentDbFieldsBuilder().isDownloaded().build();

      expectField(fields, key, 'a_isDownloaded', value);
      expectEmptyField(fields, key, AttachmentIsDownloaded(false));
    });

    test('transferState', () {
      const key = AttachmentField.transferState;
      final inProgress = AttachmentTransferStateValueObject(
          AttachmentTransferState.inProgress);
      final na = AttachmentTransferStateValueObject(AttachmentTransferState.na);
      final fields = AttachmentDbFieldsBuilder().transferState().build();

      expectField(fields, key, 'a_transferState', inProgress);
      expectEmptyField(fields, key, na);
    });

    test('transferAttempts', () {
      const key = AttachmentField.transferAttempts;
      const value = AttachmentTransferAttempts(4);
      final fields = AttachmentDbFieldsBuilder().transferAttempts().build();

      expectField(fields, key, 'a_transferAttempts', value);
      expectEmptyField(fields, key, const AttachmentTransferAttempts(0));
    });

    test('path', () {
      const key = AttachmentField.path;
      const value = AttachmentPath('/path/');
      final fields = AttachmentDbFieldsBuilder().path().build();

      expectField(fields, key, 'a_path', value);
      expectEmptyField(fields, key, const AttachmentPath(null));
    });

    test('comments', () {
      const key = AttachmentField.comments;
      const value = AttachmentComments('comments');
      final fields = AttachmentDbFieldsBuilder().comments().build();

      expectField(fields, key, 'a_comments', value);
      expectEmptyField(fields, key, const AttachmentComments(''));
    });

    test('entry', () async {
      const key = AttachmentField.entry;
      const entry = Entry(id: LocalId(134));
      final fields = AttachmentDbFieldsBuilder()
          .entry(EntryDbFieldsBuilder().build())
          .build();
      final context = MockDbContext();
      final entryDbTable = MockEntryDbTable();
      when(() => entryDbTable.find(context, entry.id!))
          .thenAnswer((_) => Future.value(entry));
      final db = MockDbRepository();
      when(() => db.entry).thenReturn(entryDbTable);
      when(() => context.nested(key, props: any(named: 'props')))
          .thenReturn(context);
      when(() => context.db).thenReturn(db);

      final nested = await fields.map[key]!
          .nested(context, {'a_entryId': entry.id!.dbValue}) as Entry;

      expect(nested.id, entry.id);
    });

    test('entry [empty]', () async {
      const key = AttachmentField.entry;
      final fields = AttachmentDbFieldsBuilder()
          .entry(EntryDbFieldsBuilder().build())
          .build();

      await expectNestedIsNull(fields, key);
    });
  });
}
