import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$OutgoingMutationDbContract tests', () {
    test('Create $OutgoingMutationDbContract', () {
      expectRemovingSpaces(
        const OutgoingMutationDbContract().create,
        '''
        CREATE TABLE outgoingMutation (
          om_id INTEGER PRIMARY KEY AUTOINCREMENT,
          om_key INTEGER NOT NULL,
          om_statusCode INTEGER,
          om_failedAttempts INTEGER NOT NULL DEFAULT 0,
          om_mutationType INTEGER NOT NULL,
          om_modelType INTEGER NOT NULL,
          om_modelId INTEGER,
          om_modelRemoteId INTEGER,
          om_organizationId INTEGER NOT NULL
        )
        ''',
      );
    });
  });
}
