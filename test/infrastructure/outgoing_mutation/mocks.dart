import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_table.dart';
import 'package:mocktail/mocktail.dart';

class MockOutgoingMutationDbTable extends Mock
    implements OutgoingMutationDbTable {}

class MockOutgoingMutationModelTranslator extends Mock
    implements ModelTranslator<OutgoingMutation> {}

OutgoingMutationDbTable mockOutgoingMutationDbTable({
  LocalId? savedId,
}) {
  final mock = MockOutgoingMutationDbTable();
  when(() => mock.save(any(), any())).thenAnswer((_) => Future.value(savedId));
  when(() => mock.delete(any(), any())).thenAnswer((_) => Future.value(1));
  return mock;
}
