import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_cache.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_lock.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/sqflite_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sqflite/sqflite.dart';

class MockDbRepository extends Mock implements DbRepository {}

class MockDbContext extends Mock implements DbContext {}

class MockDbLock extends Mock implements DbLock {}

class MockDbQueryScopeUtils extends Mock implements DbQueryScopeUtils {}

class MockDbField extends Mock implements DbField {}

class MockApiTranslator extends Mock implements ApiTranslator {}

class MockDatabase extends Mock implements Database {}

class MockTransaction extends Mock implements Transaction {}

class MockDatabaseExecutor extends Mock implements DatabaseExecutor {}

class MockSqfliteUtils extends Mock implements SqfliteUtils {}

class DbContextTxnMatcher extends CustomMatcher {
  DbContextTxnMatcher(matcher)
      : super('DbContext with transaction that is', 'transaction', matcher);

  @override
  Transaction? featureValueOf(actual) => (actual as DbContext).txn;
}

DatabaseExecutor mockDatabaseExecutor({List<Map<String, Object?>>? result}) {
  final mock = MockDatabaseExecutor();
  when(
    () => mock.rawQuery(any(), any()),
  ).thenAnswer(
    (_) => Future.value(result ?? []),
  );
  when(
    () => mock.query(
      any(),
      distinct: any(named: 'distinct'),
      columns: any(named: 'columns'),
      where: any(named: 'where'),
      whereArgs: any(named: 'whereArgs'),
      groupBy: any(named: 'groupBy'),
      having: any(named: 'having'),
      orderBy: any(named: 'orderBy'),
      limit: any(named: 'limit'),
      offset: any(named: 'offset'),
    ),
  ).thenAnswer(
    (_) => Future.value(result ?? []),
  );
  when(
    () => mock.update(
      any(),
      any(),
      where: any(named: 'where'),
      whereArgs: any(named: 'whereArgs'),
    ),
  ).thenAnswer((invocation) => Future.value(1));
  when(
    () => mock.delete(
      any(),
      where: any(named: 'where'),
      whereArgs: any(named: 'whereArgs'),
    ),
  ).thenAnswer((_) => Future.value(1));
  return mock;
}

void prepareDbTransaction(Repository db, DbContext context, Transaction txn) {
  final list = [txn];
  when(() => db.transaction<void>(any())).thenAnswer(
    (invocation) => invocation.positionalArguments[0](
      context.copyWith(txn: list.removeAt(0)),
    ),
  );
}

/// Sqflite

void verifyRawQuery(
  DatabaseExecutor executor,
  String query,
  List<dynamic> args,
) {
  final captured =
      verify(() => executor.rawQuery(captureAny(), captureAny())).captured;

  expect(_superTrim(captured[0]), _superTrim(query));
  expect(captured[1], args);
}

Future<void> testRawQuery({
  required String query,
  required List<dynamic> args,
  required List<Map<String, Object?>> result,
  required Future<void> Function(DbContext context) action,
}) async {
  final executor = mockDatabaseExecutor(result: result);
  final db = MockDbRepository();
  final context = DbContext(db: db);
  when(() => db.cache).thenReturn(RepositoryCache());
  when(() => db.executor(any())).thenAnswer((_) => Future.value(executor));

  await action(context);

  final captured =
      verify(() => executor.rawQuery(captureAny(), captureAny())).captured;
  expect(_superTrim(captured[0]), _superTrim(query));
  expect(captured[1], args);
}

Future<void> testQuery({
  required DbTable dbTable,
  bool? distinct,
  String? where,
  List<Object>? whereArgs,
  String? groupBy,
  String? having,
  String? orderBy,
  int? limit,
  int? offset,
  List<Map<String, Object?>>? result,
  required Future<void> Function(DbContext context) action,
}) async {
  final executor = mockDatabaseExecutor(result: result ?? []);
  final db = MockDbRepository();
  final context = DbContext(db: db, fields: dbTable.fieldsBuilder.build());
  when(() => db.cache).thenReturn(RepositoryCache());
  when(() => db.executor(any())).thenAnswer((_) => Future.value(executor));

  await action(context);

  verify(
    () => executor.query(
      dbTable.tableName,
      distinct: distinct ?? any(named: 'distinct'),
      columns: any(named: 'columns'),
      where: where ?? any(named: 'where'),
      whereArgs: whereArgs ?? any(named: 'whereArgs'),
      groupBy: groupBy ?? any(named: 'groupBy'),
      having: having ?? any(named: 'having'),
      orderBy: orderBy ?? any(named: 'orderBy'),
      limit: limit ?? any(named: 'limit'),
      offset: offset ?? any(named: 'offset'),
    ),
  );
}

String _superTrim(String s) {
  return s.replaceAll('\n', ' ').replaceAll(RegExp(r'\s+'), ' ').trim();
}

const kScopedQueryArg = [290196];

DbQueryScopeUtils mockDbQueryScopeUtils({
  String? fromJoin,
  String? where,
  List<int> queryArg = kScopedQueryArg,
}) {
  final mock = MockDbQueryScopeUtils();
  when(() => mock.fromJoin(any())).thenReturn(fromJoin ?? '[SCOPE FROM JOIN]');
  when(() => mock.where(any())).thenReturn(where ?? '[SCOPE WHERE]');
  when(() => mock.queryArg(any())).thenReturn(queryArg);
  return mock;
}

DbLock mockDbLock() {
  final mock = MockDbLock();
  when(() => mock.verify()).thenAnswer((invocation) => Future.value());
  return mock;
}
