import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/infrastructure/tag/tag_db_table.dart';
import 'package:test/test.dart';

import '../../domain/common/mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../mocks.dart';

void main() {
  group('$TagDbTable tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });
    // FIXME: Other tests...
    test('names', () {
      const field = 't_name';
      const pattern = 'pattern';
      final result = List.generate(3, (i) => {field: '$i'});
      final filterOut = List.generate(3, (i) => '-$i');
      final table = TagDbTable();
      testRawQuery(
        query: '''
          SELECT t_name FROM tag
          WHERE t_organizationId = ?
          AND t_name LIKE ?
          AND t_name NOT IN (?,?,?)
          ORDER BY t_name ASC
          LIMIT 10
          OFFSET 0
        ''',
        args: [
          1,
          pattern,
          ...filterOut,
        ],
        result: result,
        action: (context) => table.names(
          context.copyWith(
            queryScope: mockQueryScope(
                pattern: pattern, byOrg: true, orgId: const LocalId(1)),
          ),
          filterOut,
        ),
      );
    });
  });
}
