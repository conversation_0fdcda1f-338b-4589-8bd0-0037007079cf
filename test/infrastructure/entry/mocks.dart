import 'dart:async';

import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry/entry_db_table.dart';
import 'package:mocktail/mocktail.dart';

class MockEntryDbTable extends Mock implements EntryDbTable {}

class MockEntryDbTranslator extends Mock implements DbTranslator<Entry> {}

EntryDbTable mockEntryDbTable({
  Entry? found,
  StreamController<Mutation<Entry>>? mutationsController,
}) {
  final mock = MockEntryDbTable();
  when(() => mock.fieldsBuilder).thenAnswer((_) => EntryDbFieldsBuilder());
  when(() => mock.find(any(), any())).thenAnswer((_) => Future.value(found));
  when(() => mock.save(any(), any()))
      .thenAnswer((_) => Future.value(const LocalId(1)));
  when(() => mock.getMutations()).thenAnswer((_) {
    final controller =
        mutationsController ?? StreamController<Mutation<Entry>>.broadcast();
    return controller.stream;
  });
  when(() => mock.delete(any(), any())).thenAnswer((_) => Future.value(1));
  when(() => mock.cleanUp(any())).thenAnswer((_) => Future.value());
  return mock;
}
