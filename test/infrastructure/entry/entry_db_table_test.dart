import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_table.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/limit_offset_cursor.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sqflite/sqflite.dart';
import 'package:test/test.dart';

import '../../domain/common/mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../../test_util.dart';
import '../mocks.dart';
import 'mocks.dart';

void main() {
  group('$EntryDbTable test', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('FindDaylog without ghost entries', () async {
      const userId = LocalId(321);
      const logDay = LogDay(19831222);
      const query = '''
          SELECT e_id FROM projectEntriesProject 
          LEFT JOIN entry e ON e_id = pep_entryId 
          LEFT JOIN project ON p_id = pep_projectId 
          LEFT JOIN access_entry ON e_id = ae_entryId 
          LEFT JOIN openState ON e_openStateId = os_id 
          WHERE 
            p_organizationId = ? 
            AND p_isSyncable = 1
            AND ( ae_permission & 2 = 2 
              OR ( ae_permission & 32 = 32 
                AND ( e_authorId = 321 OR e_assigneeId = 321 ) 
              ) 
            ) 
            AND (e_day = ? ) 
            AND (e_openStateId IS NULL )
          GROUP BY e_id 
          ORDER BY e_time 
          LIMIT 10 OFFSET 0
        ''';
      final queryArgs = [1, logDay.dbValue];

      await _testLoadsDaylog(userId, logDay, false, query, queryArgs);
    });

    test('FindDaylog with ghost entries', () async {
      const userId = LocalId(321);
      const logDay = LogDay(19831222);
      const query = '''
          SELECT e_id FROM projectEntriesProject 
          LEFT JOIN entry e ON e_id = pep_entryId 
          LEFT JOIN project ON p_id = pep_projectId 
          LEFT JOIN access_entry ON e_id = ae_entryId
          LEFT JOIN openState ON e_openStateId = os_id 
          WHERE p_organizationId = ? 
            AND p_isSyncable = 1 
            AND ( ae_permission & 2 = 2
              OR ( ae_permission & 32 = 32 
                AND ( e_authorId = 321 OR e_assigneeId = 321 ) 
              ) 
            ) 
            AND (e_day = ? 
              OR ( e_openStateId IS NOT NULL 
                AND e_day <= ? 
                AND os_endDay >= ? 
              ) 
            ) 
            AND (e_openStateId IS NULL
              OR (os_progress != ? 
                AND NOT EXISTS ( 
                  SELECT 1 FROM entry 
                  LEFT JOIN progresslog ON e_extensionId = pr_id 
                  WHERE e_day = ? 
                    AND e_extensionType = ? 
                    AND pr_entryId = e.e_id 
                ) 
              ) 
            ) 
          GROUP BY e_id ORDER BY e_time LIMIT 10 OFFSET 0
        ''';
      final queryArgs = [
        1,
        logDay.dbValue,
        logDay.dbValue,
        logDay.dbValue,
        100,
        logDay.dbValue,
        ExtensionType.progresslog.dbValue,
      ];

      await _testLoadsDaylog(userId, logDay, true, query, queryArgs);
    });

    test('assignees', () async {
      final users = List.generate(3, (_) => mockUser());
      final executor = mockDatabaseExecutor(
        result: users
            .map(
              (u) => {
                'u_name': u.name!.dbValue,
                'u_email': u.email!.dbValue,
              },
            )
            .toList(),
      );
      final dbQueryScopeUtils = mockDbQueryScopeUtils();
      final queryScope = mockQueryScope(byOrg: true, orgId: const LocalId(1));
      final context =
          _mockDbContext(executor: executor, queryScope: queryScope);
      final table = EntryDbTable();

      final result = await withInjected<DbQueryScopeUtils>(
          dbQueryScopeUtils, () => table.assignees(context));

      final expectedResult = users.map((u) => u.nameEmailDisplayValue);
      expect(result, [...expectedResult, ...expectedResult]);

      final captured =
          verify(() => executor.rawQuery(captureAny(), captureAny())).captured;
      expectRemovingSpaces(
        captured[0],
        '''
        SELECT u_id, u_name, u_email, MAX(e_updatedAt)
        [SCOPE FROM JOIN]
        LEFT JOIN user ON e_assigneeId = u_id
        WHERE [SCOPE WHERE]
        AND (u_name LIKE ? OR u_email LIKE ?)
        GROUP BY u_name, u_email
        ORDER BY MAX(e_updatedAt) DESC, u_name ASC
        LIMIT 10
        ''',
      );
      expect(captured[1], [...kScopedQueryArg, '', '']);
      expectRemovingSpaces(
        captured[2],
        '''
        SELECT u_name, u_email
        FROM user
        LEFT JOIN userOrganization ON u_id = uo_userId
        WHERE uo_organizationId = ?
        AND (u_name LIKE ? OR u_email LIKE ?)
        AND u_id NOT IN (?,?,?) 
        ORDER BY u_name ASC
        LIMIT 7
        ''',
      );
      expect(captured[3], [...kScopedQueryArg, '', '', null, null, null]);
    });
  });
}

Future<void> _testLoadsDaylog(
  LocalId userId,
  LogDay logDay,
  bool isGhostEntriesEnabled,
  String expectedQuery,
  List<dynamic> expectedQueryArgs,
) async {
  final translator = _mockEntryDbTranslator();
  final table = EntryDbTable(translator);
  final executor = mockDatabaseExecutor();
  final context = _mockDbContext();
  when(() => context.queryScope).thenReturn(QueryScope(
    orgId: const LocalId(1),
    userId: userId,
  ));
  when(() => context.fields).thenReturn(table.fieldsBuilder.build());
  when(() => context.cursor).thenReturn(const LimitOffsetCursor(10, 0));
  when(() => context.hasAnyOfFields(any())).thenReturn(false);
  when(() => context.executor).thenAnswer((_) => Future.value(executor));

  await table.findDaylog(context, logDay, isGhostEntriesEnabled);

  verifyRawQuery(executor, expectedQuery, expectedQueryArgs);
}

MockDbContext _mockDbContext({
  DatabaseExecutor? executor,
  QueryScope? queryScope,
}) {
  final mock = MockDbContext();
  when(() => mock.executor).thenAnswer((_) => Future.value(executor));
  when(() => mock.queryScope).thenAnswer((_) => queryScope);
  return mock;
}

DbTranslator<Entry> _mockEntryDbTranslator() {
  final mock = MockEntryDbTranslator();
  when(() => mock.fromDb(any(), any()))
      .thenAnswer((_) => Future.value(mockEntry()));
  when(() => mock.nestedModelFields).thenReturn(<Field>{});

  return mock;
}
