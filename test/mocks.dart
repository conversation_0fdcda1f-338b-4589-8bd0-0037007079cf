import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';

class MockPathProvider extends PathProviderPlatform {
  static const kStoragePath = 'documents/';
  static const kStagingPath = 'staging/';
  static const kLibraryPath = 'library/';
  static const kSupportPath = 'support/';

  @override
  Future<String?> getApplicationDocumentsPath() {
    return Future.value(kStoragePath);
  }

  @override
  Future<String> getApplicationSupportPath() {
    return Future.value(kSupportPath);
  }

  @override
  Future<String?> getExternalStoragePath() {
    return Future.value(kStoragePath);
  }

  @override
  Future<String?> getTemporaryPath() {
    return Future.value(kStagingPath);
  }

  @override
  Future<String?> getLibraryPath() {
    return Future.value(kLibraryPath);
  }
}

class MockNavigatorObserver extends Mock implements NavigatorObserver {}

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockBuildContext extends Mock implements BuildContext {}

class MockState<T extends StatefulWidget> extends Mock implements State<T> {
  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.debug}) {
    return super.toString();
  }
}

class MockResponse<T> extends Mock implements Response<T> {}

class MockFilePicker extends Mock implements FilePicker {}

class MockImagePicker extends Mock implements ImagePicker {}

class MockValueNotifier<T> extends Mock implements ValueNotifier<T> {}

class MockNavigatorState extends Mock implements NavigatorState {
  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.debug}) {
    return 'NavigatorStateMock';
  }
}
