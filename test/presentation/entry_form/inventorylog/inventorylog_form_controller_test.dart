import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_form_controller.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocktail_fallback_values.dart';
import '../entry_form_controller_robot.dart';

void main() {
  group('$InventorylogFormController tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Read inventorylogEntry', (tester) async {
      final props = EntryFormProps(
          inputEntry: mockEntry(
              withId: true,
              extension: mockInventoryLog(type: InventorylogType.movement)));
      final robot = EntryFormControllerRobot(
        tester,
        props: props,
        controllerType: InventorylogFormController,
      );

      await robot.pumpWidget();

      await robot.verifyRead(props.inputEntry!);
    });
  });
}
