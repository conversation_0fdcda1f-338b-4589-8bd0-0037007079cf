import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_reason_suggestion_repository_query.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../../domain/inventorylog/mocks.dart';
import '../../../mocktail_fallback_values.dart';
import '../../../query_test_util.dart';

void main() {
  group('$InventorylogReasonSuggestionRepositoryQuery test', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Query', () async {
      final reasons = List.generate(2, (i) => '$i');
      final inventorylogRepository =
          mockInventorylogRepository(reasons: reasons);

      await testRepositoryQuery(
        const InventorylogReasonSuggestionRepositoryQuery(),
        mockRepositoryForQuery(inventorylogRepository: inventorylogRepository),
        () => verify(() => inventorylogRepository.reasons(captureAny())),
        reasons,
      );
    });
  });
}
