import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_builder.dart';
import 'package:flutter/foundation.dart';
import 'package:mocktail/mocktail.dart';

class MockEntryFormController extends Mock implements EntryFormController {}

class MockEntryFormControllerBuilder extends Mock
    implements EntryFormControllerBuilder {}

EntryFormController mockEntryFormController() {
  final mock = MockEntryFormController();
  when(() => mock.hasChanges).thenReturn(ValueNotifier(false));
  return mock;
}
