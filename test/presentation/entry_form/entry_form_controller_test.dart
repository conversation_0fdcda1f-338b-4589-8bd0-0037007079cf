import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../application/cache/project/mocks.dart';
import '../../mocktail_fallback_values.dart';
import 'entry_form_controller_robot.dart';

void main() {
  group('$EntryFormController tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Saves entry', (tester) async {
      final robot = EntryFormControllerRobot(tester);
      await robot.pumpWidget();

      await robot.save();

      robot.verifySave();
    });

    testWidgets('AutoSelects project', (tester) async {
      final robot = EntryFormControllerRobot(
        tester,
        activeProject: mockActiveProject(project: mockProject()),
      );
      await robot.pumpWidget();

      await robot.save();

      robot.verifySave();
    });

    testWidgets('Read inputEntry', (tester) async {
      final props = EntryFormProps(inputEntry: mockEntry(withId: true));
      final robot = EntryFormControllerRobot(tester, props: props);

      await robot.pumpWidget();

      await robot.verifyRead(props.inputEntry!);
    });

    testWidgets('LiveEntry updated', (tester) async {
      final props = EntryFormProps(inputEntry: mockEntry(withId: true));
      final updatedEntry = mockEntry(withId: true, id: props.inputEntry!.id);

      final robot = EntryFormControllerRobot(tester, props: props);
      await robot.pumpWidget();
      await robot.verifyRead(props.inputEntry!);

      robot.updateLiveEntry(updatedEntry);

      await robot.verifyRead(updatedEntry);
    });

    testWidgets('LiveEntry updated with current edition', (tester) async {
      final props = EntryFormProps(inputEntry: mockEntry(withId: true));
      final updatedEntry = mockEntry(withId: true, id: props.inputEntry!.id);
      final robot = EntryFormControllerRobot(tester, props: props);
      await robot.pumpWidget();
      await robot.verifyRead(props.inputEntry!);

      const logTime = LogTime(1200);
      robot.setEditingLogTime(logTime);
      robot.updateLiveEntry(updatedEntry);

      await robot.verifyRead(mockEntry(
        withId: true,
        id: props.inputEntry!.id,
        comments: updatedEntry.comments,
        time: logTime,
      ));
    });
  });
}
