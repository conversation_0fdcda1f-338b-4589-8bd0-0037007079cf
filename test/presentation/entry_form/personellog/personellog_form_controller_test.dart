import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/personnellog/personnellog_form_controller.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocktail_fallback_values.dart';
import '../entry_form_controller_robot.dart';

void main() {
  group('$PersonnellogFormController tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Read personnellogEntry', (tester) async {
      final props = EntryFormProps(
          inputEntry: mockEntry(withId: true, extension: mockPersonnellog()));
      final robot = EntryFormControllerRobot(
        tester,
        props: props,
        controllerType: PersonnellogFormController,
      );

      await robot.pumpWidget();

      await robot.verifyRead(props.inputEntry!);
    });
  });
}
