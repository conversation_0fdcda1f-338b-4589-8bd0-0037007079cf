import 'package:bitacora/presentation/entry_form/options/user/assignee_suggestion_repository_query.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../../../domain/entry/mocks.dart';
import '../../../../mocktail_fallback_values.dart';
import '../../../../query_test_util.dart';

void main() {
  group('$AssigneeSuggestionRepositoryQuery test', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Query', () async {
      final assignees = List.generate(2, (i) => '$i');
      final entryRepository = mockEntryRepository(assignees: assignees);
      final db = mockRepositoryForQuery(entryRepository: entryRepository);

      await testRepositoryQuery(
        const AssigneeSuggestionRepositoryQuery(),
        db,
        () => verify(() => entryRepository.assignees(captureAny())),
        assignees,
      );
    });
  });
}
