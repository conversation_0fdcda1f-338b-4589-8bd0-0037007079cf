import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag_option_actions.dart';
import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag_option_controller.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../../../base/base_robot.dart';
import '../../../util/mocks.dart';
import '../mocks.dart';

VoidCallback _onAddFromTextFields = () {};

class EntryFormTagOptionActionsTestRobot extends BaseRobot {
  final CollectionSelection<Tag> collectionSelection;
  final EntryFormTagOptionController controller;
  final VoidCallback onAddFromTextFields;

  EntryFormTagOptionActionsTestRobot(
    super.tester, {
    Repository? db,
    ActiveOrganization? activeOrganization,
    CollectionSelection<Tag>? collectionSelection,
    EntryFormTagOptionController? controller,
    VoidCallback? onAddFromTextFields,
  })  : collectionSelection =
            collectionSelection ?? mockCollectionSelection<Tag>(),
        controller = controller ?? mockEntryFormTagOptionController(),
        onAddFromTextFields = onAddFromTextFields ?? _onAddFromTextFields;

  @override
  Future<void> pumpWidget() {
    return pumpTestApp(
      providers: [
        ChangeNotifierProvider<CollectionSelection<Tag>>.value(
            value: collectionSelection),
      ],
      child: EntryFormTagOptionActions(
        controller: controller,
        onAddTagFromTextField: onAddFromTextFields,
      ),
    );
  }

  void verifyUi() {
    if (collectionSelection.isSelecting) {
      expect(find.byIcon(Icons.delete_outline), findsOneWidget);
      expect(find.byIcon(Icons.clear), findsOneWidget);
      expect(find.byIcon(Icons.add), findsNothing);
    } else {
      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.byIcon(Icons.delete_outline), findsNothing);
      expect(find.byIcon(Icons.clear), findsNothing);
    }
  }

  Future<void> tapDeleteSelection() async {
    await tap(find.byIcon(Icons.delete_outline));
    await tester.pump();
  }

  void verifyDeleteSelection() {
    verify(() => controller.deleteSelection()).called(1);
  }

  Future<void> tapClearSelection() async {
    await tap(find.byIcon(Icons.clear));
    await tester.pump();
  }

  void verifyClearSelection() {
    verify(() => controller.clearSelection()).called(1);
  }

  Future<void> tapAddTag() async {
    await tap(find.byIcon(Icons.add));
    await tester.pump();
  }
}
