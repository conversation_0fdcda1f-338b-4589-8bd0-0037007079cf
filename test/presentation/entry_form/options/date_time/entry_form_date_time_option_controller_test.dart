import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/non_editable_entry_form_open_state_option.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '_robots/entry_form_date_time_option_test_robot.dart';

void main() {
  group('$EntryFormDateTimeOptionController tests', () {
    testWidgets('Read', (tester) async {
      final entry = mockEntry();
      final liveEntry = ValueNotifier<Entry?>(entry);
      final widgetRobot = EntryFormDateTimeOptionTestRobot(tester);
      await widgetRobot.pumpWidget();

      final controller =
          EntryFormDateTimeOptionController(const EntryFormProps(), liveEntry);

      expect(
        controller.startDate.text,
        kLogDayDisplayDateFormat
            .format(getDateTimeFromLogDay(LogDay(entry.startDate!.value!))),
      );
      expect(
        controller.endDate.text,
        kLogDayDisplayDateFormat
            .format(getDateTimeFromLogDay(LogDay(entry.endDate!.value!))),
      );
      expect(
        controller.startTime.text,
        entry.startTime!.displayValue,
      );
      expect(
        controller.endTime.text,
        entry.endTime!.displayValue,
      );
    });

    testWidgets('Icon & Color', (tester) async {
      final liveEntry = ValueNotifier<Entry?>(null);
      final widgetRobot = EntryFormDateTimeOptionTestRobot(tester);
      await widgetRobot.pumpWidget();

      final controller =
          EntryFormDateTimeOptionController(const EntryFormProps(), liveEntry);

      expect(controller.icon, Icons.schedule);
      expect(controller.color, bitacoraPurple);
    });

    testWidgets('Create Entry form', (tester) async {
      final liveEntry = ValueNotifier<Entry?>(null);
      final widgetRobot = EntryFormDateTimeOptionTestRobot(tester);
      await widgetRobot.pumpWidget();
      final controller =
          EntryFormDateTimeOptionController(const EntryFormProps(), liveEntry);

      expect(
        controller.form(widgetRobot.context) is EntryFormDateTimeOption,
        true,
      );
    });

    testWidgets('Only DateTime Range form', (tester) async {
      final entry = mockEntry();
      final liveEntry = ValueNotifier<Entry?>(entry);
      final widgetRobot = EntryFormDateTimeOptionTestRobot(tester);
      await widgetRobot.pumpWidget();
      final controller =
          EntryFormDateTimeOptionController(const EntryFormProps(), liveEntry);

      final form =
          controller.form(widgetRobot.context) as EntryFormDateTimeOption;

      expect(form.isOpenStateAllowed, false);
    });

    testWidgets('NonEditableEntryOpenStateOption form', (tester) async {
      final entry = mockEntry(withOpenState: true);
      final liveEntry = ValueNotifier<Entry?>(entry);
      final widgetRobot = EntryFormDateTimeOptionTestRobot(tester);
      await widgetRobot.pumpWidget();
      final controller =
          EntryFormDateTimeOptionController(const EntryFormProps(), liveEntry);

      expect(
        controller.form(widgetRobot.context) is NonEditableEntryOpenStateOption,
        true,
      );
    });

    testWidgets('Build Open State for Save', (tester) async {
      const openStart = LogDay(19962901);
      final widgetRobot = EntryFormDateTimeOptionTestRobot(tester);
      await widgetRobot.pumpWidget();
      final controller = EntryFormDateTimeOptionController(
          const EntryFormProps(), ValueNotifier<Entry?>(null));

      controller.openStart.text =
          kLogDayDisplayDateFormat.format(getDateTimeFromLogDay(openStart));
      controller.isScheduleEntry.value = true;

      expect(
        controller.buildOpenStateForSave()!.startDay!.value,
        getLogDayFromDateTime(
            kLogDayDisplayDateFormat.parse(controller.openStart.text)),
      );
    });

    testWidgets('Build Open State Null', (tester) async {
      const openStart = LogDay(19962901);
      final widgetRobot = EntryFormDateTimeOptionTestRobot(tester);
      await widgetRobot.pumpWidget();
      final controller = EntryFormDateTimeOptionController(
          const EntryFormProps(), ValueNotifier<Entry?>(mockEntry()));

      controller.openStart.text =
          kLogDayDisplayDateFormat.format(getDateTimeFromLogDay(openStart));
      controller.isScheduleEntry.value = true;

      expect(controller.buildOpenStateForSave(), null);
    });
  });
}
