import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option_controller.dart';
import 'package:datetime_picker_formfield_new/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../../../../application/cache/logday/mocks.dart';
import '../../../../../base/base_robot.dart';
import 'mocks.dart';

class EntryFormDateTimeOptionTestRobot extends BaseRobot {
  final EntryFormDateTimeOptionController controller;

  EntryFormDateTimeOptionTestRobot(
    super.tester, {
    EntryFormDateTimeOptionController? controller,
  })  : controller = controller ?? mockEntryFormDateTimeOptionController();

  @override
  Future<void> pumpWidget() async {
    await pumpTestApp(
      child: EntryFormDateTimeOption(controller: controller),
      providers: [
        ChangeNotifierProvider<ActiveLogDay>(
          create: (_) => mockActiveLogDay(logDay: const LogDay(12122023)),
        ),
      ],
    );
  }

  void verifyDateTimeRangeUi() {
    expect(find.text('Schedule Entry'), findsOneWidget);
    expect(find.byType(Switch), findsOneWidget);
    expect(find.byType(DateTimeField), findsNWidgets(4));
  }

  Future<void> switchToSchedule() async {
    await tap(find.byType(Switch));
    await tester.pumpAndSettle();
  }

  void verifyOpenStateUi([String type = 'Complete']) {
    expect(find.text('Schedule Entry'), findsOneWidget);
    expect(find.byType(Switch), findsOneWidget);
    expect(find.byType(DateTimeField), findsNWidgets(2));
    expect(find.text(type), findsOneWidget);
  }

  Future<void> switchOpenStateType({
    String from = 'Complete',
    String to = 'Progressive',
  }) async {
    await tap(find.text(from));
    await tester.pumpAndSettle();
    await tap(find.text(to).last, warnIfMissed: true);
    await tester.pumpAndSettle();
  }
}
