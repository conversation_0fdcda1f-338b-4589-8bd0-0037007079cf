import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/entry_form/options/location/entry_form_location_option_controller.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/app_settings/app_settings_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:latlong2/latlong.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test/test.dart';

import '../../../../test_util.dart';
import '../../../../util/app_settings/mocks.dart';
import '../../../../util/mocks.dart';

void main() {
  group('$EntryFormLocationOptionController tests', () {
    test('When liveEntry is null and Entries location disabled', () async {
      final liveEntry = ValueNotifier<Entry?>(null);
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: false,
      });

      final entryFormOptionController =
          EntryFormLocationOptionController(liveEntry);

      final sharedPreferences = await SharedPreferences.getInstance();
      expect(entryFormOptionController.location.value, null);
      expect(entryFormOptionController.isShowing.value, false);
      expect(entryFormOptionController.isEnabled.value, false);
      expect(entryFormOptionController.isLoading.value, false);
      expect(entryFormOptionController.hasData.value, false);
      expect(entryFormOptionController.isLocationPermissionDenied.value, false);
      expect(
        sharedPreferences.getBool(SharedPreferencesKeys.entriesLocationEnabled),
        false,
      );
      expect(entryFormOptionController.mounted, true);
    });

    test('When liveEntry is null and Entries location enabled', () async {
      final appSettingsUtils = mockAppSettingsUtils();
      const location = LatLng(90, 50);
      final locationUtils =
          mockLocationUtils(position: mockPosition(latLng: location));
      final liveEntry = ValueNotifier<Entry?>(null);

      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: true,
      });

      await withInjected2<LocationUtils, AppSettingsUtils>(
        locationUtils,
        appSettingsUtils,
        () async {
          final entryFormOptionController = EntryFormLocationOptionController(
            liveEntry,
          );
          await awaitUntil(() => entryFormOptionController.isLoading.value);
          await awaitUntil(() => !entryFormOptionController.isLoading.value);

          final sharedPreferences = await SharedPreferences.getInstance();
          expect(entryFormOptionController.location.value, location);
          expect(entryFormOptionController.isShowing.value, false);
          expect(entryFormOptionController.isEnabled.value, true);
          expect(entryFormOptionController.isLoading.value, false);
          expect(entryFormOptionController.hasData.value, true);
          expect(
            entryFormOptionController.isLocationPermissionDenied.value,
            false,
          );
          expect(
            sharedPreferences
                .getBool(SharedPreferencesKeys.entriesLocationEnabled),
            true,
          );
          expect(entryFormOptionController.mounted, true);
        },
      );
    });

    test('Cancel when liveEntry is null and Entries location enabled',
        () async {
      final appSettingsUtils = mockAppSettingsUtils();
      final locationUtils = mockLocationUtils(position: mockPosition());
      final liveEntry = ValueNotifier<Entry?>(null);

      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: true,
      });

      await withInjected2<LocationUtils, AppSettingsUtils>(
        locationUtils,
        appSettingsUtils,
        () async {
          final entryFormOptionController = EntryFormLocationOptionController(
            liveEntry,
          );
          await awaitUntil(() => entryFormOptionController.isLoading.value);
          await entryFormOptionController.cancelDetermineLocation();
          await awaitUntil(() => !entryFormOptionController.isLoading.value);

          final sharedPreferences = await SharedPreferences.getInstance();
          expect(entryFormOptionController.location.value, null);
          expect(entryFormOptionController.isShowing.value, false);
          expect(entryFormOptionController.isEnabled.value, false);
          expect(entryFormOptionController.isLoading.value, false);
          expect(entryFormOptionController.hasData.value, false);
          expect(
            entryFormOptionController.isLocationPermissionDenied.value,
            false,
          );
          expect(
            sharedPreferences
                .getBool(SharedPreferencesKeys.entriesLocationEnabled),
            false,
          );
          expect(entryFormOptionController.mounted, true);
        },
      );
    });

    test('When liveEntry is not null and Entries location disabled', () async {
      const location = LatLng(90, 50);
      final liveEntry = ValueNotifier<Entry?>(mockEntry(
        location: const LatLngValueObject(location),
      ));
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: false,
      });

      final entryFormOptionController = EntryFormLocationOptionController(
        liveEntry,
      );

      final sharedPreferences = await SharedPreferences.getInstance();
      expect(entryFormOptionController.location.value, location);
      expect(entryFormOptionController.isShowing.value, false);
      expect(entryFormOptionController.isEnabled.value, true);
      expect(entryFormOptionController.isLoading.value, false);
      expect(entryFormOptionController.hasData.value, true);
      expect(entryFormOptionController.isLocationPermissionDenied.value, false);
      expect(
        sharedPreferences.getBool(SharedPreferencesKeys.entriesLocationEnabled),
        false,
      );
      expect(entryFormOptionController.mounted, true);
    });

    test('When liveEntry is not null and Entries location enabled', () async {
      const location = LatLng(90, 50);
      final liveEntry = ValueNotifier<Entry?>(mockEntry(
        location: const LatLngValueObject(location),
      ));
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: true,
      });

      final entryFormOptionController = EntryFormLocationOptionController(
        liveEntry,
      );

      final sharedPreferences = await SharedPreferences.getInstance();
      expect(entryFormOptionController.location.value, location);
      expect(entryFormOptionController.isShowing.value, false);
      expect(entryFormOptionController.isEnabled.value, true);
      expect(entryFormOptionController.isLoading.value, false);
      expect(entryFormOptionController.hasData.value, true);
      expect(entryFormOptionController.isLocationPermissionDenied.value, false);
      expect(
        sharedPreferences.getBool(SharedPreferencesKeys.entriesLocationEnabled),
        true,
      );
      expect(entryFormOptionController.mounted, true);
    });

    test('When EntryFormOptionController is disposed', () async {
      final liveEntry = ValueNotifier<Entry?>(null);
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: true,
      });
      final locationUtils = mockLocationUtils();
      await withInjected(locationUtils, () async {
        final entryFormOptionController =
            EntryFormLocationOptionController(liveEntry);
        await awaitUntil(() => entryFormOptionController.isLoading.value);

        entryFormOptionController.dispose();

        expect(entryFormOptionController.mounted, false);
      });
    });
  });
}
