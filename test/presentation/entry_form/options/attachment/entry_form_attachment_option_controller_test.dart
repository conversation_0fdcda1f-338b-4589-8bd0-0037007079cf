import 'dart:async';

import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_context_snapshot.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/entry_form_attachment_option_controller.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:file/file.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:provider/provider.dart';

import '../../../../application/cache/logday/mocks.dart';
import '../../../../application/cache/organization/mocks.dart';
import '../../../../application/cache/project/mocks.dart';
import '../../../../application/cache/template/mocks.dart';
import '../../../../domain/attachment/mocks.dart';
import '../../../../domain/auth/mocks.dart';
import '../../../../domain/common/mocks.dart';
import '../../../../infrastructure/mocks.dart';
import '../../../../mocks.dart';
import '../../../../mocktail_fallback_values.dart';
import '../../../../test_util.dart';
import '../../../../util/attachment/mocks.dart';
import '../../../../util/file_system/mocks.dart';

class MockIosUtsName extends Mock implements IosUtsname {}

void main() {
  BuildContext? context;

  setUpAll(() {
    PathProviderPlatform.instance = MockPathProvider();
    MocktailFallbackValues.ensureInitialized();
  });

  Repository mockDbRepository() {
    final attachments = MockAttachmentRepository();
    when(() => attachments.getMutations()).thenAnswer(
        (_) => StreamController<Mutation<Attachment>>.broadcast().stream);

    final db = MockRepository();
    when(() => db.attachment).thenReturn(attachments);
    return db;
  }

  Widget buildContext() {
    return MultiProvider(
      providers: [
        Provider(create: (c) => mockDbRepository()),
        Provider<ApiTranslator>(create: (c) => MockApiTranslator()),
        ChangeNotifierProvider(create: (c) => mockAuthRepository()),
        ChangeNotifierProvider(create: (c) => mockActiveLogDay()),
        ChangeNotifierProvider(create: (c) => mockActiveOrganization()),
        ChangeNotifierProvider(create: (c) => mockActiveProject()),
        ChangeNotifierProvider(create: (c) => mockTemplateCache()),
      ],
      child: MaterialApp(
        home: Localizations(
          locale: const Locale('en'),
          delegates: AppLocalizations.localizationsDelegates,
          child: Scaffold(
            body: Builder(builder: (c) {
              context = c;
              return Container();
            }),
          ),
        ),
      ),
    );
  }

  group('$EntryFormAttachmentOptionController tests', () {
    testWidgets('Picks from Photo Gallery', (tester) async {
      await tester.runAsync(() async {
        final fileSystem = await mockFileSystem();
        final image = await createMockImage(
          fileSystem,
          path.join(MockPathProvider.kStoragePath, 'circle.png'),
        );
        final attachmentUtils = mockAttachmentUtils();
        final imagePicker = _mockImagePicker(
          fileSystem: fileSystem,
          photoResults: [image],
        );
        await tester.pumpWidget(buildContext());

        await withInjected2<FileSystem, AttachmentUtils>(
          fileSystem,
          attachmentUtils,
          () async {
            final controller = EntryFormAttachmentOptionController(
              EntryFormControllerContextSnapshot(context!),
              ValueNotifier<Entry?>(null),
            );

            await controller.pickPhotoGallery(imagePicker);

            verify(() => imagePicker.pickMultiImage());
            verifyNever(
                () => imagePicker.pickVideo(source: ImageSource.gallery));
            final fileForProcess = verify(() =>
                    attachmentUtils.processForStaging(captureAny(), any()))
                .captured
                .first as File;
            expect(
              fileForProcess.path,
              path.join(MockPathProvider.kStoragePath, 'circle.png'),
            );
            expect(controller.isProcessingPicks.value, false);
          },
        );
      });
    });

    testWidgets('Picks from Photo Gallery on iOS versions less than 14',
        (tester) async {
      await tester.runAsync(() async {
        final deviceInfo = MockDeviceInfoPlugin();
        when(() => deviceInfo.iosInfo).thenAnswer(
          (invocation) => Future.value(
            IosDeviceInfo.fromMap({
              'isPhysicalDevice': true,
              'name': 'iPhone',
              'systemName': 'iOS 16',
              'model': 'SE',
              'modelName': 'SE',
              'freeDiskSize': 1024,
              'totalDiskSize': 1024 * 5,
              'physicalRamSize': 1024 * 8,
              'availableRamSize': 1024 * 8,
              'isiOSAppOnMac': false,
              'localizedModel': 'SE',
              'identifierForVendor': 'XD323X3123D',
              'systemVersion': '13.3.1',
              'utsname': {
                'sysname': 'iOS 16',
                'nodename': 'AB',
                'release': '16.05.4',
                'version': 'v16',
                'machine': 'iPhone',
              },
            }),
          ),
        );
        final fileSystem = await mockFileSystem();
        final image = await createMockImage(
          fileSystem,
          path.join(MockPathProvider.kStoragePath, 'circle.png'),
        );
        final attachmentStorageUtils = mockAttachmentUtils();
        final imagePicker = _mockImagePicker(
          fileSystem: fileSystem,
          photoResults: [image],
        );
        await tester.pumpWidget(buildContext());

        await withIOS(
          () => withInjected3<DeviceInfoPlugin, FileSystem, AttachmentUtils>(
            deviceInfo,
            fileSystem,
            attachmentStorageUtils,
            () async {
              final controller = EntryFormAttachmentOptionController(
                EntryFormControllerContextSnapshot(context!),
                ValueNotifier<Entry?>(null),
              );

              await controller.pickPhotoGallery(imagePicker);

              verify(
                () => imagePicker.pickImage(source: ImageSource.gallery),
              );
              verifyNever(() => imagePicker.pickMultiImage());
              verifyNever(
                () => imagePicker.pickVideo(source: ImageSource.gallery),
              );
              final fileForProcess = verify(() => attachmentStorageUtils
                      .processForStaging(captureAny(), any())).captured.first
                  as File;
              expect(
                fileForProcess.path,
                path.join(MockPathProvider.kStoragePath, 'circle.png'),
              );
              expect(controller.isProcessingPicks.value, false);
            },
          ),
        );
      });
    });

    testWidgets('Picks from Video Gallery', (tester) async {
      await tester.runAsync(() async {
        final fileSystem = await mockFileSystem();
        final video = await fileSystem
            .file(path.join(MockPathProvider.kStoragePath, 'viral.mp4'))
            .create();
        final attachmentStorageUtils = mockAttachmentUtils();
        final imagePicker = _mockImagePicker(
          fileSystem: fileSystem,
          videoResult: video,
          isVideo: true,
        );
        await tester.pumpWidget(buildContext());

        await withInjected2<FileSystem, AttachmentUtils>(
          fileSystem,
          attachmentStorageUtils,
          () async {
            final controller = EntryFormAttachmentOptionController(
              EntryFormControllerContextSnapshot(context!),
              ValueNotifier<Entry?>(null),
            );

            await controller.pickVideoGallery(imagePicker);

            verify(() => imagePicker.pickVideo(source: ImageSource.gallery));
            verifyNever(() => imagePicker.pickMultiImage());
            final fileForProcess = verify(() => attachmentStorageUtils
                .processForStaging(captureAny(), any())).captured.first as File;
            expect(
              fileForProcess.path,
              path.join(MockPathProvider.kStoragePath, 'viral.mp4'),
            );
            expect(controller.isProcessingPicks.value, false);
          },
        );
      });
    });

    testWidgets('Picks Files', (tester) async {
      await tester.runAsync(() async {
        final fileSystem = await mockFileSystem();
        final filePicker = await _mockFilePicker(fileSystem: fileSystem);
        await tester.pumpWidget(buildContext());

        await withInjected<FileSystem>(
          fileSystem,
          () async {
            final controller = EntryFormAttachmentOptionController(
              EntryFormControllerContextSnapshot(context!),
              ValueNotifier<Entry?>(null),
            );

            await controller.pickFiles(filePicker);

            verify(
              () => filePicker.pickFiles(
                type: FileType.any,
                allowMultiple: true,
              ),
            );
            expect(controller.isProcessingPicks.value, false);
          },
        );
      });
    });

    testWidgets(
      'Is processing picks true',
      (tester) async {
        await tester.runAsync(() async {
          final fileSystem = await mockFileSystem();
          final image = await createMockImage(fileSystem,
              path.join(MockPathProvider.kStoragePath, 'circle.png'));
          final imagePicker = _mockImagePicker(
            fileSystem: fileSystem,
            photoResults: [image],
          );
          await tester.pumpWidget(buildContext());

          await withInjected2<FileSystem, AttachmentUtils>(
            fileSystem,
            mockAttachmentUtils(),
            () async {
              final controller = EntryFormAttachmentOptionController(
                EntryFormControllerContextSnapshot(context!),
                ValueNotifier<Entry?>(null),
              );

              unawaited(controller.pickPhotoGallery(imagePicker));
              await Future.delayed(const Duration(milliseconds: 100));

              await awaitUntil(() {
                return controller.isProcessingPicks.value == true;
              });
            },
          );
        });
      },
    );

    testWidgets(
      'Is processing picks false ignore when is disposed',
      (tester) async {
        await tester.runAsync(() async {
          final fileSystem = await mockFileSystem();
          final image = await createMockImage(fileSystem, 'circle.png');
          final imagePicker = _mockImagePicker(
            fileSystem: fileSystem,
            photoResults: [image],
          );
          await tester.pumpWidget(buildContext());

          await withInjected2<FileSystem, AttachmentUtils>(
            fileSystem,
            mockAttachmentUtils(),
            () async {
              final controller = EntryFormAttachmentOptionController(
                EntryFormControllerContextSnapshot(context!),
                ValueNotifier<Entry?>(null),
              );

              unawaited(controller.pickPhotoGallery(imagePicker));
              await Future.delayed(const Duration(milliseconds: 100));

              expect(controller.isProcessingPicks.value, true);
            },
          );
        });
      },
    );
  });
}

ImagePicker _mockImagePicker({
  List<File>? photoResults,
  File? videoResult,
  bool isVideo = false,
  required FileSystem fileSystem,
}) {
  final imagePicker = MockImagePicker();

  if (isVideo) {
    when(() => imagePicker.pickVideo(source: ImageSource.gallery))
        .thenAnswer((_) async {
      videoResult ??= await fileSystem.file('viral.mp4').create();

      videoResult = videoResult!.renameSync(path.join(
          videoResult!.dirname, 'image_picker_${videoResult!.basename}'));

      return Future.value(XFile(videoResult!.path));
    });
  } else {
    when(() => imagePicker.pickMultiImage()).thenAnswer(
      (_) async {
        photoResults ??= [
          await createMockImage(fileSystem, 'image_pickerimg1.png'),
          await createMockImage(fileSystem, 'image_pickerimg2.png')
        ];

        return Future.value(
          photoResults!.map((e) {
            e = e.renameSync(path.join(e.dirname, 'image_picker${e.basename}'));
            return XFile(e.path);
          }).toList(growable: false),
        );
      },
    );

    when(() => imagePicker.pickImage(source: ImageSource.gallery)).thenAnswer(
      (_) async {
        photoResults ??= [
          await createMockImage(fileSystem, 'image_picker_img1.png')
        ];

        final photo = photoResults!.first.renameSync(path.join(
          photoResults!.first.dirname,
          'image_picker_${photoResults!.first.basename}',
        ));
        return Future.value(XFile(photo.path));
      },
    );
  }

  return imagePicker;
}

Future<FilePicker> _mockFilePicker(
    {required FileSystem fileSystem, List<PlatformFile>? results}) async {
  final filePicker = MockFilePicker();

  final files = [
    await fileSystem.file('bitacora-todo.xlsx').create(),
    await fileSystem.file('jira export.csv').create(),
  ];

  when(() => filePicker.pickFiles(type: FileType.any, allowMultiple: true))
      .thenAnswer((_) => Future.value(FilePickerResult(results ??
          files.map((e) {
            return PlatformFile(name: e.basename, size: 100, path: e.path);
          }).toList(growable: false))));

  return filePicker;
}
