import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/audio/audio_player_widget.dart';
import 'package:bitacora/presentation/widgets/audio_timeline_player.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../../../mocktail_fallback_values.dart';
import '../../../../../test_util.dart';
import '../../../../daylog/audio/mocks.dart';

const String kDefaultAudioPath = 'record.m4a';

void main() {
  group('$AudioPlayerWidget tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('When no is a current player', (tester) async {
      await tester.pumpWidget(testAttachmentAudioPlayer());

      expect(
        find.byWidgetPredicate(
            (w) => w is AnimatedIcon && w.progress.value == 0),
        findsOneWidget,
      );
      expect(find.byType(AudioTimelinePlayer), findsNothing);
      expect(find.text('00:00'), findsNothing);
      expect(find.text(kDefaultAudioPath), findsOneWidget);
    });

    testWidgets('When is selected', (tester) async {
      final streamController = StreamController<Duration>();
      final audioPlayer = mockAudioPlayer(
        onAudioPositionChanged: streamController.stream,
        duration: const Duration(seconds: 4),
      );

      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(
            testAttachmentAudioPlayer(isSelected: ValueNotifier(true)));
        await tester.pumpAndSettle();
      });

      expect(find.byType(AnimatedIcon), findsOneWidget);
      expect(find.byType(AudioTimelinePlayer), findsNothing);
    });

    testWidgets('On play pressed', (tester) async {
      var playPressedCount = 0;
      onPlayPressed() {
        playPressedCount++;
      }

      final positionStreamController = StreamController<Duration>();
      final onDurationStreamController = StreamController<Duration>();
      final audioPlayer = mockAudioPlayer(
        onAudioPositionChanged: positionStreamController.stream,
        onDurationChanged: onDurationStreamController.stream,
      );

      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(
            testAttachmentAudioPlayer(onPlayPressed: onPlayPressed));
        await tester.pumpAndSettle();
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
        onDurationStreamController.sink.add(const Duration(milliseconds: 4000));
        positionStreamController.sink.add(const Duration(milliseconds: 2000));
        await tester.pumpAndSettle();
      });

      verify(() => audioPlayer.setSourceDeviceFile(kDefaultAudioPath));
      verify(() => audioPlayer.seek(Duration.zero));
      verify(() => audioPlayer.resume());
      expect(playPressedCount, 1);
    });

    testWidgets('On play with iOS', (tester) async {
      withIOS(() async {
        var playPressedCount = 0;
        onPlayPressed() {
          playPressedCount++;
        }

        final positionStreamController = StreamController<Duration>();
        final audioPlayer = mockAudioPlayer(
          onAudioPositionChanged: positionStreamController.stream,
          duration: const Duration(seconds: 4),
        );

        await withInjected<AudioPlayer>(audioPlayer, () async {
          await tester.pumpWidget(
              testAttachmentAudioPlayer(onPlayPressed: onPlayPressed));
          await tester.pumpAndSettle();
          await tester.tap(find.byType(AnimatedIcon));
          await tester.pumpAndSettle();
          positionStreamController.sink.add(const Duration(milliseconds: 2000));
          await tester.pumpAndSettle();
        });

        verify(() => audioPlayer.setSourceDeviceFile(kDefaultAudioPath));
        verify(() => audioPlayer.seek(Duration.zero));
        verify(() => audioPlayer.resume());
        expect(playPressedCount, 1);
      });
    });

    testWidgets('When playing is complete', (tester) async {
      final isSelected = ValueNotifier(false);
      var playCompleteCount = 0;
      onPlayCompleted() {
        playCompleteCount++;
        isSelected.value = false;
      }

      final positionStreamController = StreamController<Duration>();
      final stateStreamController = StreamController<PlayerState>();
      final onDurationStreamController = StreamController<Duration>();
      final audioPlayer = mockAudioPlayer(
        onAudioPositionChanged: positionStreamController.stream,
        onPlayerStateChanged: stateStreamController.stream,
        onDurationChanged: onDurationStreamController.stream,
        duration: const Duration(seconds: 4),
      );

      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(
            testAttachmentAudioPlayer(onPlayCompleted: onPlayCompleted));
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
        onDurationStreamController.sink.add(const Duration(milliseconds: 4000));
        positionStreamController.sink.add(const Duration(milliseconds: 2000));
        await tester.pumpAndSettle();
        stateStreamController.sink.add(PlayerState.completed);
        await tester.pumpAndSettle();
      });

      verify(() => audioPlayer.release()).called(1);
      expect(
        find.byWidgetPredicate(
          (w) => w is AnimatedIcon && w.progress.value == 0,
        ),
        findsOneWidget,
      );
      expect(playCompleteCount, 1);
      expect(
        find.byWidgetPredicate(
          (w) => w is AnimatedIcon && w.progress.value == 0,
        ),
        findsOneWidget,
      );
      expect(find.text(kDefaultAudioPath), findsOneWidget);
    });

    testWidgets('When another player becomes current', (tester) async {
      final isSelected = ValueNotifier(false);
      final positionStreamController = StreamController<Duration>();
      final audioPlayer = mockAudioPlayer(
        onAudioPositionChanged: positionStreamController.stream,
        duration: const Duration(seconds: 4),
      );
      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(testAttachmentAudioPlayer(
          isSelected: isSelected,
          onPlayPressed: () {
            isSelected.value = true;
          },
        ));
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
        positionStreamController.sink.add(const Duration(milliseconds: 2000));
        await tester.pumpAndSettle();

        isSelected.value = false;
        await tester.pumpAndSettle();
      });

      expect(
        find.byWidgetPredicate(
          (w) => w is AnimatedIcon && w.progress.value == 0,
        ),
        findsOneWidget,
      );
      expect(find.text(kDefaultAudioPath), findsOneWidget);
    });

    testWidgets('Playing pause', (tester) async {
      final isSelected = ValueNotifier(false);
      final positionStreamController = StreamController<Duration>();
      final onDurationStreamController = StreamController<Duration>();
      final stateStreamController = StreamController<PlayerState>.broadcast();
      var state = PlayerState.stopped;
      final audioPlayer = mockAudioPlayer(
        onAudioPositionChanged: positionStreamController.stream,
        onPlayerStateChanged: stateStreamController.stream,
        onDurationChanged: onDurationStreamController.stream,
        onResume: () async {
          state = PlayerState.playing;
          return 1;
        },
        onPause: () async {
          state = PlayerState.paused;
          return 1;
        },
      );
      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(testAttachmentAudioPlayer(
          isSelected: isSelected,
          onPlayPressed: () {
            isSelected.value = true;
          },
        ));
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
        onDurationStreamController.sink.add(const Duration(milliseconds: 4000));
        positionStreamController.sink.add(const Duration(milliseconds: 2000));
        await tester.pumpAndSettle();
        await awaitUntil(() => state == PlayerState.playing);

        when(() => audioPlayer.state).thenReturn(PlayerState.playing);
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
      });

      await awaitUntil(() => state == PlayerState.paused);
      verify(() => audioPlayer.pause()).called(1);
      expect(
        find.byWidgetPredicate(
          (w) => w is AnimatedIcon && w.progress.value == 0,
        ),
        findsOneWidget,
      );
    });

    testWidgets('Playing resume', (tester) async {
      final isSelected = ValueNotifier(false);
      final positionStreamController = StreamController<Duration>();
      final onDurationStreamController = StreamController<Duration>();
      final stateStreamController = StreamController<PlayerState>();
      var state = PlayerState.stopped;
      final audioPlayer = mockAudioPlayer(
        onAudioPositionChanged: positionStreamController.stream,
        onDurationChanged: onDurationStreamController.stream,
        onPlayerStateChanged: stateStreamController.stream,
        duration: const Duration(seconds: 4),
        onResume: () async {
          state = PlayerState.playing;
          return 1;
        },
        onPause: () async {
          state = PlayerState.paused;
          return 1;
        },
      );
      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(testAttachmentAudioPlayer(
            isSelected: isSelected,
            onPlayPressed: () {
              isSelected.value = true;
            }));
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
        onDurationStreamController.sink.add(const Duration(milliseconds: 4000));
        positionStreamController.sink.add(const Duration(milliseconds: 2000));
        await tester.pumpAndSettle();
        await awaitUntil(() => state == PlayerState.playing);
        when(() => audioPlayer.state).thenReturn(PlayerState.playing);
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
        await awaitUntil(() => state == PlayerState.paused);
        when(() => audioPlayer.state).thenReturn(PlayerState.paused);

        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();
      });

      await awaitUntil(() => state == PlayerState.playing);
      verify(() => audioPlayer.resume()).called(2);
      expect(
        find.byWidgetPredicate(
          (w) => w is AnimatedIcon && w.progress.value == 1.0,
        ),
        findsOneWidget,
      );
    });
  });
}

Widget testAttachmentAudioPlayer({
  ValueNotifier<bool>? isSelected,
  String? attachmentPath,
  VoidCallback? onPlayPressed,
  VoidCallback? onPlayCompleted,
}) {
  return MaterialApp(
    home: Scaffold(
      body: Center(
        child: ValueListenableBuilder<bool>(
            valueListenable: isSelected ?? ValueNotifier<bool>(false),
            builder: (context, value, _) {
              return Provider<bool>.value(
                value: value,
                child: AudioPlayerWidget(
                  attachmentPath: attachmentPath ?? kDefaultAudioPath,
                  onPlayPressed: onPlayPressed ?? () {},
                  onPlayCompleted: onPlayCompleted ?? () {},
                ),
              );
            }),
      ),
    ),
  );
}
