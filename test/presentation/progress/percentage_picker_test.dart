import 'dart:math';

import 'package:bitacora/presentation/progress/percentage_picker.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

void main() {
  Widget testApp(Widget child, Percentage percentage) {
    return MaterialApp(
      home: Localizations(
        locale: const Locale('en'),
        delegates: AppLocalizations.localizationsDelegates,
        child: ChangeNotifierProvider<Percentage>.value(
          value: percentage,
          builder: (context, _) => child,
        ),
      ),
    );
  }

  group('$PercentagePicker tests', () {
    testWidgets('PercentagePicker when it is not editable', (tester) async {
      const minValue = 1;

      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: false),
        Percentage(minValue),
      ));

      expect(find.text('$minValue%'), findsOneWidget);
      expect(find.byType(GestureDetector), findsNothing);
    });

    testWidgets('PercentagePicker when it is editable', (tester) async {
      const minValue = 2;

      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: true),
        Percentage(minValue),
      ));

      expect(find.text('$minValue%'), findsOneWidget);
      expect(find.byType(GestureDetector), findsNWidgets(2));
    });

    testWidgets('Tap to go up', (tester) async {
      const minValue = 30;
      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: true),
        Percentage(minValue),
      ));

      final buttonUp = find.byIcon(Icons.arrow_right);
      await tester.tap(buttonUp);
      await tester.pumpAndSettle();

      expect(find.text('31%'), findsOneWidget);
    });

    testWidgets('Tap to go up when the percentage value is 100',
        (tester) async {
      const minValue = 100;
      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: true),
        Percentage(minValue),
      ));

      final buttonUp = find.byIcon(Icons.arrow_right);
      await tester.tap(buttonUp);
      await tester.pumpAndSettle();

      expect(find.text('100%'), findsOneWidget);
    });

    // Tests long press down from 80% -> [47%,67%]
    testWidgets('Long press to go up', (tester) async {
      const errorRange = 10;
      const minValue = 30;
      const millisLongPressDuration = 800;
      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: true),
        Percentage(minValue),
      ));

      final buttonUp = find.byIcon(Icons.arrow_right);
      await TestAsyncUtils.guard<void>(() async {
        final gesture = await tester.startGesture(
          tester.getCenter(buttonUp, warnIfMissed: true, callee: 'longPress'),
          pointer: null,
          buttons: 0x01,
        );
        await tester.pumpAndSettle(
            const Duration(milliseconds: millisLongPressDuration));
        await gesture.up();
      });
      await tester.pumpAndSettle();

      final newPercentage = _calculatePercentageAfterFromLongPress(
        minValue,
        millisLongPressDuration,
        true,
      );
      expect(
        find.textContaining(
          RegExp(
              '["${newPercentage - errorRange}"-"${newPercentage + errorRange}"]%'),
        ),
        findsOneWidget,
      );
    });

    testWidgets('Tap to go down', (tester) async {
      const minValue = 30;
      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: true),
        Percentage(minValue),
      ));

      final buttonDown = find.byIcon(Icons.arrow_left);
      final buttonUp = find.byIcon(Icons.arrow_right);
      await tester.tap(buttonUp);
      await tester.tap(buttonUp);
      await tester.tap(buttonUp);
      await tester.tap(buttonUp);
      await tester.tap(buttonDown);
      await tester.pumpAndSettle();

      expect(find.text('33%'), findsOneWidget);
    });

    testWidgets('Tap to go down when the percentage value is minimum',
        (tester) async {
      const minValue = 30;
      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: true),
        Percentage(minValue),
      ));

      final buttonDown = find.byIcon(Icons.arrow_left);
      await tester.tap(buttonDown);
      await tester.pumpAndSettle();

      expect(find.text('30%'), findsOneWidget);
    });

    // Tests long press up to 30% to 63% and down to -> [39%,59%]
    testWidgets('Long press to go down', (tester) async {
      const errorRange = 10;
      const minValue = 30;
      const millisUpLongPressDuration = 900;
      const millisDownLongPressDuration = 600;
      await tester.pumpWidget(testApp(
        const PercentagePicker(isEditable: true),
        Percentage(minValue),
      ));
      final buttonUp = find.byIcon(Icons.arrow_right);
      final buttonDown = find.byIcon(Icons.arrow_left);

      await TestAsyncUtils.guard<void>(() async {
        final gesture = await tester.startGesture(
            tester.getCenter(buttonUp, warnIfMissed: true, callee: 'longPress'),
            pointer: null,
            buttons: 0x01);
        await tester.pumpAndSettle(
            const Duration(milliseconds: millisUpLongPressDuration));
        await gesture.up();
      });
      await tester.pumpAndSettle();
      final newUpPercentage = _calculatePercentageAfterFromLongPress(
        minValue,
        millisUpLongPressDuration,
        true,
      );

      await TestAsyncUtils.guard<void>(() async {
        final gesture = await tester.startGesture(
            tester.getCenter(
              buttonDown,
              warnIfMissed: true,
              callee: 'longPress',
            ),
            pointer: null,
            buttons: 0x01);
        await tester.pumpAndSettle(
            const Duration(milliseconds: millisDownLongPressDuration));
        await gesture.up();
      });
      await tester.pumpAndSettle();

      final newPercentage = _calculatePercentageAfterFromLongPress(
        newUpPercentage,
        millisDownLongPressDuration,
        false,
      );
      expect(
        find.textContaining(
          RegExp(
              '["${newPercentage - errorRange}"-"${newPercentage + errorRange}"]%'),
        ),
        findsOneWidget,
      );
    });
  });
}

int _calculatePercentageAfterFromLongPress(
    int minValue, int millisLongPressDuration, bool direction) {
  var millisForNextTick = 80;
  var longPressTime = 0;
  var value = minValue;
  while (longPressTime <= millisLongPressDuration) {
    millisForNextTick -= 5;
    millisForNextTick = max(millisForNextTick, 16);
    longPressTime += millisForNextTick;

    if (direction && value != 100) {
      value++;
    }

    if (!direction && value != 0) {
      value--;
    }
  }
  return value;
}
