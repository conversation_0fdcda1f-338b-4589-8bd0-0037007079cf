import 'package:bitacora/presentation/user_settings/other_settings.dart';
import 'package:flutter_test/flutter_test.dart';

import '_robots/other_settings_test_robot.dart';

void main() {
  group('$OtherSettings tests', () {
    testWidgets('Has Ui', (tester) async {
      final robot = OtherSettingsTestRobot(tester);

      await robot.pumpWidget();

      robot.verifyUi();
    });

    testWidgets('Tap System Permissions', (tester) async {
      final robot = OtherSettingsTestRobot(tester);
      await robot.pumpWidget();

      await robot.tapSystemPermissions();

      robot.verifyTapSystemPermissions();
    });
  });
}
