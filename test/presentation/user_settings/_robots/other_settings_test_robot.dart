import 'package:bitacora/presentation/user_settings/other_settings.dart';
import 'package:bitacora/util/app_settings/app_settings_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/base_robot.dart';
import '../../../util/app_settings/mocks.dart';

class OtherSettingsTestRobot extends BaseRobot {
  final _appSettings = mockAppSettingsUtils();

  OtherSettingsTestRobot(super.tester);

  @override
  Future<void> pumpWidget() async {
    await pumpTestApp(
      child: const Column(children: [OtherSettings()]),
    );
  }

  void verifyUi() {
    expect(find.text('Other'), findsOneWidget);
    expect(find.text('System permissions'), findsOneWidget);
    expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
  }

  Future<void> tapSystemPermissions() async {
    await withInjected<AppSettingsUtils>(
        _appSettings, () => tap(find.text('System permissions')));
    await tester.pumpAndSettle();
  }

  void verifyTapSystemPermissions() async {
    verify(() => _appSettings.openAppSettings()).called(1);
  }
}
