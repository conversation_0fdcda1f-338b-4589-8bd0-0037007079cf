import 'package:bitacora/presentation/widgets/section_title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../base/base_robot.dart';
import '../../../base/test_app.dart';

class SectionTitleTestRobot extends BaseRobot {
  SectionTitleTestRobot(super.tester);

  @override
  Future<void> pumpWidget({String? title}) async {
    await tester.pumpWidget(
      TestApp(
        child: SectionTitle(
          title: title ?? 'Title',
        ),
      ),
    );
  }

  void verifyUi(String title) {
    expect(find.text(title), findsOneWidget);
    expect(find.byType(Divider), findsOneWidget);
  }
}
