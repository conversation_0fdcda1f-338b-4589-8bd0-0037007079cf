import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/widgets/recorder_wave_indicator.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  Widget testRecorderWaveIndicator({AudioEntryListenerController? controller}) {
    return MaterialApp(
      home: Localizations(
        locale: const Locale('en'),
        delegates: AppLocalizations.localizationsDelegates,
        child: const Scaffold(
          body: RecorderWaveIndicator(),
        ),
      ),
    );
  }

  group('$RecorderWaveIndicator tests', () {
    testWidgets('Generate waves', (tester) async {
      await tester.pumpWidget(testRecorderWaveIndicator());

      expect(find.byType(Row), findsOneWidget);
      expect(find.byType(Container), findsNWidgets(kNumberOfAudioWaves));
    });
  });
}
