import 'package:bitacora/presentation/widgets/section_title.dart';
import 'package:flutter_test/flutter_test.dart';

import '_robots/section_title_test_robot.dart';

void main() {
  group('$SectionTitle tests', () {
    testWidgets('Has UI', (tester) async {
      const title = 'Section title.';
      final robot = SectionTitleTestRobot(tester);

      await robot.pumpWidget(title: title);

      robot.verifyUi(title);
    });
  });
}
