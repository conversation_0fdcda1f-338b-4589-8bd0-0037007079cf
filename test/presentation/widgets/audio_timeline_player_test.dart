import 'package:audioplayers/audioplayers.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/widgets/audio_timeline_player.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../mocktail_fallback_values.dart';
import '../daylog/audio/mocks.dart';

Widget testAudioTimelinePlayer({
  Duration? playerPosition,
  AudioEntryListenerController? controller,
  Color? defaultColor,
}) {
  return MaterialApp(
    home: Localizations(
      locale: const Locale('en'),
      delegates: AppLocalizations.localizationsDelegates,
      child: Scaffold(
        body: Center(
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              AudioTimelinePlayer(
                playerPosition: playerPosition ?? Duration.zero,
                totalDuration: controller?.duration ?? Duration.zero,
                defaultColor: defaultColor,
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  group('$AudioTimelinePlayer tests', () {
    testWidgets('Has ui', (tester) async {
      await tester.pumpWidget(testAudioTimelinePlayer());

      expect(find.byType(Expanded), findsOneWidget);
      expect(find.byType(SliderTheme), findsOneWidget);
      expect(find.byType(Slider), findsOneWidget);
    });

    testWidgets('With default color', (tester) async {
      await tester
          .pumpWidget(testAudioTimelinePlayer(defaultColor: bitacoraYellow));

      expect(
        find.byWidgetPredicate(
          (w) =>
              w is SliderTheme &&
              w.data.activeTrackColor == bitacoraYellow &&
              w.data.inactiveTrackColor == bitacoraYellow.withValues(alpha: 0.3) &&
              w.data.thumbColor == bitacoraYellow,
        ),
        findsOneWidget,
      );
    });

    testWidgets('The slider shows the playback percentage.', (tester) async {
      const totalDurationMs = 9000;
      const currentPositionMs = totalDurationMs ~/ 2;
      final audioPlayer = mockAudioPlayer();
      final controller = mockAudioEntryListenerController(
          duration: const Duration(milliseconds: totalDurationMs));

      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(
          testAudioTimelinePlayer(
            controller: controller,
            playerPosition: const Duration(milliseconds: currentPositionMs),
          ),
        );
      });

      expect(
        find.byWidgetPredicate((w) => w is Slider && w.value == 500),
        findsOneWidget,
      );
    });

    testWidgets(
      'Slider changes the position of the player when its value changes',
      (tester) async {
        final audioPlayer = mockAudioPlayer();
        final controller = mockAudioEntryListenerController();

        await withInjected<AudioPlayer>(audioPlayer, () async {
          await tester.pumpWidget(
            testAudioTimelinePlayer(
              controller: controller,
              playerPosition: Duration.zero,
            ),
          );

          await tester.drag(find.byType(Slider), const Offset(100, 0));

          verify(() => audioPlayer.seek(any()));
        });
      },
    );
  });
}
