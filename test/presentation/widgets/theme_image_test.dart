import 'package:bitacora/presentation/widgets/theme_image.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  Widget testAppThemeImage({
    required String name,
    double? width,
    double? height,
    required Brightness brightness,
  }) {
    return MaterialApp(
      theme: ThemeData(brightness: brightness),
      home: Localizations(
        locale: const Locale('en'),
        delegates: AppLocalizations.localizationsDelegates,
        child: ThemeImage(
          name: name,
        ),
      ),
    );
  }

  group('$ThemeImage tests', () {
    testWidgets('Display light image', (tester) async {
      const assetImage = 'images/ic_bitacora.png';
      await tester.pumpWidget(testAppThemeImage(
        name: 'images/ic_bitacora.png',
        brightness: Brightness.light,
        width: 100,
        height: 100,
      ));

      expect(find.byType(Image), findsOneWidget);
      expect(find.byWidgetPredicate((widget) {
        if (widget is Image) {
          final assetImageWidget = widget.image as AssetImage;
          return assetImageWidget.assetName == assetImage;
        }
        return false;
      }), findsOneWidget);
    });

    testWidgets('Display dark image', (tester) async {
      const assetImage = 'images/ic_bitacora.png';
      final split = assetImage.split('.');
      await tester.pumpWidget(testAppThemeImage(
        name: 'images/ic_bitacora.png',
        brightness: Brightness.dark,
        width: 100,
        height: 100,
      ));

      expect(find.byType(Image), findsOneWidget);
      expect(find.byWidgetPredicate((widget) {
        if (widget is Image) {
          final assetImageWidget = widget.image as AssetImage;
          return assetImageWidget.assetName ==
              '${split[0]}${'_dark'}.${split[1]}';
        }
        return false;
      }), findsOneWidget);
    });
  });
}
