import 'dart:async';
import 'dart:math';

import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/metadata/active_custom_field_metadata_filter.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/application/cache/template/template_cache.dart';
import 'package:bitacora/application/location_tracking/location_tracking_common_queries.dart';
import 'package:bitacora/application/location_tracking/widget/location_tracking_monitor_subscription_notifier.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/presentation/daylog/app_bar/feed/daylog_app_bar_feed_badge_repository_query.dart';
import 'package:bitacora/presentation/daylog/app_bar/feed/daylog_app_bar_show_icon_repository_query.dart';
import 'package:bitacora/presentation/daylog/daylog_page.dart';
import 'package:bitacora/presentation/daylog/entry_timer/find_entry_with_timer_repository_query.dart';
import 'package:bitacora/presentation/daylog/my_first_report/my_first_report_repository_query.dart';
import 'package:bitacora/presentation/home_page_selector.dart';
import 'package:bitacora/presentation/login/login_page.dart';
import 'package:bitacora/presentation/organization/choose_organization_page.dart';
import 'package:bitacora/presentation/widgets/loading_indicator.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/access/find_permission_repository_query.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../application/cache/auth/mocks.dart';
import '../application/cache/logday/mocks.dart';
import '../application/cache/organization/mocks.dart';
import '../application/cache/project/mocks.dart';
import '../application/cache/template/mocks.dart';
import '../domain/common/mocks.dart';
import '../domain/entry/mocks.dart';
import '../domain/outgoing_mutation/mocks.dart';
import '../domain/report/mocks.dart';
import '../infrastructure/access/mocks.dart';
import '../infrastructure/location_tracking/mocks.dart';
import '../mocktail_fallback_values.dart';

const kMockNumOrgs = 5;
final Random _random = Random(0);

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  Widget testApp({
    HomePageSelectorWidget? widget,
    ActiveSession? activeSession,
    OrganizationCache? orgCache,
    ActiveOrganization? activeOrg,
  }) {
    List<Organization>? organizations;

    final db = _mockRepository();
    final syncState = SyncState(db, activeOrg ?? mockActiveOrganization());
    activeSession ??= mockActiveSession(session: mockSession());
    if (orgCache == null) {
      organizations =
          List.generate(kMockNumOrgs, (index) => mockOrganization());
      orgCache = mockOrganizationCache(organizations: organizations);
    }
    activeOrg ??= mockActiveOrganization(
      organization: organizations == null
          ? orgCache.value?.first
          : organizations[_random.nextInt(kMockNumOrgs)],
    );

    return MultiProvider(
      providers: [
        Provider<Repository>.value(value: db),
        ChangeNotifierProvider<SyncState>.value(value: syncState),
        ChangeNotifierProvider<ActiveSession>.value(value: activeSession),
        ChangeNotifierProvider<OrganizationCache>.value(value: orgCache),
        ChangeNotifierProvider<ActiveOrganization>.value(value: activeOrg),
        ChangeNotifierProvider<ActiveCustomFieldMetadataFilter>.value(
            value: ActiveCustomFieldMetadataFilter(null)),
        ChangeNotifierProvider<ProjectCache>(
          create: (_) => mockProjectCache(),
        ),
        ChangeNotifierProvider<ActiveProject>(
          create: (_) => mockActiveProject(),
        ),
        ChangeNotifierProvider<ActiveLogDay>(
          create: (_) => mockActiveLogDay(logDay: const LogDay(20220127)),
        ),
        ChangeNotifierProvider<LocationTrackingMonitorSubscriptionNotifier>(
          create: (_) => LocationTrackingMonitorSubscriptionNotifier(),
        ),
        Provider<RecoverSessionLauncher>(
            create: (_) => RecoverSessionLauncher()),
        ChangeNotifierProvider<TemplateCache>.value(value: mockTemplateCache()),
      ],
      child: MaterialApp(
        home: Localizations(
          locale: const Locale('en'),
          delegates: AppLocalizations.localizationsDelegates,
          child: Scaffold(
            body: widget ?? const HomePageSelectorWidget(),
          ),
        ),
      ),
    );
  }

  group('$DaylogPage widget test', () {
    testWidgets('Display LoadingIndicator when ActiveSession is loading',
        (tester) async {
      final activeSession = mockActiveSession(hasLoaded: false);

      await tester.pumpWidget(testApp(activeSession: activeSession));

      expect(find.byType(LoadingIndicatorPage), findsOneWidget);
    });

    testWidgets('Display LoginPage when ActiveSession is null', (tester) async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.onboardingWasSeen: true,
      });

      final activeSession = mockActiveSession();

      await tester.pumpWidget(testApp(activeSession: activeSession));
      await tester.pumpAndSettle();

      expect(find.byType(LoginPage), findsOneWidget);
    });

    testWidgets('Display LoadingIndicator when OrganizationCache is null',
        (tester) async {
      final orgCache =
          mockOrganizationCache(organizationsList: <List<Organization>?>[]);

      await tester.pumpWidget(testApp(orgCache: orgCache));

      expect(find.byType(LoadingIndicatorPage), findsOneWidget);
    });

    testWidgets(
        'Display LoadingIndicator when ActiveOrganization is null but loading',
        (tester) async {
      /// Happens on login. ActiveOrg is initially loaded with null value.
      /// ActiveOrg reloads on orgCache update.
      /// We want to keep showing the LoadingIndicator until activeOrg reloads.
      final activeOrg = mockActiveOrganization(
        organizations: <Organization?>[null],
        isLoading: true,
      );

      await tester.pumpWidget(testApp(activeOrg: activeOrg));

      expect(find.byType(LoadingIndicatorPage), findsOneWidget);
    });

    testWidgets(
        'Display ChooseOrganizationsPage when ActiveOrganization is not loading and value null',
        (tester) async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.onboardingWasSeen: true,
      });
      final activeOrg = mockActiveOrganization(
        organizations: <Organization?>[null],
      );

      await tester.pumpWidget(testApp(activeOrg: activeOrg));
      await tester.pumpAndSettle();

      expect(find.byType(ChooseOrganizationsPage), findsOneWidget);
    });
  });
}

Repository _mockRepository() {
  final access = mockAccessDbTable();
  final db = MockRepository();
  final locationTrackingDbTable = mockLocationTrackingDbTable();
  final report = mockReportRepository();
  final entry = mockEntryRepository();
  final feedPost = mockFeedPostRepository();
  final resource = mockResourceRepository();
  final product = mockProductRepository();
  when(() => db.access).thenReturn(access);
  when(() => db.report).thenReturn(report);
  when(() => db.entry).thenReturn(entry);
  when(() => db.feedPost).thenReturn(feedPost);
  when(() => db.resource).thenReturn(resource);
  when(() => db.product).thenReturn(product);
  when(
    () => db.query(any<FindAccessPermissionRepositoryQuery>()),
  ).thenAnswer((_) => Future.value(const Access()));
  when(
    () => db.query(const FindEntryWithStartedTimerRepositoryQuery()),
  ).thenAnswer((_) => Future.value(null));
  when(
    () => db.query(
      const ActiveEntryLocationTrackingRepositoryQuery(),
    ),
  ).thenAnswer((_) => Future.value(null));
  when(() => db.query(
        const MyFirstReportRepositoryQuery(),
        context: any(named: 'context'),
      )).thenAnswer(
    (invocation) => Future.value([const Report(), const Report()]),
  );
  when(() => db.query(
        const DaylogAppBarFeedShowIconRepositoryQuery(),
        context: any(named: 'context'),
      )).thenAnswer(
    (invocation) => Future.value(true),
  );
  when(() => db.query(
        const DaylogAppBarFeedBadgeRepositoryQuery(),
        context: any(named: 'context'),
      )).thenAnswer(
    (invocation) => Future.value(true),
  );

  when(() => db.locationTracking).thenReturn(locationTrackingDbTable);

  final outgoingMutationRepository = MockOutgoingMutationRepository();
  final controller = StreamController<Mutation<OutgoingMutation>>.broadcast();
  when(() => outgoingMutationRepository.getMutations())
      .thenAnswer((invocation) => controller.stream);
  when(() => db.outgoingMutation).thenReturn(outgoingMutationRepository);

  final queryScope = mockQueryScope();
  final queryContext = mockRepositoryQueryContext(db: db);
  when(
    () => db.queryScope(
        userId: any(named: 'userId'),
        orgId: any(named: 'orgId'),
        projectId: any(named: 'projectId')),
  ).thenReturn(queryScope);
  when(() => db.context(queryScope: any(named: 'queryScope')))
      .thenReturn(queryContext);

  return db;
}
