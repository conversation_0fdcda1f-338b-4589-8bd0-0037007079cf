import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/presentation/daylog/daylog_page.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_repository_query.dart';
import 'package:bitacora/util/access/access_for_organization_repository_query.dart';
import 'package:bitacora/util/access/find_permission_repository_query.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../analytics/mocks.dart';
import '../../../application/cache/auth/mocks.dart';
import '../../../application/cache/logday/mocks.dart';
import '../../../application/cache/organization/mocks.dart';
import '../../../application/cache/project/mocks.dart';
import '../../../domain/common/mocks.dart';
import '../../../domain/report/mocks.dart';
import '../../../infrastructure/access/mocks.dart';
import '../../../infrastructure/entry/mocks.dart';
import '../../../mocktail_fallback_values.dart';

void main() {
  group('$DaylogPage tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });
  });
}

Widget testDaylogPage({
  Repository? db,
  ActiveSession? activeSession,
  ActiveOrganization? activeOrg,
  ProjectCache? projectCache,
  ActiveProject? activeProject,
  AnalyticsLogger? analyticsLogger,
  SyncState? syncState,
  ActiveLogDay? activeLogDay,
}) {
  return MultiProvider(
    providers: [
      Provider<Repository>(create: (_) => db ?? _mockRepository()),
      Provider<AnalyticsLogger>(
          create: (_) => analyticsLogger ?? _mockAnalyticsLogger()),
      Provider<SyncState>(
          create: (_) =>
              syncState ??
              SyncState(
                  MockRepository(), activeOrg ?? mockActiveOrganization())),
      ChangeNotifierProvider<ActiveSession>(
          create: (_) =>
              activeSession ?? mockActiveSession(session: mockSession())),
      ChangeNotifierProvider<ActiveOrganization>(
          create: (_) =>
              activeOrg ??
              mockActiveOrganization(organization: mockOrganization())),
      ChangeNotifierProvider<ProjectCache>(
          create: (_) => projectCache ?? _mockProjectCache()),
      ChangeNotifierProvider<ActiveProject>(
          create: (_) => activeProject ?? _mockActiveProject()),
      ChangeNotifierProvider<ActiveLogDay>(
          create: (_) => activeLogDay ?? _mockActiveLogDay()),
    ],
    child: MaterialApp(
      home: Localizations(
        locale: const Locale('en'),
        delegates: AppLocalizations.localizationsDelegates,
        child: const Scaffold(
          body: DaylogPage(),
        ),
      ),
    ),
  );
}

Repository _mockRepository() {
  final mock = MockRepository();
  final access = mockAccessDbTable();
  final entry = mockEntryDbTable();
  final report = mockReportRepository();
  final context = MockRepositoryQueryContext();

  when(() => mock.access).thenReturn(access);
  when(() => mock.entry).thenReturn(entry);
  when(() => mock.report).thenReturn(report);

  when(() => mock.context(
      cursor: any(named: 'cursor'),
      queryScope: any(named: 'queryScope'))).thenReturn(context);
  when(() => mock.queryScope(
          userId: any(named: 'userId'),
          orgId: any(named: 'orgId'),
          projectId: any(named: 'projectId')))
      .thenAnswer((invocation) => QueryScope(
          userId: invocation.namedArguments[const Symbol('userId')],
          orgId: invocation.namedArguments[const Symbol('orgId')],
          projectId: invocation.namedArguments[const Symbol('projectId')]));

  whenQuery<LogListEntriesRepositoryQuery, List<Entry>>(mock, (_) => <Entry>[]);
  whenQuery<FindAccessPermissionRepositoryQuery, Access?>(
    mock,
    (_) => Access(permission: AccessPermission(kAccessWrite)),
  );
  whenQuery<AccessForOrganizationRepositoryQuery, Access?>(
    mock,
    (_) => Access(permission: AccessPermission(kAccessWrite | kAccessWriteOwn)),
  );

  return mock;
}

AnalyticsLogger _mockAnalyticsLogger() {
  final mock = MockAnalyticsLogger();
  when(() => mock.logEvent(any())).thenAnswer((_) => Future.value(null));
  return mock;
}

ProjectCache _mockProjectCache() {
  final mock = MockProjectCache();
  when(() => mock.value).thenReturn(<Project>[]);
  return mock;
}

ActiveProject _mockActiveProject() {
  final mock = MockActiveProject();
  when(() => mock.value).thenReturn(null);
  return mock;
}

ActiveLogDay _mockActiveLogDay() {
  final mock = MockActiveLogDay();
  const logDay = LogDay(20220127);
  when(() => mock.hasLoaded).thenReturn(true);
  when(() => mock.value).thenReturn(logDay);

  return mock;
}
