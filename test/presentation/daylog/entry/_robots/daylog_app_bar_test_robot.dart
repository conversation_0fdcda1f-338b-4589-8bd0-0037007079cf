import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/presentation/daylog/app_bar/daylog_app_bar.dart';
import 'package:bitacora/presentation/daylog/app_bar/filters/project/daylog_app_bar_project_filter.dart';
import 'package:bitacora/presentation/daylog/sync_state_indicator/sync_state_indicator.dart';
import 'package:bitacora/presentation/widgets/log_date/active_logday_widget.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../../../application/cache/auth/mocks.dart';
import '../../../../application/cache/logday/mocks.dart';
import '../../../../application/cache/organization/mocks.dart';
import '../../../../application/cache/project/mocks.dart';
import '../../../../base/base_robot.dart';
import '../mocks.dart';

class DaylogAppBarTestRobot extends BaseRobot {
  DaylogAppBarTestRobot(super.tester);

  @override
  Future<void> pumpWidget({
    ActiveSession? activeSession,
    ActiveOrganization? activeOrganization,
    ProjectCache? projectCache,
    ActiveProject? activeProject,
    LogDay? activeLogDay,
    SyncState? syncState,
  }) async {
    final selection = CollectionSelection<LocalId>();
    final db = mockRepository();
    return pumpTestApp(
      providers: [
        Provider<Repository>.value(value: db),
        ChangeNotifierProvider<ActiveSession>(
            create: (_) =>
                activeSession ?? mockActiveSession(session: mockSession())),
        ChangeNotifierProvider<ActiveOrganization>(
            create: (_) =>
                activeOrganization ??
                mockActiveOrganization(
                    organization: mockOrganization(withRemoteId: true))),
        ChangeNotifierProvider<ProjectCache>(
            create: (context) =>
                projectCache ??
                mockProjectCache(projects: [mockProject(), mockProject()])),
        ChangeNotifierProvider<ActiveProject>(
            create: (_) => activeProject ?? mockActiveProject()),
        ChangeNotifierProvider<ActiveLogDay>(
            create: (_) =>
                mockActiveLogDay(logDay: activeLogDay ?? const LogDay(290196))),
        ChangeNotifierProvider<SyncState>(
            create: (_) =>
                syncState ??
                SyncState(db, activeOrganization ?? mockActiveOrganization())),
        ChangeNotifierProvider<CollectionSelection<LocalId>>.value(
            value: selection),
      ],
      appBar: const DaylogAppBar(),
    );
  }

  void verifyUi() {
    expect(find.byType(SyncStateIndicator), findsOneWidget);
    expect(find.byType(DaylogAppBarProjectFilter), findsOneWidget);
    expect(find.byType(ActiveLogdayWidget), findsOneWidget);
  }

  void verifyTitle(ActiveOrganization activeOrganization) {
    final organization = activeOrganization.value;
    expect(find.byWidgetPredicate((w) {
      if (w is! Text) {
        return false;
      }

      if (!activeOrganization.hasLoaded) {
        if (w.data! != '') {
          return false;
        }
        return true;
      }

      if (w.data! != organization!.name!.displayValue) {
        return false;
      }

      return true;
    }), findsOneWidget);
  }
}
