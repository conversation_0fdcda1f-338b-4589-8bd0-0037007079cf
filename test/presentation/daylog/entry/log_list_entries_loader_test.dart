import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_loader.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_repository_query.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../../domain/common/mocks.dart';
import '../../../domain/entry/mocks.dart';
import '../../../infrastructure/mocks.dart';
import '../../../mocktail_fallback_values.dart';
import '../../../test_util.dart';

void main() {
  MocktailFallbackValues.ensureInitialized();
  final db = _mockRepository();
  final user = mockUser();
  final org = mockOrganization();
  final project = mockProject(organization: org);
  const logDay = LogDay(19991231);

  group('$LogListEntriesLoader tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();

      whenQuery<LogListEntriesRepositoryQuery, List<Entry>>(
        db,
        (_) => List<Entry>.generate(10, (_) => mockEntry()),
      );
    });

    test('Org is used for query', () async {
      final logListEntriesLoader = LogListEntriesLoader(db, user, false);
      logListEntriesLoader.org = org;
      logListEntriesLoader.day = logDay;

      logListEntriesLoader.loadMoreThan(0);

      final captured = verify(
        () => db.query(
          anyThat<LogListEntriesRepositoryQuery>(),
          context: captureAny(named: 'context'),
        ),
      ).captured.first as DbContext;
      expect(captured.queryScope!.byOrg, true);
      expect(captured.queryScope!.orgId, org.id);
    });

    test('Day is used for query', () async {
      final logListEntriesLoader = LogListEntriesLoader(db, user, false);
      logListEntriesLoader.org = org;
      logListEntriesLoader.day = logDay;

      logListEntriesLoader.loadMoreThan(0);

      final captured = verify(
        () => db.query(
          captureAnyThat<LogListEntriesRepositoryQuery>(),
          context: any(named: 'context'),
        ),
      ).captured.first as LogListEntriesRepositoryQuery;
      expect(captured.day, logDay);
    });

    test('Project is used for query', () async {
      final logListEntriesLoader = LogListEntriesLoader(db, user, false);
      logListEntriesLoader.project = project;
      logListEntriesLoader.day = logDay;

      logListEntriesLoader.loadMoreThan(0);

      final captured = verify(
        () => db.query(
          anyThat<LogListEntriesRepositoryQuery>(),
          context: captureAny(named: 'context'),
        ),
      ).captured.first as DbContext;
      expect(captured.queryScope!.byOrg, false);
      expect(captured.queryScope!.projectId, project.id);
    });

    test('Open entries is used for query', () async {
      final logListEntriesLoader = LogListEntriesLoader(db, user, false, true);
      logListEntriesLoader.org = org;

      logListEntriesLoader.loadMoreThan(0);

      final captured = verify(
        () => db.query(
          captureAnyThat<LogListEntriesRepositoryQuery>(),
          context: any(named: 'context'),
        ),
      ).captured.first as LogListEntriesRepositoryQuery;
      expect(captured.isOpenEntries, true);
    });

    test('Org change with same id does not reload from db', () async {
      final logListEntriesLoader = LogListEntriesLoader(db, user, false);
      logListEntriesLoader.org = org;
      logListEntriesLoader.day = logDay;
      await Future.microtask(() => logListEntriesLoader.loadMoreThan(0));
      logListEntriesLoader.org = Organization(id: org.id);

      await Future.microtask(() => logListEntriesLoader.loadMoreThan(0));

      verify(
        () => db.query(
          anyThat<LogListEntriesRepositoryQuery>(),
          context: any(named: 'context'),
        ),
      ).called(1);
    });

    test('Project change with same id does not reload from db', () async {
      final logListEntriesLoader = LogListEntriesLoader(db, user, false);
      logListEntriesLoader.project = project;
      logListEntriesLoader.day = logDay;
      await Future.microtask(() => logListEntriesLoader.loadMoreThan(0));
      logListEntriesLoader.project = Project(id: project.id);

      await Future.microtask(() => logListEntriesLoader.loadMoreThan(0));

      verify(
        () => db.query(
          anyThat<LogListEntriesRepositoryQuery>(),
          context: any(named: 'context'),
        ),
      ).called(1);
    });

    test('Loads multiple pages', () async {
      final logListEntriesLoader = LogListEntriesLoader(db, user, false);
      logListEntriesLoader.org = org;
      logListEntriesLoader.day = logDay;

      await Future.microtask(() => logListEntriesLoader.loadMoreThan(0));
      await Future.microtask(() => logListEntriesLoader.loadMoreThan(10));
      await Future.microtask(() => logListEntriesLoader.loadMoreThan(20));
      await Future.microtask(() => logListEntriesLoader.loadMoreThan(20));

      verify(
        () => db.query(
          anyThat<LogListEntriesRepositoryQuery>(),
          context: any(named: 'context'),
        ),
      ).called(3);
    });
  });
}

Repository _mockRepository() {
  final entryRepository = mockEntryRepository();
  final mock = MockRepository();
  when(() => mock.entry).thenReturn(entryRepository);
  when(
    () => mock.queryScope(
      userId: any(named: 'userId'),
      orgId: any(named: 'orgId'),
      projectId: any(named: 'projectId'),
    ),
  ).thenAnswer(
    (invocation) {
      return QueryScope(
        userId: invocation.namedArguments[const Symbol('userId')],
        orgId: invocation.namedArguments[const Symbol('orgId')],
        projectId: invocation.namedArguments[const Symbol('projectId')],
      );
    },
  );
  when(
    () => mock.context(
      cursor: any(named: 'cursor'),
      queryScope: any(named: 'queryScope'),
    ),
  ).thenAnswer(
    (invocation) => DbContext(
      db: MockDbRepository(),
      cursor: invocation.namedArguments[const Symbol('cursor')],
      queryScope: invocation.namedArguments[const Symbol('queryScope')],
    ),
  );
  return mock;
}
