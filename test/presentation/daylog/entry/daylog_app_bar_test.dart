import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/presentation/daylog/app_bar/daylog_app_bar.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../application/cache/organization/mocks.dart';
import '../../../mocktail_fallback_values.dart';
import '_robots/daylog_app_bar_test_robot.dart';

void main() {
  group('$DaylogAppBar tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Has Ui', (tester) async {
      final robot = DaylogAppBarTestRobot(tester);

      await robot.pumpWidget();

      robot.verifyUi();
    });

    testWidgets('Title when ActiveOrganization has Loaded', (tester) async {
      final robot = DaylogAppBarTestRobot(tester);
      final activeOrganization = mockActiveOrganization(
        hasLoaded: true,
        organization: mockOrganization(withRemoteId: true),
      );

      await robot.pumpWidget(activeOrganization: activeOrganization);

      robot.verifyTitle(activeOrganization);
    });

    testWidgets('Title when ActiveOrganization has not Loaded', (tester) async {
      final robot = DaylogAppBarTestRobot(tester);
      final activeOrganization = mockActiveOrganization(
        hasLoaded: false,
      );

      await robot.pumpWidget(activeOrganization: activeOrganization);

      robot.verifyTitle(activeOrganization);
    });
  });
}
