import 'package:bitacora/presentation/daylog/audio/audio_entry_listener.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocktail_fallback_values.dart';
import '_robots/audio_entry_recorder_test_robot.dart';
import 'mocks.dart';

void main() {
  group('$AudioEntryListener tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Ui widgets', (tester) async {
      final robot = AudioEntryRecorderTestRobot(
        tester,
        controller: mockAudioEntryListenerController(),
      );

      await robot.pumpWidget();

      robot.verifyUi();
    });
  });
}
