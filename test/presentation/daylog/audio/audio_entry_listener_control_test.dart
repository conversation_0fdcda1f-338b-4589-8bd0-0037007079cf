import 'package:audioplayers/audioplayers.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_side_control.dart';
import 'package:bitacora/presentation/widgets/play_button.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../mocktail_fallback_values.dart';
import 'mocks.dart';

void main() {
  group('$AudioEntryListenerSideControl tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Ui when listening and not isAnchoredRecording',
        (tester) async {
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(true),
        isAnchored: ValueNotifier(false),
      );

      await tester
          .pumpWidget(_testAudioEntryRecorderControl(controller: controller));

      expect(find.byIcon(Icons.lock_open), findsOneWidget);
      expect(find.byIcon(Icons.expand_less), findsOneWidget);
      expect(find.byIcon(Icons.save), findsNothing);
      expect(find.byIcon(Icons.stop), findsOneWidget);
    });

    testWidgets('Ui when listening and isAnchoredRecording', (tester) async {
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(true),
        isAnchored: ValueNotifier(true),
      );

      await tester
          .pumpWidget(_testAudioEntryRecorderControl(controller: controller));

      expect(find.byIcon(Icons.lock_open), findsNothing);
      expect(find.byIcon(Icons.expand_less), findsNothing);
      expect(find.byIcon(Icons.save), findsOneWidget);
      expect(find.byIcon(Icons.stop), findsOneWidget);
    });

    testWidgets('Ui when is not listening and isAnchoredRecording',
        (tester) async {
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(false),
        isAnchored: ValueNotifier(true),
        audioAttachment: mockAttachment(),
      );

      await tester
          .pumpWidget(_testAudioEntryRecorderControl(controller: controller));

      expect(find.byIcon(Icons.lock_open), findsNothing);
      expect(find.byIcon(Icons.expand_less), findsNothing);
      expect(find.byIcon(Icons.save), findsOneWidget);
      expect(find.byType(PlayButton), findsOneWidget);
      expect(find.byIcon(Icons.stop), findsNothing);
    });

    testWidgets('Ui audio attachment is playing', (tester) async {
      final audioPlayer = mockAudioPlayer(state: PlayerState.playing);
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(false),
        isAnchored: ValueNotifier(true),
        audioAttachment: mockAttachment(),
      );

      await withInjected<AudioPlayer>(
          audioPlayer,
          () => tester.pumpWidget(
                _testAudioEntryRecorderControl(controller: controller),
              ));

      expect(find.byIcon(Icons.lock_open), findsNothing);
      expect(find.byIcon(Icons.expand_less), findsNothing);
      expect(find.byIcon(Icons.save), findsOneWidget);
      expect(find.byType(PlayButton), findsOneWidget);
      expect(find.byIcon(Icons.stop), findsNothing);
    });

    testWidgets('Save and Hide when tap save icon', (tester) async {
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(false),
        isAnchored: ValueNotifier(true),
        audioAttachment: mockAttachment(),
      );
      await tester
          .pumpWidget(_testAudioEntryRecorderControl(controller: controller));
      await tester.ensureVisible(find.byIcon(Icons.save));

      await tester.pumpAndSettle();
      await tester.tap(find.byIcon(Icons.save));

      verify(() => controller.saveAndHide(any()));
    });

    testWidgets(
      'Stop and show player when tap stop icon',
      (tester) async {
        final controller = mockAudioEntryListenerController(
          isListening: ValueNotifier(true),
          isAnchored: ValueNotifier(true),
        );
        await tester
            .pumpWidget(_testAudioEntryRecorderControl(controller: controller));
        await tester.pumpAndSettle();

        await tester.tap(find.byIcon(Icons.stop));
        await tester.pumpAndSettle();

        verify(() => controller.stopListeningAndShowPlayer()).called(1);
      },
    );

    testWidgets('Pause player when tap pause icon', (tester) async {
      final audioPlayer = mockAudioPlayer(state: PlayerState.playing);
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(false),
        isAnchored: ValueNotifier(true),
        audioAttachment: mockAttachment(),
      );
      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester
            .pumpWidget(_testAudioEntryRecorderControl(controller: controller));

        final pauseButton = find.byType(PlayButton);
        await tester.ensureVisible(pauseButton);
        await tester.pumpAndSettle();
        await tester.tap(pauseButton);

        await tester.pumpAndSettle();
      });

      verify(() => audioPlayer.pause()).called(1);
    });

    testWidgets('Resume player when tap play icon', (tester) async {
      final audioPlayer = mockAudioPlayer();
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(false),
        isAnchored: ValueNotifier(true),
        audioAttachment: mockAttachment(),
      );
      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester
            .pumpWidget(_testAudioEntryRecorderControl(controller: controller));

        final playPauseButton = find.byType(PlayButton);
        await tester.ensureVisible(playPauseButton);
        await tester.pumpAndSettle();
        await tester.tap(playPauseButton);

        await tester.pumpAndSettle();
      });

      verify(() => audioPlayer.resume()).called(1);
    });

    testWidgets('Icon lock when is Anchored', (tester) async {
      final controller = mockAudioEntryListenerController(
        isListening: ValueNotifier(true),
        isAnchored: ValueNotifier(false),
        swipeOffset: ValueNotifier(const Offset(0.0, 100.0)),
      );
      await tester
          .pumpWidget(_testAudioEntryRecorderControl(controller: controller));

      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.lock), findsOneWidget);
    });
  });
}

Widget _testAudioEntryRecorderControl(
    {AudioEntryListenerController? controller}) {
  return MaterialApp(
    home: Localizations(
      locale: const Locale('en'),
      delegates: AppLocalizations.localizationsDelegates,
      child: Scaffold(
        body: AudioEntryListenerSideControl(
          controller: controller ?? AudioEntryListenerController(),
        ),
      ),
    ),
  );
}
