import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_presenter.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';
import 'package:record/record.dart';
import 'package:speech_to_text/speech_to_text.dart';

import '../../../test_util.dart';

class MockAudioEntryListenerPresenterController extends Mock
    implements AudioEntryListenerPresenterController {}

class MockAudioEntryListenerController extends Mock
    implements AudioEntryListenerController {}

class MockSpeechToText extends Mock implements SpeechToText {}

class MockRecord extends Mock implements AudioRecorder {}

class MockAudioPlayer extends Mock implements AudioPlayer {}

AudioEntryListenerPresenterController
    mockAudioEntryListenerPresenterController({
  ValueNotifier<Offset?>? longPressPosition,
  AudioEntryListenerController? audioEntryListenerController,
}) {
  final mock = MockAudioEntryListenerPresenterController();
  when(() => mock.longPressPosition)
      .thenAnswer((_) => longPressPosition ?? ValueNotifier(null));
  when(() => mock.audioListenerController)
      .thenAnswer((_) => ValueNotifier(audioEntryListenerController));

  return mock;
}

AudioEntryListenerController mockAudioEntryListenerController({
  ValueNotifier<bool>? isShowing,
  ValueNotifier<bool>? isListening,
  ValueNotifier<bool>? isAnchored,
  ValueNotifier<bool>? isInterrupted,
  ValueNotifier<bool>? isPermissionDenied,
  ValueNotifier<Offset>? swipeOffset,
  Attachment? audioAttachment,
  Duration? duration,
  Stopwatch? stopwatch,
  Completer? showCompleter,
  bool? isFinishingListening,
  bool? isCanceled,
  AudioEntryListenerMode? mode,
}) {
  stopwatch ??= mockStopwatch();
  final controller = MockAudioEntryListenerController();
  when(() => controller.isShowing)
      .thenAnswer((_) => isShowing ?? ValueNotifier<bool>(false));
  when(() => controller.isListening)
      .thenAnswer((_) => isListening ?? ValueNotifier(false));
  when(() => controller.isAnchored)
      .thenAnswer((_) => isAnchored ?? ValueNotifier(false));
  when(() => controller.isPermissionDenied)
      .thenReturn(isInterrupted ?? ValueNotifier(false));
  when(() => controller.swipeOffset)
      .thenReturn(swipeOffset ?? ValueNotifier(Offset.zero));
  when(() => controller.recognizedWords).thenReturn(ValueNotifier(''));
  when(() => controller.onSpeechToTextInterrupted(any()))
      .thenAnswer((_) => Future.value(null));
  when(
    () => controller.show(any()),
  ).thenAnswer((_) async {
    if (showCompleter != null) {
      await showCompleter.future;
    }
    return Future.value();
  });
  when(() => controller.finishListeningAndSave(any()))
      .thenAnswer((_) => Future.value());
  when(() => controller.stopListeningAndShowPlayer())
      .thenAnswer((_) => Future.value());
  when(() => controller.saveAndHide(any())).thenAnswer((_) => Future.value());
  when(() => controller.cancel(any())).thenAnswer((_) => Future.value());
  when(() => controller.anchor()).thenAnswer((_) => Future.value());
  when(() => controller.duration).thenReturn(duration ?? Duration.zero);
  when(() => controller.stopwatch).thenReturn(stopwatch);
  when(() => controller.isFinishingListening)
      .thenReturn(isFinishingListening ?? false);
  when(() => controller.isCanceled).thenReturn(isCanceled ?? false);
  when(() => controller.mode).thenReturn(mode ?? AudioEntryListenerMode.all);

  return controller;
}

SpeechToText mockSpeechToText({
  bool? isListening,
}) {
  final speechToText = MockSpeechToText();
  when(() => speechToText.isListening).thenReturn(isListening ?? false);
  when(() => speechToText.initialize(
        onStatus: any(named: 'onStatus'),
        onError: any(named: 'onError'),
      )).thenAnswer((_) => Future.value(true));
  when(() => speechToText.listen(onResult: any(named: 'onResult')))
      .thenAnswer((_) => Future.value());
  when(() => speechToText.stop()).thenAnswer((_) => Future.value());
  when(() => speechToText.cancel()).thenAnswer((_) => Future.value());

  return speechToText;
}

AudioRecorder mockAudioRecorder({
  bool? isRecording,
  bool? hasPermission,
  String? filePath,
}) {
  final record = MockRecord();
  String? path;
  when(() => record.isRecording())
      .thenAnswer((_) => Future.value(isRecording ?? false));
  when(() => record.hasPermission())
      .thenAnswer((_) => Future.value(hasPermission ?? false));
  when(() => record.start(const RecordConfig(), path: any(named: 'path')))
      .thenAnswer((invocation) {
    path = invocation.namedArguments[const Symbol('path')];
    return Future.value();
  });
  when(() => record.hasPermission()).thenAnswer((_) => Future.value(true));
  when(() => record.stop()).thenAnswer((_) {
    FileSystemInjector.get().file(path ?? 'staging').create(recursive: true);
    return Future.value();
  });

  return record;
}

AudioPlayer mockAudioPlayer({
  PlayerState? state,
  Duration? duration,
  Stream<Duration>? onAudioPositionChanged,
  Stream<PlayerState>? onPlayerStateChanged,
  Stream<Duration>? onDurationChanged,
  Future<int> Function()? onResume,
  Future<int> Function()? onPause,
}) {
  final player = MockAudioPlayer();
  when(() => player.state).thenReturn(state ?? PlayerState.stopped);
  when(() => player.onPlayerStateChanged).thenAnswer(
      (_) => onPlayerStateChanged ?? StreamController<PlayerState>().stream);
  when(() => player.onPositionChanged).thenAnswer(
      (_) => onAudioPositionChanged ?? StreamController<Duration>().stream);
  when(() => player.onDurationChanged).thenAnswer(
      (_) => onDurationChanged ?? StreamController<Duration>().stream);
  when(() => player.resume())
      .thenAnswer((_) => onResume != null ? onResume() : Future.value());
  when(() => player.pause())
      .thenAnswer((_) => onPause != null ? onPause() : Future.value());
  when(() => player.stop()).thenAnswer((_) => Future.value());
  when(() => player.release()).thenAnswer((_) => Future.value());
  when(() => player.getDuration())
      .thenAnswer((_) => Future.value(duration ?? Duration.zero));
  when(() => player.dispose()).thenAnswer((_) => Future.value(null));
  when(() => player.setSourceDeviceFile(any()))
      .thenAnswer((_) => Future.value());
  when(() => player.seek(any())).thenAnswer((_) => Future.value());

  return player;
}
