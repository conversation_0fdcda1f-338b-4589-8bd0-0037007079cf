import 'dart:math';

import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/presentation/organization/organizations_page.dart';
import 'package:bitacora/presentation/widgets/circle_check.dart';
import 'package:bitacora/presentation/widgets/menu_list.dart';
import 'package:bitacora/presentation/widgets/menu_list_item.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/navigator_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../application/cache/organization/mocks.dart';
import '../../../base/base_robot.dart';
import '../../../domain/common/mocks.dart';
import '../../../domain/organization/mocks.dart';
import '../../../util/mocks.dart';

class OrganizationsPageTestRobot extends BaseRobot {
  final NavigatorUtils _navigatorUtils = mockNavigatorUtils();
  final Random _random = Random(0);

  OrganizationsPageTestRobot(super.tester);

  @override
  Future<void> pumpWidget({
    List<Organization>? organizations,
    int? indexActiveOrganization,
  }) async {
    final activeOrganization = organizations?[
            indexActiveOrganization ?? _random.nextInt(organizations.length)] ??
        mockOrganization();
    await pumpTestApp(
      providers: [
        ChangeNotifierProvider<OrganizationCache>(
            create: (_) => mockOrganizationCache(
                organizations:
                    organizations ?? <Organization>[activeOrganization])),
        ChangeNotifierProvider(
            create: (_) =>
                mockActiveOrganization(organization: activeOrganization)),
        Provider<Repository>(create: (_) => _mockRepository()),
      ],
      child: const OrganizationsPage(),
    );
  }

  void verifyMenuList(List<Organization> organizations, int activeOrgIndex) {
    expect(find.byType(MenuList), findsOneWidget);
    expect(find.byType(MenuListItem), findsNWidgets(organizations.length));
    expect(find.byWidgetPredicate((w) {
      if (w is! MenuListItem) {
        return false;
      }

      if (!w.isSelected) {
        return false;
      }

      if (w.title != organizations[activeOrgIndex].name!.value) {
        return false;
      }

      return true;
    }), findsOneWidget);
  }

  Future<void> selectNewActiveOrganization(int indexActiveOrg) async {
    await withInjected<NavigatorUtils>(_navigatorUtils, () async {
      await tap(find.byType(MenuListItem).at(indexActiveOrg));
      await tester.pump(
          kCircleCheckAnimationDuration + const Duration(milliseconds: 100));
    });
  }

  void verifyTapNewActiveOrganization() {
    verify(() => _navigatorUtils.popUntilRoot(any())).called(1);
  }

  Repository _mockRepository() {
    final db = MockRepository();
    final orgRepository = mockOrganizationRepository();
    when(() => orgRepository.save(any(), any()))
        .thenAnswer((invocation) => Future.value());
    when(() => db.context()).thenReturn(MockRepositoryQueryContext());
    when(() => db.organization).thenReturn(orgRepository);
    return db;
  }
}
