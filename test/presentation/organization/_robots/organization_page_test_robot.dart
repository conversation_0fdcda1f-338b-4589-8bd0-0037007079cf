import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/presentation/organization/organization_page.dart';
import 'package:bitacora/presentation/organization/staff/active_organization_staff_repository_query.dart';
import 'package:bitacora/presentation/organization/staff/staff_list.dart';
import 'package:bitacora/presentation/organization/staff/user_invite_form.dart';
import 'package:bitacora/util/access/access_for_organization_repository_query.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../application/cache/auth/mocks.dart';
import '../../../application/cache/organization/mocks.dart';
import '../../../base/base_robot.dart';
import '../../../base/test_app.dart';
import '../../../infrastructure/mocks.dart';
import '../../../infrastructure/user/mocks.dart';
import '../../../infrastructure/user_invite/mocks.dart';
import '../../../test_util.dart';
import '../../../util/mocks.dart';
import '../../../util/toast/mocks.dart';

class OrganizationPageTestRobot extends BaseRobot {
  final toast = mockToast();
  final clipboard = mockClipboard();

  OrganizationPageTestRobot(super.tester);

  @override
  Future<void> pumpWidget({Organization? organization, Access? access}) async {
    await tester.pumpWidget(MultiProvider(
      providers: [
        Provider<Repository>(create: (_) => _mockRepository(access: access)),
        ChangeNotifierProvider<ActiveOrganization>(
            create: (_) => mockActiveOrganization(
                organization: organization ?? mockOrganization())),
        ChangeNotifierProvider<ActiveSession>(
            create: (_) => mockActiveSession(session: mockSession()))
      ],
      child: const TestApp(child: OrganizationPage()),
    ));
    await tester.pump();
  }

  void verifyOrgData(Organization organization) {
    expect(find.text(organization.name!.displayValue), findsNWidgets(1));
  }

  void verifyStaff() {
    expect(find.byType(StaffList), findsOneWidget);
  }

  Repository _mockRepository({
    Access? access,
  }) {
    final mock = MockDbRepository();
    final userDbTable = mockUserDbTable();
    when(() => mock.user).thenReturn(userDbTable);
    final userInviteDbTable = mockUserInviteDbTable();
    when(() => mock.userInvite).thenReturn(userInviteDbTable);
    when(() => mock.queryScope(orgId: any(named: 'orgId')))
        .thenReturn(const QueryScope(orgId: LocalId(1)));
    when(() => mock.context(queryScope: any(named: 'queryScope')))
        .thenReturn(DbContext(db: mock));
    when(
      () => mock.query(
        const OrganizationStaffRepositoryQuery(),
        context: any(named: 'context'),
      ),
    ).thenAnswer((_) => Future.value(<User>[mockUser()]));
    when(
      () => mock.query(anyThat<AccessForOrganizationRepositoryQuery>()),
    ).thenAnswer((_) => Future.value(access ?? const Access()));
    return mock;
  }

  void verifyInviteForm(Organization organization, Access access) {
    if (organization.activePlan!.value != OrganizationActivePlan.free.value &&
        access.permission!.value & kAccessAdmin != 0) {
      expect(find.byType(UserInviteForm), findsOneWidget);
    } else {
      expect(find.byType(UserInviteForm), findsNothing);
    }
  }
}
