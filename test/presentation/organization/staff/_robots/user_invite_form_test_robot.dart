import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/application/user_invite/user_invite_service.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/presentation/entry_form/templatelog/user_by_email_repository_query.dart';
import 'package:bitacora/presentation/organization/staff/user_invite_form.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../../application/api/mocks.dart';
import '../../../../application/cache/organization/mocks.dart';
import '../../../../application/sync/mocks.dart';
import '../../../../application/user_invite/mocks.dart';
import '../../../../base/base_robot.dart';
import '../../../../domain/common/mocks.dart';

class UserInviteFormTestRobot extends BaseRobot {
  final userInviteService = mockUserInviteService();
  final syncTrigger = mockSyncTrigger();

  UserInviteFormTestRobot(super.tester);

  @override
  Future<void> pumpWidget() {
    return pumpTestApp(providers: [
      Provider<Repository>(create: (_) => _mockRepository()),
      Provider<ApiHelper>(create: (context) => MockApiHelper()),
      Provider<SyncTrigger>.value(value: syncTrigger),
      ChangeNotifierProvider<ActiveOrganization>(
        create: (context) => mockActiveOrganization(
          organization: mockOrganization(withId: true),
        ),
      ),
    ], child: const UserInviteForm());
  }

  Repository _mockRepository() {
    final mock = MockRepository();
    when(() => mock.context(queryScope: any(named: 'queryScope')))
        .thenReturn(MockRepositoryQueryContext());
    when(() => mock.query(any<UserByEmailRepositoryQuery>(),context: any(named: 'context')))
        .thenAnswer((_) => Future.value(null));
    return mock;
  }

  Future<void> enterEmail(String email) async {
    await enterText(find.byType(TextFormField), text: email);
  }

  Future<void> invite() async {
    await withInjected<UserInviteService>(userInviteService, () async {
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pump();
    });
  }

  void verifyInvalidEmailMessage() {
    expect(find.text('Invalid email'), findsOneWidget);
  }

  void verifyInvite(String email) {
    expect(find.text(email), findsNothing);
    verify(() => userInviteService.invite(any(), email)).called(1);
    final captured = verify(() => syncTrigger.trigger(captureAny())).captured;
    expect(captured.length, 1);
    expect(
      (captured.first as SyncTriggerEvent).source,
      SyncTriggerSource.userInvite,
    );
  }
}
