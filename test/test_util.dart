import 'dart:async';
import 'dart:math';
import 'dart:ui';

import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/platform_utils.dart';
import 'package:file/file.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path/path.dart' as path;
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:test/test.dart';

import 'util/mocks.dart';

typedef BuildContextCallback = void Function(BuildContext);
final Random _random = Random(0);

/// Widget for test code that solves DI through provider
/// (i.e. requires BuildContext)

Widget testAppForContextCallback({
  RouteFactory? onGenerateRoute,
  required List<SingleChildWidget> providers,
  required BuildContextCallback callback,
}) {
  return MultiProvider(
    providers: providers,
    child: MaterialApp(
      onGenerateRoute: onGenerateRoute,
      home: Builder(builder: (context) {
        return TextButton(
          onPressed: () => callback(context),
          child: const Text('Run Test'),
        );
      }),
    ),
  );
}

/// Utility function to await chained asynchronous code.
///
/// i.e.
/// [Future<void>] _x() async {
///   final a = await _a();
///   final b = await _b();
/// }
///
/// void y() {
///   _x();
/// }
///
/// How to await y();? You can't... use awaitUntil(...);
///
///
Future<void> awaitUntil(bool Function() isDone) async {
  StackTrace baseStack;
  try {
    throw '';
  } catch (_, stack) {
    baseStack = stack;
  }

  var i = 0;
  while (!isDone()) {
    i++;
    await Future.microtask(() {});
    if (i == 100) {
      throw baseStack;
    }
  }
}

Future<void> awaitUntilStopsThrowing(VoidCallback throwsAtFirst) async {
  await awaitUntil(() {
    try {
      throwsAtFirst();
      return true;
    } catch (_) {
      return false;
    }
  });
}

/// Platform

dynamic withIOS(dynamic callback) {
  final mock = MockPlatformUtils();
  when(() => mock.isIOS).thenReturn(true);
  when(() => mock.isAndroid).thenReturn(false);

  return withInjected<PlatformUtils>(mock, callback);
}

dynamic withAndroid(dynamic callback) {
  final mock = MockPlatformUtils();
  when(() => mock.isIOS).thenReturn(false);
  when(() => mock.isAndroid).thenReturn(true);

  return withInjected<PlatformUtils>(mock, callback);
}

Future<File> createMockImage(
  FileSystem fileSystem,
  String filePath, [
  int height = 100,
  int width = 100,
]) async {
  final extension = path.extension(filePath);
  assert(extension == '.png' || extension == '.jpg');
  final recorder = PictureRecorder();
  final canvas = Canvas(recorder);
  final paint = Paint();
  paint.color = Colors.red;
  canvas.drawCircle(const Offset(50, 50), _random.nextDouble() * 50, paint);
  final picture = recorder.endRecording();
  final image = await picture.toImage(width, height);
  final byteData = await image.toByteData(format: ImageByteFormat.png);
  final buffer = byteData!.buffer;

  final pngPath = path.setExtension(path.withoutExtension(filePath), '.png');
  final png = await fileSystem.file(pngPath).writeAsBytes(
      buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));

  if (extension == '.jpg') {
    final filePng = decodeImage(fileSystem.file(pngPath).readAsBytesSync())!;
    final jpg = fileSystem.file(filePath).writeAsBytes(encodeJpg(filePng));
    await png.delete();
    return jpg;
  }
  return png;
}

dynamic expectZoneThrows(dynamic Function() f, [dynamic matcher]) async {
  var didZoneThrow = false;
  var didExecThrow = false;
  unawaited(runZonedGuarded(() async {
    try {
      await f();
    } catch (_) {
      didExecThrow = true;
      rethrow;
    }
  }, (e, __) {
    didZoneThrow = true;
    if (matcher != null) {
      expect(e, matcher);
    }
  }));
  await awaitUntil(() => didZoneThrow);
  if (didExecThrow) {
    throw 'Executor thrown instead of zone';
  }
}

Stopwatch mockStopwatch({
  Duration? elapsed,
  bool? isRunning,
}) {
  final mock = MockStopwatch();
  when(() => mock.elapsed).thenReturn(elapsed ?? Duration.zero);
  when(() => mock.elapsedMilliseconds).thenReturn(elapsed?.inMilliseconds ?? 0);
  when(() => mock.isRunning).thenReturn(isRunning ?? false);
  when(() => mock.start()).thenReturn(null);
  when(() => mock.stop()).thenReturn(null);
  when(() => mock.reset()).thenReturn(null);
  return mock;
}

T anyThat<T>({String? named}) {
  return any<T>(named: named, that: isA<T>());
}

T captureAnyThat<T>({String? named}) {
  return captureAny<T>(named: named, that: isA<T>());
}

String _removeSpaces(String string) {
  return string.replaceAll(' ', '').replaceAll('\n', '');
}

void expectRemovingSpaces(String a, String b) {
  expect(_removeSpaces(a), _removeSpaces(b));
}
