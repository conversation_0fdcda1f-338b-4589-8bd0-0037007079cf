import 'package:bitacora/application/last_foreground/last_foreground.dart';
import 'package:bitacora/application/last_foreground/last_foreground_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/test_app.dart';
import '../../mocktail_fallback_values.dart';
import 'mocks.dart';

void main() {
  group('$LastForeground tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Calls LastForegroundUtils().onForeground on init',
        (tester) async {
      final lastForegroundUtils = mockLastForegroundUtils();

      await withInjected<LastForegroundUtils>(
        lastForegroundUtils,
        () async {
          await tester.pumpWidget(TestApp(
              child: LastForeground(
            child: Container(),
          )));

          await tester.pumpAndSettle();
        },
      );

      verify(() => lastForegroundUtils.onForeground()).called(1);
    });

    testWidgets('Calls LastForegroundUtils().onForeground on resume',
        (tester) async {
      final lastForegroundUtils = mockLastForegroundUtils();

      await withInjected<LastForegroundUtils>(
        lastForegroundUtils,
        () async {
          await tester.pumpWidget(TestApp(
              child: LastForeground(
            child: Container(),
          )));

          tester.binding
              .handleAppLifecycleStateChanged(AppLifecycleState.resumed);

          await tester.pumpAndSettle();
        },
      );

      verify(() => lastForegroundUtils.onForeground()).called(2);
    });
  });
}
