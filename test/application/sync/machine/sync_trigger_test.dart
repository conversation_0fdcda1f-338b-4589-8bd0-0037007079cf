import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../test_util.dart';

void main() {
  group('$SyncTrigger tests', () {
    test('Injects new', () {
      expect(SyncTrigger() != SyncTrigger(), true);
    });

    test('Streams trigger', () async {
      final trigger = SyncTrigger();
      SyncTriggerEvent? captured;
      trigger.stream().listen((event) => captured = event);
      const sent = SyncTriggerEvent(SyncTriggerSource.user);

      trigger.trigger(sent);

      await awaitUntil(() => captured == sent);
    });
  });
}
