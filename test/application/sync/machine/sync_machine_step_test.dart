import 'dart:async';

import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocktail_fallback_values.dart';
import '../../../test_util.dart';
import 'mocks.dart';

void main() {
  group('$SyncMachineStep tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test(('Forwards params'), () {
      final params = syncMachineParams();
      final step = TestSyncMachineStep(params);

      expect(step.session, params.session);
      expect(step.organization, params.organization);
      expect(step.db, params.db);
      expect(step.apiTranslator, params.apiTranslator);
      expect(step.apiHelper, params.apiHelper);
    });

    test('Performs sync', () async {
      final step = TestSyncMachineStep(syncMachineParams());

      await step.sync();

      expect(step.isSynced, true);
    });

    test('Throws when busy and sync attempted', () {
      expectZoneThrows(() {
        final step = TestSyncMachineStep(syncMachineParams());
        step.sync();

        step.sync();
      });
    });

    test('Throws when canceled and sync attempted', () {
      expectZoneThrows(() {
        final step = TestSyncMachineStep(syncMachineParams());
        step.sync();
        step.cancel();

        step.sync();
      });
    });

    test('Throws when synced and already synced', () async {
      final step = TestSyncMachineStep(syncMachineParams());
      await step.sync();

      expect(
        () => step.sync(),
        throwsA('Unexpected call to sync [true, false, false]'),
      );
    });

    test('Resets on expected triggers', () {
      _testResetsOn(
        (s) => s.maybeReset(const SyncTriggerEvent(
            SyncTriggerSource.session, SyncTriggerMode.normal)),
        false,
      );
      _testResetsOn(
        (s) => s.maybeReset(const SyncTriggerEvent(
            SyncTriggerSource.session, SyncTriggerMode.fullSync)),
        true,
      );
      _testResetsOn(
        (s) => s.maybeReset(const SyncTriggerEvent(
            SyncTriggerSource.session, SyncTriggerMode.superSync)),
        true,
      );
    });

    test('Marks itself as dirty', () async {
      final step = TestSyncMachineStep(syncMachineParams());
      unawaited(step.sync());

      step.maybeReset(const SyncTriggerEvent(
        SyncTriggerSource.session,
        SyncTriggerMode.fullSync,
      ));

      await awaitUntil(() => step.syncCount == 1);
      expect(step.isSynced, false);
      await step.sync();
      await awaitUntil(() => step.syncCount == 2);
    });

    test('Throws and can retry sync if failed', () async {
      final step = TestSyncMachineStep(syncMachineParams(), crashCountdown: 1);
      await expectLater(
        () => step.sync(),
        throwsA('Crash'),
      );

      expect(step.isSynced, false);
      await step.sync();

      expect(step.syncCount, 2);
      expect(step.isSynced, true);
    });
  });
}

Future<void> _testResetsOn(
    Function(SyncMachineStep step) f, bool shouldReset) async {
  final step = TestSyncMachineStep(syncMachineParams());
  await step.sync();
  f(step);

  if (shouldReset) {
    expect(step.isSynced, false);
    await step.sync();
    expect(step.syncCount, 2);
  } else {
    expect(step.isSynced, true);
    expectZoneThrows(() {
      step.sync();
    });
  }
}
