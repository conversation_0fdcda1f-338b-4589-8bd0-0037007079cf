import 'package:bitacora/application/sync/machine/dedup/sync_entry_dedup_monitor.dart';
import 'package:bitacora/application/sync/machine/sync_machine.dart';
import 'package:bitacora/application/sync/machine/sync_machine_params.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/application/sync/sync_utils.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/connectivity/connectivity.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../mocktail_fallback_values.dart';
import '../../../test_util.dart';
import '../../../util/connectivity/mocks.dart';
import '../../cache/organization/mocks.dart';
import '../mocks.dart';
import 'dedup/mocks.dart';
import 'mocks.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  group('$SyncMachine tests', () {
    test('Injects same injector', () {
      expect(SyncMachineInjector(), SyncMachineInjector());
    });

    test('Injector provides', () {
      withInjected2<SyncEntryDedupMonitor, Connectivity>(
        mockSyncEntryDedupMonitor(),
        MockConnectivity(),
        () {
          final steps = <SyncMachineStep>[];
          final params = syncMachineParams();
          final syncTrigger = SyncTrigger();

          final provided = SyncMachineInjector().get(
            steps: steps,
            params: params,
            syncTrigger: syncTrigger,
          );

          expect(provided.runtimeType.toString(), '$SyncMachine');
          expect(provided.steps, steps);
          expect(provided.params, params);
          expect(provided.syncTrigger, syncTrigger);
        },
      );
    });

    test('Fires SyncEntryDedupMonitor and cancels', () {
      final monitor = mockSyncEntryDedupMonitor();
      withInjected2<SyncEntryDedupMonitor, Connectivity>(
        monitor,
        mockConnectivity(),
        () {
          final syncMachine = _syncMachine();

          syncMachine.cancel();

          verify(() => monitor.cancel());
        },
      );
    });

    test('Fails to sync without connectivity', () async {
      await withInjected2<SyncEntryDedupMonitor, Connectivity>(
        mockSyncEntryDedupMonitor(),
        mockConnectivity(notifier: ValueNotifier([ConnectivityResult.none])),
        () async {
          final syncTrigger = SyncTrigger();
          final syncState = SyncState(mockRepository(),
              mockActiveOrganization(organization: mockOrganization()));
          final isBusyMutations = <bool>[];
          syncState.addListener(() {
            if (isBusyMutations.isEmpty ||
                syncState.isBusy != isBusyMutations.last) {
              isBusyMutations.add(syncState.isBusy);
            }
          });
          _syncMachine(
            syncTrigger: syncTrigger,
            params: syncMachineParams(syncState: syncState),
          );

          syncTrigger.trigger(const SyncTriggerEvent(SyncTriggerSource.db));

          await awaitUntilStopsThrowing(
              () => expect(isBusyMutations, [true, false]));
          expect(syncState.lastResult, SyncResult.connectionError);
          expect(syncState.isApiSyncBusy, false);
        },
      );
    });

    test('Crashes during sync ... isBusy changes to false', () async {
      SharedPreferences.setMockInitialValues({});
      final isBusyMutations = <bool>[];
      final syncState = SyncState(mockRepository(),
          mockActiveOrganization(organization: mockOrganization()));

      expectZoneThrows(() async {
        await withInjected3<SyncEntryDedupMonitor, Connectivity, SyncUtils>(
          mockSyncEntryDedupMonitor(),
          mockConnectivity(),
          mockSyncUtils(),
          () async {
            final syncTrigger = SyncTrigger();
            final params = syncMachineParams(syncState: syncState);
            final step = TestSyncMachineStep(params, crashCountdown: 1);
            syncState.addListener(() {
              if (isBusyMutations.isEmpty ||
                  syncState.isBusy != isBusyMutations.last) {
                isBusyMutations.add(syncState.isBusy);
              }
            });
            _syncMachine(
              params: params,
              steps: [step],
              syncTrigger: syncTrigger,
            );

            syncTrigger.trigger(const SyncTriggerEvent(SyncTriggerSource.db));
          },
        );
      });

      await awaitUntil(() => syncState.isBusy);
      await awaitUntil(() => !syncState.isBusy);
      expect(isBusyMutations, [true, false]);
    });

    test('Runs each step sequentially', () async {
      SharedPreferences.setMockInitialValues({});
      final syncUtils = mockSyncUtils();

      await withInjected3<SyncEntryDedupMonitor, Connectivity, SyncUtils>(
        mockSyncEntryDedupMonitor(),
        mockConnectivity(),
        syncUtils,
        () async {
          final syncTrigger = SyncTrigger();
          final syncState = SyncState(mockRepository(),
              mockActiveOrganization(organization: mockOrganization()));
          final params = syncMachineParams(syncState: syncState);
          final syncedSteps = <SyncMachineStep>[];
          final steps = [
            _syncMachineStep(params, syncedSteps),
            _syncMachineStep(params, syncedSteps),
            _syncMachineStep(params, syncedSteps),
            _syncMachineStep(params, syncedSteps),
            _syncMachineStep(params, syncedSteps),
          ];
          _syncMachine(
            steps: steps,
            syncTrigger: syncTrigger,
            params: params,
          );

          syncTrigger.trigger(const SyncTriggerEvent(
            SyncTriggerSource.session,
            SyncTriggerMode.fullSync,
          ));

          await awaitUntil(() => syncState.isBusy);
          await awaitUntil(() => !syncState.isBusy);
          expect(steps, syncedSteps);
          verify(() => syncUtils.saveLastSyncTimeToNow());
        },
      );
    });

    test('Saves last sync result when done', () async {
      final syncUtils = mockSyncUtils();

      await withInjected3<SyncEntryDedupMonitor, Connectivity, SyncUtils>(
        mockSyncEntryDedupMonitor(),
        mockConnectivity(),
        syncUtils,
        () async {
          final syncTrigger = SyncTrigger();
          final syncState = SyncState(mockRepository(),
              mockActiveOrganization(organization: mockOrganization()));
          _syncMachine(
            syncTrigger: syncTrigger,
            params: syncMachineParams(syncState: syncState),
          );

          syncTrigger.trigger(const SyncTriggerEvent(
            SyncTriggerSource.session,
            SyncTriggerMode.fullSync,
          ));

          await awaitUntil(() => syncState.isBusy);
          await awaitUntil(() => !syncState.isBusy);
          verify(() => syncUtils.saveLastSyncTimeToNow());
        },
      );
    });

    test('Cancels steps', () async {
      SharedPreferences.setMockInitialValues({});
      await withInjected2<SyncEntryDedupMonitor, Connectivity>(
        mockSyncEntryDedupMonitor(),
        mockConnectivity(),
        () async {
          final syncTrigger = SyncTrigger();
          final syncState = SyncState(mockRepository(),
              mockActiveOrganization(organization: mockOrganization()));
          final params = syncMachineParams(syncState: syncState);
          final syncedSteps = <SyncMachineStep>[];
          final steps = [
            _syncMachineStep(params, syncedSteps),
            _syncMachineStep(params, syncedSteps),
          ];
          final syncMachine = _syncMachine(
            params: params,
            steps: steps,
            syncTrigger: syncTrigger,
          );
          syncTrigger.trigger(const SyncTriggerEvent(
            SyncTriggerSource.session,
            SyncTriggerMode.fullSync,
          ));
          final prefs = await SharedPreferences.getInstance();
          await awaitUntilStopsThrowing(() {
            prefs.reload();
            expect(prefs.getBool(SharedPreferencesKeys.hasPendingSync), true);
          });

          syncMachine.cancel();

          await awaitUntil(() => !syncState.isBusy);
          expect(steps.length, greaterThan(syncedSteps.length));
          for (var step in steps) {
            expect(step.isCanceled, true);
          }
          expect(syncState.isApiSyncBusy, false);
          expect(syncState.lastResult, SyncResult.canceled);
        },
      );
    });

    test('Interrupted sync resumes', () {
      expectZoneThrows(
        () async =>
            await withInjected3<SyncEntryDedupMonitor, Connectivity, SyncUtils>(
          mockSyncEntryDedupMonitor(),
          mockConnectivity(),
          mockSyncUtils(),
          () async {
            final syncTrigger = SyncTrigger();
            final syncState = SyncState(mockRepository(),
                mockActiveOrganization(organization: mockOrganization()));
            final params = syncMachineParams(syncState: syncState);
            final syncedSteps = <SyncMachineStep>[];
            final steps = [
              _syncMachineStep(params, syncedSteps),
              _syncMachineStep(params, syncedSteps, crashCountdown: 1),
              _syncMachineStep(params, syncedSteps),
            ];
            _syncMachine(
              params: params,
              steps: steps,
              syncTrigger: syncTrigger,
            );

            syncTrigger.trigger(const SyncTriggerEvent(
              SyncTriggerSource.session,
              SyncTriggerMode.fullSync,
            ));
            await awaitUntil(() => syncState.isBusy);
            await awaitUntil(() => !syncState.isBusy);
            expect(syncedSteps.length, 2);

            syncTrigger.trigger(const SyncTriggerEvent(
              SyncTriggerSource.connectivity,
              SyncTriggerMode.fullSync,
            ));
            await awaitUntil(() => syncState.isBusy);
            await awaitUntil(() => !syncState.isBusy);
            expect(syncedSteps.length, steps.length + 1);
          },
        ),
      );
    });

    test('Sync restarts all steps', () async {
      await withInjected3<SyncEntryDedupMonitor, Connectivity, SyncUtils>(
        mockSyncEntryDedupMonitor(),
        mockConnectivity(),
        mockSyncUtils(),
        () async {
          final syncTrigger = SyncTrigger();
          final syncState = SyncState(mockRepository(),
              mockActiveOrganization(organization: mockOrganization()));
          final params = syncMachineParams(syncState: syncState);
          final syncedSteps = <SyncMachineStep>[];
          final steps = [
            _syncMachineStep(params, syncedSteps),
            _syncMachineStep(params, syncedSteps),
          ];
          _syncMachine(
            params: params,
            steps: steps,
            syncTrigger: syncTrigger,
          );

          syncTrigger.trigger(const SyncTriggerEvent(
            SyncTriggerSource.session,
            SyncTriggerMode.fullSync,
          ));
          await awaitUntil(() => syncState.isBusy);
          await awaitUntil(() => !syncState.isBusy);

          syncTrigger.trigger(const SyncTriggerEvent(
            SyncTriggerSource.session,
            SyncTriggerMode.fullSync,
          ));
          await awaitUntil(() => syncState.isBusy);
          await awaitUntil(() => !syncState.isBusy);

          expect(steps.length * 2, syncedSteps.length);
        },
      );
    });

    test('Successful sync clears hasPendingSync flag', () async {
      await _testClearsHasPendingSync(
          const SyncTriggerEvent(
              SyncTriggerSource.session, SyncTriggerMode.fullSync),
          false);
    });

    test('Incomplete sync does not clear hasPendingSync flag', () async {
      await _testClearsHasPendingSync(
        const SyncTriggerEvent(
            SyncTriggerSource.session, SyncTriggerMode.fullSync),
        false,
      );
      await _testClearsHasPendingSync(
        const SyncTriggerEvent(
            SyncTriggerSource.session, SyncTriggerMode.fullSync),
        true,
        hasConnectivity: false,
      );
    });

    test('Failed sync does not clear hasPendingSync flag', () async {
      SyncResult? result;
      await expectZoneThrows(
        () async {
          result = await _testClearsHasPendingSync(
            const SyncTriggerEvent(
              SyncTriggerSource.session,
              SyncTriggerMode.fullSync,
            ),
            true,
            hasBadStep: true,
          );
        },
        'Crash',
      );
      await awaitUntil(() => result == SyncResult.unknownError);
    });
  });
}

SyncMachineStep _syncMachineStep(
  SyncMachineParams params,
  List<SyncMachineStep> syncedSteps, {
  int crashCountdown = 0,
}) {
  return TestSyncMachineStep(
    params,
    onSync: (step) => syncedSteps.add(step),
    crashCountdown: crashCountdown,
  );
}

SyncMachine _syncMachine({
  List<SyncMachineStep>? steps,
  SyncMachineParams? params,
  SyncTrigger? syncTrigger,
}) {
  params ??= syncMachineParams();
  return SyncMachineInjector().get(
    steps: steps ?? [TestSyncMachineStep(params)],
    params: params,
    syncTrigger: syncTrigger ?? SyncTrigger(),
  );
}

Future<SyncResult> _testClearsHasPendingSync(
  SyncTriggerEvent event,
  bool hasPendingSyncAfterSync, {
  bool hasConnectivity = true,
  bool hasBadStep = false,
}) async {
  SharedPreferences.setMockInitialValues({
    SharedPreferencesKeys.hasPendingSync: true,
  });
  final syncState = SyncState(mockRepository(),
      mockActiveOrganization(organization: mockOrganization()));
  await withInjected3<SyncEntryDedupMonitor, Connectivity, SyncUtils>(
    mockSyncEntryDedupMonitor(),
    mockConnectivity(
        notifier: ValueNotifier(hasConnectivity
            ? [ConnectivityResult.wifi]
            : [ConnectivityResult.none])),
    mockSyncUtils(),
    () async {
      final syncTrigger = SyncTrigger();
      final params = syncMachineParams(syncState: syncState);
      final steps = hasBadStep
          ? <SyncMachineStep>[
              _syncMachineStep(
                syncMachineParams(),
                <SyncMachineStep>[],
                crashCountdown: 1,
              )
            ]
          : null;
      final isBusyMutations = [];
      syncState.addListener(() {
        if (isBusyMutations.isEmpty ||
            syncState.isBusy != isBusyMutations.last) {
          isBusyMutations.add(syncState.isBusy);
        }
      });

      _syncMachine(
        params: params,
        steps: steps,
        syncTrigger: syncTrigger,
      );

      syncTrigger.trigger(event);

      await awaitUntilStopsThrowing(
        () => expect(isBusyMutations, [true, false]),
      );
      final prefs = await SharedPreferences.getInstance();
      await prefs.reload();
      expect(
        prefs.getBool(SharedPreferencesKeys.hasPendingSync),
        hasPendingSyncAfterSync,
      );
      expect(
        syncState.lastResult,
        hasBadStep
            ? SyncResult.unknownError
            : hasConnectivity
                ? SyncResult.ok
                : SyncResult.connectionError,
      );
    },
  );
  return syncState.lastResult;
}
