import 'package:bitacora/application/sync/machine/steps/access/sync_machine_step_access.dart';
import 'package:bitacora/application/sync/machine/steps/cleanup/sync_machine_step_cleanup.dart';
import 'package:bitacora/application/sync/machine/steps/cleanup/sync_machine_step_outgoing_mutations_cleanup.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/custom_field/sync_collection_custom_field_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/entry/sync_collection_entry_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/entry/sync_machine_step_entry_sync_migration.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/entry_groups/sync_collection_entry_group_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/feed_post/sync_collection_feed_post_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/person_details/sync_collection_person_detail_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/project/sync_collection_project_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/project/sync_machine_step_conditional_project_resync.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/qr_code/sync_collection_qr_code_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/resource/sync_collection_resource_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/resource_aggregation/sync_collection_resource_aggregation_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/resource_category/sync_collection_resource_category_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/signature/sync_collection_signature_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_machine_step_collection_download.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/template/sync_collection_template_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/template_condition/sync_collection_template_condition_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/user/sync_collection_user_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/flags/sync_machine_step_flags_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/head/sync_machine_step_head.dart';
import 'package:bitacora/application/sync/machine/steps/sync_machine_steps_builder.dart';
import 'package:bitacora/application/sync/machine/steps/upload/sync_machine_step_upload.dart';
import 'package:bitacora/application/sync/machine/sync_machine_multi_step.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../mocks.dart';

void main() {
  group('$SyncMachineStepsBuilder tests', () {
    test('Injects same', () {
      expect(SyncMachineStepsBuilder(), SyncMachineStepsBuilder());
    });

    test('Builds for foreground sync', () {
      final builder = SyncMachineStepsBuilder();
      final params = mockSyncMachineParams();

      final steps = builder.buildForForegroundSync(params);

      _expectFullList(steps);
    });

    test('Builds for background sync with upload only', () async {
      SharedPreferences.setMockInitialValues({});
      final builder = SyncMachineStepsBuilder();
      final params = mockSyncMachineParams();

      final steps = await builder.buildForBackgroundSync(params);

      expect(steps.first is SyncMachineStepUpload, true);
      expect(steps.length, 1);
    });
  });
}

void _expectFullList(List<SyncMachineStep> steps) {
  var i = 0;
  final multiStep = steps[i++] as SyncMachineMultiStep;

  expect(multiStep.steps[0] is SyncMachineMultiStep, true);
  final multiStepHeadFlags = multiStep.steps[0] as SyncMachineMultiStep;
  expect(multiStepHeadFlags.steps[0] is SyncMachineStepHead, true);
  expect(multiStepHeadFlags.steps[1] is SyncMachineStepFlagsDownloader, true);

  expect(multiStep.steps[1] is SyncMachineStepAccess, true);
  expect(multiStep.steps[2] is SyncMachineMultiStep, true);
  expect(multiStep.steps.length, 3);
  expect(multiStep.isCritical, true);
  expect(multiStep.isParallel, true);

  final multiStep2 = multiStep.steps[2] as SyncMachineMultiStep;
  expect(multiStep2.steps[0] is SyncMachineStepCollectionDownload, true);
  expect(multiStep2.steps[1] is SyncMachineStepCollectionDownload, true);
  expect(multiStep2.steps[2] is SyncMachineStepCollectionDownload, true);
  expect(multiStep2.steps.length, 3);
  expect(multiStep2.isCritical, false);
  expect(multiStep2.isParallel, false);

  final collectionDownloadStep1 =
      multiStep2.steps[0] as SyncMachineStepCollectionDownload;
  expect(
      collectionDownloadStep1.downloaders[0] is SyncCollectionProjectDownloader,
      true);
  expect(collectionDownloadStep1.downloaders[1] is SyncCollectionUserDownloader,
      true);
  expect(
      collectionDownloadStep1.downloaders[2]
          is SyncCollectionCustomFieldDownloader,
      true);
  expect(
      collectionDownloadStep1.downloaders[3] is SyncCollectionQrCodeDownloader,
      true);
  expect(
      collectionDownloadStep1.downloaders[4]
          is SyncCollectionResourceDownloader,
      true);
  expect(
      collectionDownloadStep1.downloaders[5]
          is SyncCollectionFeedPostDownloader,
      true);
  expect(collectionDownloadStep1.downloaders.length, 6);

  final collectionDownloadStep2 =
      multiStep2.steps[1] as SyncMachineStepCollectionDownload;
  expect(
      collectionDownloadStep2.downloaders[0]
          is SyncCollectionTemplateDownloader,
      true);
  expect(
      collectionDownloadStep2.downloaders[1]
          is SyncCollectionPersonDetailDownloader,
      true);
  expect(
      collectionDownloadStep2.downloaders[2]
          is SyncCollectionResourceCategoryDownloader,
      true);
  expect(
      collectionDownloadStep2.downloaders[3]
          is SyncCollectionResourceAggregationDownloader,
      true);
  expect(collectionDownloadStep2.downloaders.length, 4);

  final collectionDownloadStep3 =
      multiStep2.steps[2] as SyncMachineStepCollectionDownload;
  expect(
      collectionDownloadStep3.downloaders[0]
          is SyncCollectionTemplateConditionDownloader,
      true);
  expect(collectionDownloadStep3.downloaders.length, 1);

  expect(steps[i++] is SyncMachineStepUpload, true);
  expect(steps[i++] is SyncMachineStepEntrySyncMigration, true);

  final multiStep3 = steps[i++] as SyncMachineMultiStep;
  expect(multiStep3.steps[0] is SyncMachineStepCollectionDownload, true);
  expect(multiStep3.steps[1] is SyncMachineStepCollectionDownload, true);

  final collectionDownloadStep4 =
      multiStep3.steps[0] as SyncMachineStepCollectionDownload;
  expect(
      collectionDownloadStep4.downloaders[0] is SyncCollectionEntryDownloader,
      true);
  expect(collectionDownloadStep4.downloaders.length, 1);

  final collectionDownloadStep5 =
      multiStep3.steps[1] as SyncMachineStepCollectionDownload;
  expect(
      collectionDownloadStep5.downloaders[0]
          is SyncCollectionSignatureDownloader,
      true);
  expect(
      collectionDownloadStep5.downloaders[1]
          is SyncCollectionEntryGroupDownloader,
      true);
  expect(collectionDownloadStep5.downloaders.length, 2);

  expect(steps[i++] is SyncMachineStepConditionalProjectResync, true);
  expect(steps[i++] is SyncMachineStepOutgoingMutationsCleanup, true);
  expect(steps[i++] is SyncMachineStepCleanup, true);
  expect(steps.length, 7);
}
