import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/sync/machine/steps/head/sync_machine_step_head.dart';
import 'package:bitacora/domain/common/query/organization_common_repository_queries.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/entry/entry_db_table.dart';
import 'package:bitacora/infrastructure/organization/organization_db_table.dart';
import 'package:bitacora/infrastructure/project/project_db_table.dart';
import 'package:bitacora/infrastructure/user/user_db_table.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sqflite/sqflite.dart';

import '../../../../../domain/common/mocks.dart';
import '../../../../../infrastructure/entry/mocks.dart';
import '../../../../../infrastructure/mocks.dart';
import '../../../../../infrastructure/organization/mocks.dart';
import '../../../../../infrastructure/project/mocks.dart';
import '../../../../../infrastructure/user/mocks.dart';
import '../../../../../mocktail_fallback_values.dart';
import '../../../../api/mocks.dart';
import '../../../mock_api_queries.dart';
import '../../mocks.dart';

void main() {
  setUp(() {
    MocktailFallbackValues.ensureInitialized();
  });

  group('$SyncMachineStepHead tests', () {
    test('Throws on api error', () async {
      final syncStep = SyncMachineStepHead(
        syncMachineParams(
          db: _mockRepository(),
          apiHelper: _mockApiHelper(throws: true),
        ),
      );

      expect(() async => await syncStep.sync(), throwsA(isA<DioException>()));
    });

    test('Saves user', () async {
      final userTable = mockUserDbTable();
      final txn = MockTransaction();
      final syncStep = SyncMachineStepHead(
        syncMachineParams(
          db: _mockRepository(txn: txn, userDbTable: userTable),
          apiHelper: _mockApiHelper(),
          apiTranslator: _mockApiTranslator(),
        ),
      );

      await syncStep.sync();

      verify(
        () => userTable.safeSave(
          any(that: DbContextTxnMatcher(txn)),
          any(
            that: const TypeMatcher<User>().having(
              (u) => u.remoteId!.value,
              'remoteId',
              kMockApiSyncMultiOrgHeadResponse['user']['id'],
            ),
          ),
        ),
      );
    });

    test('Saves all orgs', () async {
      final orgTable = mockOrganizationDbTable();
      final txn = MockTransaction();
      final syncStep = SyncMachineStepHead(
        syncMachineParams(
          db: _mockRepository(txn: txn, orgDbTable: orgTable),
          apiHelper: _mockApiHelper(),
          apiTranslator: _mockApiTranslator(),
        ),
      );

      await syncStep.sync();

      final captured = verify(
        () => orgTable.save(
          any(that: DbContextTxnMatcher(txn)),
          captureAny(),
        ),
      ).captured;
      for (var i = 0; i < captured.length; i++) {
        expect(
          (captured[i] as Organization).remoteId!.value,
          kMockApiSyncMultiOrgHeadResponse['organizations'][i]['id'],
        );
      }
    });

    test('Deletes missing orgs', () async {
      final orgTable = mockOrganizationDbTable();
      final txn = MockTransaction();
      var localOrganizations = [
        Organization(
            id: LocalId(
                kMockApiSyncMultiOrgHeadResponse['organizations'][0]['id'])),
        const Organization(id: LocalId(987)),
        const Organization(id: LocalId(986)),
      ];
      final syncStep = SyncMachineStepHead(
        syncMachineParams(
          db: _mockRepository(
            txn: txn,
            orgDbTable: orgTable,
            localOrganizations: localOrganizations,
          ),
          apiHelper: _mockApiHelper(),
          apiTranslator: _mockApiTranslator(),
        ),
      );

      await syncStep.sync();

      final captured = verify(
        () => orgTable.delete(
          any(that: DbContextTxnMatcher(txn)),
          captureAny(),
        ),
      ).captured;
      expect((captured[0] as LocalId).value, localOrganizations[1].id!.value);
      expect((captured[1] as LocalId).value, localOrganizations[2].id!.value);
      expect(captured.length, 2);
    });

    test('Deleting active org throws', () async {
      const activeOrg = Organization(id: LocalId(987), remoteId: RemoteId(789));
      var localOrganizations = [activeOrg];
      final syncStep = SyncMachineStepHead(
        syncMachineParams(
          organization: activeOrg,
          db: _mockRepository(
            localOrganizations: localOrganizations,
          ),
          apiHelper: _mockApiHelper(),
          apiTranslator: _mockApiTranslator(),
        ),
      );

      expect(
        () async => await syncStep.sync(),
        throwsA('Active org was deleted'),
      );
    });
  });
}

Repository _mockRepository({
  Transaction? txn,
  List<Organization>? localOrganizations,
  List<Project>? localProjects,
  OrganizationDbTable? orgDbTable,
  UserDbTable? userDbTable,
  ProjectDbTable? projectDbTable,
  EntryDbTable? entryDbTable,
}) {
  final db = MockDbRepository();
  final context = DbContext(db: db);
  orgDbTable ??= mockOrganizationDbTable();
  userDbTable ??= mockUserDbTable();
  projectDbTable ??= mockProjectDbTable();
  entryDbTable ??= mockEntryDbTable();

  prepareDbTransaction(db, context, txn ?? MockTransaction());

  whenQuery<SyncedProjectIdsRepositoryQuery, List<Project>>(
    db,
    (_) => localProjects ?? <Project>[],
  );

  whenQuery<OrganizationIdsRepositoryQuery, List<Organization>>(
    db,
    (_) => localOrganizations ?? <Organization>[],
  );
  whenQuery<OrganizationIdRepositoryQuery, Organization?>(
    db,
    (_) => const Organization(id: LocalId(1)),
  );

  when(() => db.organization).thenReturn(orgDbTable);
  when(() => db.user).thenReturn(userDbTable);
  when(() => db.project).thenReturn(projectDbTable);
  when(() => db.entry).thenReturn(entryDbTable);
  return db;
}

ApiHelper _mockApiHelper({bool throws = false}) {
  final mock = MockApiHelper();
  prepareApiGetResponse(
    mock,
    'users/${kSyncTestUserRemoteId.dbValue}/sync',
    kMockApiSyncMultiOrgHeadResponse,
    throws: throws,
  );
  return mock;
}

ApiTranslator _mockApiTranslator() {
  final mock = MockApiTranslator();
  final userTranslator = mockUserModelTranslator(prepareToMap: false);
  final organizationTranslator =
      mockOrganizationModelTranslator(prepareToMap: false);
  final projectTranslator = mockProjectModelTranslator(prepareToMap: false);

  when(() => mock.user).thenReturn(userTranslator);
  when(() => mock.organization).thenReturn(organizationTranslator);
  when(() => mock.project).thenReturn(projectTranslator);
  return mock;
}
