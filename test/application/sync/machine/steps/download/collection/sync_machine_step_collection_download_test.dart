import 'package:bitacora/application/sync/machine/steps/download/collection/sync_machine_step_collection_download.dart';
import 'package:bitacora/dev/api_tools/mock_responses/sync_collection_responses.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_last_sync_time.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_next_page_token.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../mocktail_fallback_values.dart';
import '../../../../../api/mocks.dart';
import '../../../../resync/mocks.dart';
import '../../../mocks.dart';
import 'mocks.dart';

void main() {
  group('$SyncMachineStepCollectionDownload tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Queries one page', () async {
      final apiHelper = mockApiHelper();
      final response = kMockResponseSyncCollectionResourcesSingle.data!;
      prepareApiPostResponse(
        apiHelper,
        'sync',
        response,
      );
      final downloaders = [
        mockSyncCollectionDownloader([
          SyncMetadata(collectionType: SyncMetadataCollectionType.resource),
        ]),
      ];
      final step = SyncMachineStepCollectionDownload(
          syncMachineParams(apiHelper: apiHelper), 'test', downloaders);

      await step.sync();

      verify(
        () => downloaders[0].download(
          response['resources'],
          SyncMetadataLastSyncTime(
              getDateTimeFromApiEpoch(response['sync_time'])),
        ),
      );

      verify(() => downloaders[0].resolvePendingRelations());
    });

    test('Queries multiple pages for single collection', () async {
      final apiHelper = mockApiHelper();
      final params = syncMachineParams(apiHelper: apiHelper);
      final responses = [
        kMockResponseSyncCollectionDownload1_1.data,
        kMockResponseSyncCollectionDownload1_2.data
      ];
      final downloaders = [
        mockSyncCollectionDownloader([
          SyncMetadata(collectionType: SyncMetadataCollectionType.resource),
          SyncMetadata(
            collectionType: SyncMetadataCollectionType.resource,
            nextPageToken: SyncMetadataNextPageToken(
              responses[0]['resources']['next_page_token'],
            ),
          ),
          SyncMetadata(collectionType: SyncMetadataCollectionType.resource),
        ]),
      ];
      prepareApiPostResponse(
        apiHelper,
        'sync',
        responses[0],
        data: {
          'organization_id': params.organization.remoteId!.apiValue,
          'sync_items': {'resources': {}}
        },
      );
      prepareApiPostResponse(
        apiHelper,
        'sync',
        responses[1],
        data: {
          'organization_id': params.organization.remoteId!.apiValue,
          'sync_items': {
            'resources': {'next_page_token': '1687882819.708059000'}
          }
        },
      );
      final step =
          SyncMachineStepCollectionDownload(params, 'test', downloaders);

      await step.sync();

      for (final response in responses) {
        verify(
          () => downloaders[0].download(
            response['resources'],
            SyncMetadataLastSyncTime(
                getDateTimeFromApiEpoch(response['sync_time'])),
          ),
        );
      }

      verify(() => downloaders[0].resolvePendingRelations());
    });

    test('Queries multiple pages for multiple collections', () async {
      final apiHelper = mockApiHelper();
      final params = syncMachineParams(apiHelper: apiHelper);
      final collectionTypes = [
        SyncMetadataCollectionType.resourceCategory,
        SyncMetadataCollectionType.resourceAggregation
      ];
      final responses = [
        kMockResponseSyncCollectionDownload2_1.data,
        kMockResponseSyncCollectionDownload2_2.data,
        kMockResponseSyncCollectionDownload2_3.data,
      ];
      final downloaders = collectionTypes
          .map(
            (type) => mockSyncCollectionDownloader(
              [
                SyncMetadata(collectionType: type),
                ...responses.map(
                  (e) => SyncMetadata(
                    collectionType: type,
                    nextPageToken: SyncMetadataNextPageToken(
                      e[type.apiValue]['next_page_token'],
                    ),
                  ),
                ),
              ],
            ),
          )
          .toList();
      for (var i = 0; i < responses.length; i++) {
        prepareApiPostResponse(
          apiHelper,
          'sync',
          responses[i],
          data: {
            'organization_id': params.organization.remoteId!.apiValue,
            'sync_items': {
              for (var e in collectionTypes)
                e.apiValue: {
                  if (i > 0 &&
                      responses[i - 1][e.apiValue]['next_page_token'] != null)
                    'next_page_token': responses[i - 1][e.apiValue]
                        ['next_page_token'],
                }
            },
          },
        );
      }
      final step =
          SyncMachineStepCollectionDownload(params, 'test', downloaders);

      await step.sync();

      for (final response in responses) {
        for (var i = 0; i < 2; i++) {
          verify(
            () => downloaders[i].download(
              response[collectionTypes[i].apiValue],
              SyncMetadataLastSyncTime(
                  getDateTimeFromApiEpoch(response['sync_time'])),
            ),
          );
        }
      }

      for (var i = 0; i < 2; i++) {
        verify(() => downloaders[i].resolvePendingRelations());
      }
    });

    test('Short circuits repeated page', () async {
      final apiHelper = mockApiHelper();
      prepareApiPostResponse(
        apiHelper,
        'sync',
        kMockResponseSyncCollectionResourcesSingle.data,
      );
      final downloader = mockSyncCollectionDownloader([
        SyncMetadata(collectionType: SyncMetadataCollectionType.resource),
        SyncMetadata(
          collectionType: SyncMetadataCollectionType.resource,
          nextPageToken: SyncMetadataNextPageToken('1'),
        ),
        SyncMetadata(
          collectionType: SyncMetadataCollectionType.resource,
          nextPageToken: SyncMetadataNextPageToken('1'),
        ),
      ]);
      final step = SyncMachineStepCollectionDownload(
          syncMachineParams(apiHelper: apiHelper), 'test', [downloader]);

      expect(
        () async {
          await step.sync();
        },
        throwsA('Infinite loop detected resources: 1'),
      );
    });
  });
}
