import 'package:bitacora/application/sync/machine/steps/cleanup/sync_machine_step_outgoing_mutations_cleanup.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../mocks.dart';

void main() {
  group('$SyncMachineStepOutgoingMutationsCleanup tests', () {
    test('Has correct debug name', () {
      final params = mockSyncMachineParams();
      final step = SyncMachineStepOutgoingMutationsCleanup(params);

      expect(step.debugName, 'outgoing-mutations-cleanup');
    });

    test('Can be instantiated', () {
      final params = mockSyncMachineParams();
      final step = SyncMachineStepOutgoingMutationsCleanup(params);

      expect(step, isA<SyncMachineStepOutgoingMutationsCleanup>());
    });
  });
}
