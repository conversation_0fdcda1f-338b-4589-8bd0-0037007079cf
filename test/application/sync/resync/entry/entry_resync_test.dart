import 'package:bitacora/application/sync/resync/entry/entry_resync.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../infrastructure/entry/mocks.dart';
import '../../../../mocktail_fallback_values.dart';
import '../mocks.dart';

void main() {
  group('$EntryResync tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });
    test('Resync worklog', () async {
      final entry = mockEntry(extension: mockWorklog(), withRemoteId: true);
      final entryTable = mockEntryDbTable();
      final db = mockRepository(entryTable: entryTable);
      final apiHelper = mockApiHelper();
      final translator = mockApiTranslator(translated: entry);
      final entryResync = EntryResyncInjector().get(db, api<PERSON>elper, translator);

      await entryResync.resyncEntry(entry);

      final endPoint =
          verify(() => apiHelper.get<Map<String, dynamic>>(captureAny()))
              .captured
              .first;
      expect(endPoint.split('/').first, 'worklogs');
      verify(() => translator.updateFromMap(any(), any())).called(1);
      verify(() => entryTable.save(any(), any())).called(1);
    });

    test('Resync inventorylog', () async {
      final entry =
          mockEntry(extension: mockInventoryLog(), withRemoteId: true);
      final entryTable = mockEntryDbTable();
      final db = mockRepository(entryTable: entryTable);
      final apiHelper = mockApiHelper();
      final translator = mockApiTranslator(translated: entry);
      final entryResync = EntryResyncInjector().get(db, apiHelper, translator);

      await entryResync.resyncEntry(entry);

      final endPoint =
          verify(() => apiHelper.get<Map<String, dynamic>>(captureAny()))
              .captured
              .first;
      expect(endPoint.split('/').first, 'inventorylogs');
      verify(() => translator.updateFromMap(any(), any())).called(1);
      verify(() => entryTable.save(any(), any())).called(1);
    });

    test('Resync personnellog', () async {
      final entry =
          mockEntry(extension: mockPersonnellog(), withRemoteId: true);
      final entryTable = mockEntryDbTable();
      final db = mockRepository(entryTable: entryTable);
      final apiHelper = mockApiHelper();
      final translator = mockApiTranslator(translated: entry);
      final entryResync = EntryResyncInjector().get(db, apiHelper, translator);

      await entryResync.resyncEntry(entry);

      final endPoint =
          verify(() => apiHelper.get<Map<String, dynamic>>(captureAny()))
              .captured
              .first;
      expect(endPoint.split('/').first, 'personnellogs');
      verify(() => translator.updateFromMap(any(), any())).called(1);
      verify(() => entryTable.save(any(), any())).called(1);
    });

    test('Resync progresslog', () async {
      final entry = mockEntry(extension: mockProgresslog(), withRemoteId: true);
      final entryTable = mockEntryDbTable();
      final db = mockRepository(entryTable: entryTable);
      final apiHelper = mockApiHelper();
      final translator = mockApiTranslator(translated: entry);
      final entryResync = EntryResyncInjector().get(db, apiHelper, translator);

      await entryResync.resyncEntry(entry);

      final endPoint =
          verify(() => apiHelper.get<Map<String, dynamic>>(captureAny()))
              .captured
              .first;
      expect(endPoint.split('/').first, 'progresslogs');
      verify(() => translator.updateFromMap(any(), any())).called(1);
      verify(() => entryTable.save(any(), any())).called(1);
    });
  });
}
