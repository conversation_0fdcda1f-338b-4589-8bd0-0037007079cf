import 'dart:async';

import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/sync/background/background_sync_future_scheduler.dart';
import 'package:bitacora/application/sync/machine/sync_machine.dart';
import 'package:bitacora/application/sync/machine/sync_machine_params.dart';
import 'package:bitacora/application/sync/pending_attachments_upload_repository_query.dart';
import 'package:bitacora/application/sync/pending_mutations_upload_repository_query.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/application/sync/sync_utils.dart';
import 'package:bitacora/application/sync/sync_widget.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry_repository.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/attachment/s3_syncer/attachment_s3_syncer.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/connectivity/connectivity.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../analytics/mocks.dart';
import '../../domain/auth/mocks.dart';
import '../../domain/common/mocks.dart';
import '../../domain/entry/mocks.dart';
import '../../domain/outgoing_mutation/mocks.dart';
import '../../infrastructure/attachment/mocks.dart';
import '../../infrastructure/mocks.dart';
import '../../mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../../test_util.dart';
import '../../util/connectivity/mocks.dart';
import '../api/mocks.dart';
import '../cache/auth/mocks.dart';
import '../cache/organization/mocks.dart';
import 'background/mocks.dart';
import 'machine/mocks.dart';
import 'mocks.dart';

BuildContext? context;

void main() {
  group('$SyncWidget tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Outgoing mutation triggers sync with db source',
        (tester) async {
      final syncTrigger = mockSyncTrigger();

      await withInjected3<AttachmentS3Syncer, SyncTrigger, SyncUtils>(
        mockAttachmentS3Syncer(),
        syncTrigger,
        mockSyncUtils(),
        () async {
          final controller =
              StreamController<Mutation<OutgoingMutation>>.broadcast();
          await tester.pumpWidget(_testApp(
            activeSession: mockActiveSession(),
            db: _mockRepository(controller: controller),
          ));

          controller.sink.add(Mutation(type: MutationType.insert));

          await awaitUntilStopsThrowing(() {
            final event = verify(() => syncTrigger.trigger(captureAny()))
                .captured
                .first as SyncTriggerEvent;
            expect(event.source, SyncTriggerSource.db);
          });
        },
      );
    });

    testWidgets('Gaining connectivity triggers sync with db source',
        (tester) async {
      final syncTrigger = mockSyncTrigger();
      final connectivityNotifier = ValueNotifier([ConnectivityResult.none]);
      final connectivity = mockConnectivity(notifier: connectivityNotifier);

      await withInjected4<AttachmentS3Syncer, SyncTrigger, Connectivity,
          SyncUtils>(
        mockAttachmentS3Syncer(),
        syncTrigger,
        connectivity,
        mockSyncUtils(),
        () async {
          await tester.pumpWidget(_testApp(
            db: _mockRepository(entryRepository: mockEntryRepository()),
            activeSession: mockActiveSession(session: mockSession()),
          ));
          await awaitUntilStopsThrowing(() {
            final event = verify(() => syncTrigger.trigger(captureAny()))
                .captured
                .first as SyncTriggerEvent;
            expect(event.source, SyncTriggerSource.session);
          });

          when(() => connectivity.hasConnectivity()).thenReturn(true);
          connectivityNotifier.value = [ConnectivityResult.wifi];

          await awaitUntilStopsThrowing(() {
            final event = verify(() => syncTrigger.trigger(captureAny()))
                .captured
                .first as SyncTriggerEvent;
            expect(event.source, SyncTriggerSource.connectivity);
          });
        },
      );
    });

    testWidgets('Cleans up on dispose', (tester) async {
      final connectivityNotifier = _mockValueNotifier();
      final connectivity = mockConnectivity(notifier: connectivityNotifier);
      final attachmentS3Syncer = mockAttachmentS3Syncer();
      final syncState = mockSyncState();
      final syncMachine = mockSyncMachine();

      await withInjected4<AttachmentS3Syncer, Connectivity, SyncState,
          SyncMachine>(
        attachmentS3Syncer,
        connectivity,
        syncState,
        syncMachine,
        () async {
          final outgoingMutationsController =
              StreamController<Mutation<OutgoingMutation>>.broadcast();
          await tester.pumpWidget(_testApp(
            db: _mockRepository(controller: outgoingMutationsController),
          ));

          Navigator.of(context!).pop();

          await tester.pumpAndSettle();
          expect(outgoingMutationsController.hasListener, false);
          _verifyNotifierAddRemove(connectivityNotifier);
          verify(() => attachmentS3Syncer.dispose());
          verify(() => syncState.dispose());
          verify(() => syncMachine.cancel());
        },
      );
    });

    testWidgets('Provides sync trigger and state', (tester) async {
      await withInjected<AttachmentS3Syncer>(
        mockAttachmentS3Syncer(),
        () async {
          await tester.pumpWidget(_testApp(activeSession: mockActiveSession()));

          context!.read<SyncTrigger>();
          context!.read<SyncState>();
        },
      );
    });

    testWidgets('Sync machine has right parameters', (tester) async {
      final syncMachineInjector = mockSyncMachineInjector();
      final syncTrigger = mockSyncTrigger();
      final syncState = mockSyncState();
      await withInjected4<AttachmentS3Syncer, SyncMachineInjector, SyncTrigger,
          SyncState>(
        mockAttachmentS3Syncer(),
        syncMachineInjector,
        syncTrigger,
        syncState,
        () async {
          final db = _mockRepository();
          final activeSession = mockActiveSession(session: mockSession());
          final organization = mockOrganization();
          final apiTranslator = MockApiTranslator();
          final apiHelper = MockApiHelper();
          await tester.pumpWidget(_testApp(
            db: db,
            activeSession: activeSession,
            activeOrganization:
                mockActiveOrganization(organization: organization),
            apiTranslator: apiTranslator,
            apiHelper: apiHelper,
          ));

          final captured = verify(() => syncMachineInjector.get(
                steps: captureAny(named: 'steps'),
                params: captureAny(named: 'params'),
                syncTrigger: captureAny(named: 'syncTrigger'),
              )).captured;
          final params = captured[1] as SyncMachineParams;
          expect(params.db, db);
          expect(params.apiHelper, apiHelper);
          expect(params.apiTranslator, apiTranslator);
          expect(params.session, activeSession.value!);
          expect(params.organization, organization);
          expect(params.syncState, syncState);
          expect(captured[2], syncTrigger);
        },
      );
    });

    testWidgets('Sync cancels when active session ends', (tester) async {
      final syncMachine = mockSyncMachine();

      await withInjected3<AttachmentS3Syncer, Connectivity, SyncMachine>(
        mockAttachmentS3Syncer(),
        mockConnectivity(),
        syncMachine,
        () async {
          final activeSession = mockActiveSession(session: mockSession());
          await tester.pumpWidget(_testApp(activeSession: activeSession));
          await tester.pumpAndSettle();

          final captured =
              verify(() => activeSession.addListener(captureAny())).captured;

          when(() => activeSession.value).thenAnswer((_) => null);
          captured[0]();
          await tester.pumpAndSettle();

          verify(() => syncMachine.cancel());
        },
      );
    });

    testWidgets('Sync restarts after active org change', (tester) async {
      final firstOrg = mockOrganization(withId: true);
      final secondOrg = mockOrganization(withId: true);
      final syncMachines = [
        mockSyncMachine(
          syncMachineParams: mockSyncMachineParams(organization: firstOrg),
        ),
        mockSyncMachine(
          syncMachineParams: mockSyncMachineParams(organization: secondOrg),
        )
      ];
      final syncMachineInjector =
          mockSyncMachineInjector(syncMachines: [...syncMachines]);
      final syncTrigger = mockSyncTrigger();

      await withInjectedN(
        {
          AttachmentS3Syncer: mockAttachmentS3Syncer(),
          Connectivity: mockConnectivity(),
          SyncMachineInjector: syncMachineInjector,
          SyncTrigger: syncTrigger,
          BackgroundSyncFutureScheduler: mockBackgroundSyncFutureScheduler(),
          SyncUtils: mockSyncUtils(),
        },
        () async {
          final activeOrganization = mockActiveOrganization(organizations: [
            firstOrg,
            secondOrg,
          ]);
          await tester
              .pumpWidget(_testApp(activeOrganization: activeOrganization));
          await tester.pumpAndSettle();
          final captured = verify(
            () => activeOrganization.addListener(captureAny()),
          ).captured;

          captured[0]();
          await tester.pumpAndSettle();

          verify(() => syncMachines[0].cancel());
          verify(() => syncMachineInjector.get(
                steps: any(named: 'steps'),
                params: any(named: 'params'),
                syncTrigger: any(named: 'syncTrigger'),
              )).called(2);
          verify(() => syncTrigger.trigger(any())).called(2);
        },
      );
    });

    testWidgets('On resume app with pending sync', (tester) async {
      SharedPreferences.setMockInitialValues({});
      final syncTrigger = mockSyncTrigger();

      await withInjectedN(
        {
          AttachmentS3Syncer: mockAttachmentS3Syncer(),
          SyncMachine: mockSyncMachine(),
          SyncTrigger: syncTrigger,
          BackgroundSyncFutureScheduler: mockBackgroundSyncFutureScheduler(),
          SyncUtils: mockSyncUtils(),
        },
        () async {
          await tester.pumpWidget(_testApp());

          tester.binding
              .handleAppLifecycleStateChanged(AppLifecycleState.resumed);

          await tester.pumpAndSettle();
        },
      );

      await awaitUntilStopsThrowing(
          () => verify(() => syncTrigger.trigger(any())).called(2));
    });

    testWidgets('On resume app with dirty db', (tester) async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.hasDirtyDb: true,
      });
      final db = _mockRepository();
      await withInjected4<AttachmentS3Syncer, SyncMachine, SyncUtils,
          BackgroundSyncFutureScheduler>(
        mockAttachmentS3Syncer(),
        mockSyncMachine(),
        mockSyncUtils(),
        mockBackgroundSyncFutureScheduler(),
        () async {
          await tester.pumpWidget(_testApp(
            db: db,
          ));

          tester.binding
              .handleAppLifecycleStateChanged(AppLifecycleState.resumed);
        },
      );

      final prefs = await SharedPreferences.getInstance();
      await awaitUntil(() {
        prefs.reload();
        return prefs.getBool(SharedPreferencesKeys.hasDirtyDb) == false;
      });
      verify(() => db.markDirty()).called(1);
    });

    testWidgets('On resume app with dirty entry repository', (tester) async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.hasDirtyEntryRepository: true,
      });
      final entryRepository = MockEntryRepository();
      when(() => entryRepository.markDirty()).thenReturn(null);
      await withInjected4<AttachmentS3Syncer, SyncMachine, SyncUtils,
          BackgroundSyncFutureScheduler>(
        mockAttachmentS3Syncer(),
        mockSyncMachine(),
        mockSyncUtils(),
        mockBackgroundSyncFutureScheduler(),
        () async {
          await tester.pumpWidget(_testApp(
            db: _mockRepository(entryRepository: entryRepository),
          ));

          tester.binding
              .handleAppLifecycleStateChanged(AppLifecycleState.resumed);
        },
      );

      final prefs = await SharedPreferences.getInstance();
      await awaitUntil(() {
        prefs.reload();
        return prefs.getBool(SharedPreferencesKeys.hasDirtyEntryRepository) ==
            false;
      });
      verify(() => entryRepository.markDirty()).called(1);
    });

    testWidgets('On resume app with pending sync and dirty entry repository',
        (tester) async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.hasDirtyEntryRepository: true,
      });
      final entryRepository = MockEntryRepository();
      when(() => entryRepository.markDirty()).thenReturn(null);
      final syncTrigger = mockSyncTrigger();

      await withInjectedN(
        {
          AttachmentS3Syncer: mockAttachmentS3Syncer(),
          SyncMachine: mockSyncMachine(),
          SyncTrigger: syncTrigger,
          BackgroundSyncFutureScheduler: mockBackgroundSyncFutureScheduler(),
          SyncUtils: mockSyncUtils(hasPendingSync: true),
        },
        () async {
          await tester.pumpWidget(_testApp(
            db: _mockRepository(entryRepository: entryRepository),
          ));

          tester.binding
              .handleAppLifecycleStateChanged(AppLifecycleState.resumed);

          await tester.pumpAndSettle();
        },
      );

      final prefs = await SharedPreferences.getInstance();

      await awaitUntil(() {
        prefs.reload();
        return prefs.getBool(SharedPreferencesKeys.hasDirtyEntryRepository) ==
            false;
      });
      verify(() => entryRepository.markDirty()).called(1);
      verify(() => syncTrigger.trigger(any())).called(2);
    });

    testWidgets('Disposing widget removes WidgetsBindingObserver',
        (tester) async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.hasPendingSync: true,
      });
      final syncTrigger = mockSyncTrigger();

      await withInjected3<AttachmentS3Syncer, SyncMachine, SyncTrigger>(
        mockAttachmentS3Syncer(),
        mockSyncMachine(),
        syncTrigger,
        () async {
          await tester.pumpWidget(_testApp());
          Navigator.of(context!).pop();
          await tester.pumpAndSettle();

          tester.binding
              .handleAppLifecycleStateChanged(AppLifecycleState.resumed);

          await tester.pumpAndSettle();
        },
      );

      verify(() => syncTrigger.trigger(any())).called(1);
    });
  });
}

void _verifyNotifierAddRemove(
    MockValueNotifier<List<ConnectivityResult>> connectivityNotifier) {
  final added =
      verify(() => connectivityNotifier.addListener(captureAny())).captured[0];
  final removed =
      verify(() => connectivityNotifier.removeListener(captureAny()))
          .captured[0];
  expect(added, removed);
}

MockValueNotifier<List<ConnectivityResult>> _mockValueNotifier() {
  final mock = MockValueNotifier<List<ConnectivityResult>>();
  when(() => mock.addListener(any())).thenReturn(null);
  when(() => mock.removeListener(any())).thenReturn(null);
  when(() => mock.value).thenReturn([]);
  return mock;
}

Widget _testApp({
  Repository? db,
  ApiTranslator? apiTranslator,
  ApiHelper? apiHelper,
  ActiveSession? activeSession,
  ActiveOrganization? activeOrganization,
}) {
  return MultiProvider(
    providers: [
      Provider<AnalyticsLogger>(create: (_) => MockAnalyticsLogger()),
      Provider<Repository>(create: (_) => db ?? _mockRepository()),
      Provider<ApiTranslator>(
          create: (_) => apiTranslator ?? MockApiTranslator()),
      Provider<ApiHelper>(create: (_) => apiHelper ?? MockApiHelper()),
      ChangeNotifierProvider<AuthRepository>.value(value: mockAuthRepository()),
      ChangeNotifierProvider<ActiveOrganization>(
          create: (_) =>
              activeOrganization ??
              mockActiveOrganization(organization: mockOrganization())),
      ChangeNotifierProvider<ActiveSession>(
          create: (context) =>
              activeSession ?? mockActiveSession(session: mockSession())),
    ],
    child: MaterialApp(
      home: SyncWidget(
        child: Builder(builder: (c) {
          context = c;
          return Container();
        }),
      ),
    ),
  );
}

Repository _mockRepository({
  StreamController<Mutation<OutgoingMutation>>? controller,
  EntryRepository? entryRepository,
}) {
  controller ??= StreamController<Mutation<OutgoingMutation>>.broadcast();

  final outgoingMutationDbTable = MockOutgoingMutationRepository();
  when(() => outgoingMutationDbTable.getMutations())
      .thenAnswer((invocation) => controller!.stream);

  final mock = MockRepository();
  when(() => mock.outgoingMutation).thenReturn(outgoingMutationDbTable);
  when(() => mock.entry).thenAnswer((_) => entryRepository!);
  when(() => mock.markDirty()).thenReturn(null);
  when(() => mock.query(const PendingAttachmentsUploadRepositoryQuery()))
      .thenAnswer((_) => Future.value([]));
  when(() => mock.query(const PendingMutationsUploadRepositoryQuery()))
      .thenAnswer((_) => Future.value([]));
  return mock;
}
