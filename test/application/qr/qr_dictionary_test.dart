import 'package:bitacora/application/qr/translator/qr_code_dictionary.dart';
import 'package:test/test.dart';

void main() {
  test('we do not have duplicate fields', () {
    const dictionary = QrDictionary();
    _testDictionary(dictionary.fields);

    _testDictionary(dictionary.entry.fields);
    _testDictionary(dictionary.identifier.fields);
  });
}

void _testDictionary(List<String> fields) {
  expect(fields.toSet().length, fields.length);
}
