import 'package:bitacora/application/remote_config.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFirebaseRemoteConfig extends Mock implements FirebaseRemoteConfig {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('\$RemoteConfig tests', () {
    setUp(() {
      registerFallbackValue(RemoteConfigSettings(
        fetchTimeout: const Duration(minutes: 1),
        minimumFetchInterval: const Duration(hours: 1),
      ));
    });

    test('Injects same', () {
      expect(RemoteConfig(), RemoteConfig());
    });

    test('Gets values from Firebase', () {
      final remoteConfig = RemoteConfig();
      expect(remoteConfig.getWorklogPrompt(), isA<Future<String>>());
      expect(remoteConfig.getBool('testBool'), isA<Future<bool>>());
      expect(remoteConfig.getInt('testInt'), isA<Future<int>>());
      expect(remoteConfig.getDouble('testDouble'), isA<Future<double>>());
      expect(remoteConfig.getString('testString'), isA<Future<String>>());
    });

    test('Can refresh config', () async {
      final remoteConfig = RemoteConfig();
      await remoteConfig.refresh();
    });
  });
}
