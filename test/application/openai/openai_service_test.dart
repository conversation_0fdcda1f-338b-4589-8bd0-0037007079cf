import 'dart:typed_data';

import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/openai/openai_service.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:dio/dio.dart';
import 'package:file/file.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../application/mocks.dart';
import '../../mocktail_fallback_values.dart';

class MockDio extends Mock implements Dio {}

class MockResponse<T> extends Mock implements Response<T> {}

Response<T> mockResponse<T>(T data) {
  final response = MockResponse<T>();
  when(() => response.data).thenReturn(data);
  return response;
}

class MockFile extends Mock implements File {}

void main() {
  group('OpenAiService tests', () {
    late MockDio mockDio;
    late OpenAiService service;
    late AppConfig appConfig;

    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    setUp(() {
      mockDio = MockDio();
      appConfig = mockAppConfig(openAiApiUrl: 'http://test-api-url.com');

      when(() => mockDio.options).thenReturn(BaseOptions());

      service = withInjected<AppConfig>(
        appConfig,
        () => OpenAiService(dio: mockDio),
      );
    });

    test('Constructor sets baseUrl from AppConfig', () {
      expect(mockDio.options.baseUrl, appConfig.openAiApiUrl);
    });

    group('createWorklogFromAudio', () {
      test('Successfully creates worklog from audio', () async {
        final mockFile = MockFile();
        final mockBytes = Uint8List.fromList([1, 2, 3, 4]);
        final projects = [
          {'id': 1, 'name': 'Project 1'}
        ];
        final expectedWorklog = {
          'transcription': 'This is a test transcription',
          'comments': 'This is a test comment',
          'sentiment': 'Positive',
          'keywords': 'test, worklog, audio',
          'action_item': 'Complete the test',
          'health_item': 'Take a break',
          'health_tags': 'stress, focus'
        };

        when(() => mockFile.readAsBytes()).thenAnswer((_) async => mockBytes);
        when(() => mockFile.basename).thenReturn('audio.m4a');

        when(() => mockDio.post(
              'worklog-from-audio',
              data: any(named: 'data'),
            )).thenAnswer((_) async => mockResponse(expectedWorklog));

        final result = await service.createWorklogFromAudio(mockFile, projects);

        expect(result, expectedWorklog);

        verify(() => mockDio.options.headers['Content-Type'] = 'multipart/form-data');
        verify(() => mockDio.post(
              'worklog-from-audio',
              data: any(named: 'data'),
            ));
      });

      test('Handles errors during worklog creation', () async {
        final mockFile = MockFile();
        final mockBytes = Uint8List.fromList([1, 2, 3, 4]);
        final projects = [
          {'id': 1, 'name': 'Project 1'}
        ];
        final expectedError = 'Worklog creation failed';

        when(() => mockFile.readAsBytes()).thenAnswer((_) async => mockBytes);
        when(() => mockFile.basename).thenReturn('audio.m4a');

        when(() => mockDio.post(
              'worklog-from-audio',
              data: any(named: 'data'),
            )).thenThrow(expectedError);

        expect(() => service.createWorklogFromAudio(mockFile, projects),
            throwsA(expectedError));
      });
    });
  });
}
