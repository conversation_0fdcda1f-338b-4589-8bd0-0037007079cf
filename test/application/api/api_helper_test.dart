import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/api/auth_interceptor.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/dev/api_tools/dev_api_man_in_the_middle.dart';
import 'package:bitacora/dev/api_tools/mock_man_in_the_middle.dart';
import 'package:bitacora/util/dio/mini_log_interceptor.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';

import '../../domain/auth/mocks.dart';
import '../mocks.dart';

void main() {
  group('ApiHelper tests', () {
    test('Has auth interceptor', () {
      final apiHelper = _getApiHelper();

      expect(_hasInterceptor<AuthInterceptor>(apiHelper), true);
    });

    test('Has logging interceptor when expected', () {
      withInjected<AppConfig>(
        mockAppConfig(logLevel: Level.off),
        () =>
            expect(_hasInterceptor<MiniLogInterceptor>(_getApiHelper()), true),
      );
      withInjected<AppConfig>(
        mockAppConfig(logLevel: Level.trace),
        () => expect(_hasInterceptor<LogInterceptor>(_getApiHelper()), true),
      );
      withInjected<AppConfig>(
        mockAppConfig(logLevel: Level.debug),
        () =>
            expect(_hasInterceptor<MiniLogInterceptor>(_getApiHelper()), true),
      );
    });

    test('Has MITM interceptor conditionally', () {
      expect(_hasInterceptor<DevApiManInTheMiddle>(_getApiHelper()), false);
      expect(_hasInterceptor<MockManInTheMiddle>(_getApiHelper()), false);
      withInjected<AppConfig>(
        mockAppConfig(isDevToolsEnabled: true),
        () => expect(
            _hasInterceptor<DevApiManInTheMiddle>(_getApiHelper()), true),
      );
      withInjected<AppConfig>(
        mockAppConfig(isIntegrationTest: true),
        () =>
            expect(_hasInterceptor<MockManInTheMiddle>(_getApiHelper()), true),
      );
    });
  });
}

ApiHelper _getApiHelper() {
  return ApiHelperInjector().get(
    authRepository: MockAuthRepository(),
  );
}

bool _hasInterceptor<T>(ApiHelper apiHelper) {
  for (var interceptor in apiHelper.interceptors) {
    if (interceptor is T) {
      return true;
    }
  }
  return false;
}
