import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:mocktail/mocktail.dart';

class MockOrganizationCache extends Mock implements OrganizationCache {}

class MockActiveOrganization extends Mock implements ActiveOrganization {}

OrganizationCache mockOrganizationCache({
  List<List<Organization>?>? organizationsList,
  List<Organization>? organizations,
  bool hasLoaded = true,
  bool isLoading = false,
}) {
  final mock = MockOrganizationCache();
  when(() => mock.value).thenAnswer((_) =>
      organizations ??
      (organizationsList != null
          ? (organizationsList.isEmpty ? null : organizationsList.removeAt(0))
          : <Organization>[]));
  when(() => mock.hasLoaded).thenReturn(hasLoaded);
  when(() => mock.isLoading).thenReturn(isLoading);
  when(() => mock.load()).thenAnswer((_) => Future.value());
  return mock;
}

ActiveOrganization mockActiveOrganization({
  Organization? organization,
  List<Organization?>? organizations,
  bool hasLoaded = true,
  bool isLoading = false,
}) {
  final mock = MockActiveOrganization();
  when(() => mock.value).thenAnswer((_) =>
      organization ??
      (organizations != null
          ? (organizations.isEmpty ? null : organizations.removeAt(0))
          : null));
  when(() => mock.set(any())).thenAnswer((_) => Future.value());
  when(() => mock.hasLoaded).thenReturn(hasLoaded);
  when(() => mock.isLoading).thenReturn(isLoading);
  when(() => mock.load()).thenAnswer((_) => Future.value());
  return mock;
}
