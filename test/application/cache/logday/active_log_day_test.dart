import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/domain/common/value_object/log_day.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test/test.dart';

void main() {
  group('$ActiveLogDay tests', () {
    final activeLogDay = ActiveLogDay();

    test('get activeLogDay without shared preferences', () async {
      SharedPreferences.setMockInitialValues({});

      await activeLogDay.load();

      expect(activeLogDay.value!.value, getLogDayForToday());
    });

    test('get activeLogDay from shared preferences', () async {
      const activeLogDayValue = 2;
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.activeLogDay: activeLogDayValue,
      });

      await activeLogDay.load();

      expect(activeLogDay.value!.value, activeLogDayValue);
    });

    test('set activeLogDay', () async {
      SharedPreferences.setMockInitialValues({});
      await activeLogDay.load();
      expect(activeLogDay.value!.value, getLogDayForToday());

      await activeLogDay.set(const LogDay(4));

      expect(activeLogDay.value!.value, 4);
    });
  });
}
