import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/domain/common/value_object/log_day.dart';
import 'package:mocktail/mocktail.dart';

class MockActiveLogDay extends Mock implements ActiveLogDay {}

ActiveLogDay mockActiveLogDay({
  LogDay? logDay,
  bool hasLoaded = true,
  bool isLoading = false,
}) {
  final mock = MockActiveLogDay();
  when(() => mock.value).thenAnswer((_) => logDay);
  when(() => mock.hasLoaded).thenReturn(hasLoaded);
  when(() => mock.isLoading).thenReturn(isLoading);
  return mock;
}
