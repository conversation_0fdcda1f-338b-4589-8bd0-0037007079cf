import 'package:bitacora/application/remote_config.dart';
import 'package:mocktail/mocktail.dart';

class MockRemoteConfig extends Mock implements RemoteConfig {}

RemoteConfig mockRemoteConfig() {
  final mock = MockRemoteConfig();
  when(() => mock.getWorklogPrompt()).thenAnswer((_) => Future.value(''));
  when(() => mock.getBool(any())).thenAnswer((_) => Future.value(false));
  when(() => mock.getInt(any())).thenAnswer((_) => Future.value(0));
  when(() => mock.getDouble(any())).thenAnswer((_) => Future.value(0.0));
  when(() => mock.getString(any())).thenAnswer((_) => Future.value(''));
  when(() => mock.refresh()).thenAnswer((_) => Future.value());
  return mock;
}
