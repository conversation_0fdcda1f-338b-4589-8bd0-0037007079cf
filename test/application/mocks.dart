import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/auth_app_service.dart';
import 'package:bitacora/application/nuke.dart';
import 'package:bitacora/application/restart.dart';
import 'package:bitacora/application/support/app_support.dart';
import 'package:logger/logger.dart';
import 'package:mocktail/mocktail.dart';

class MockNuke extends Mock implements Nuke {}

class MockRestart extends Mock implements Restart {}

class MockAppAuthService extends Mock implements AuthAppService {}

class MockAppSupportInjector extends Mock implements AppSupportInjector {}

Nuke mockNuke() {
  final mock = MockNuke();
  when(() => mock.nuke(any())).thenAnswer((_) => Future.value());
  return mock;
}

Restart mockRestart() {
  final mock = MockRestart();
  when(() => mock.restart(any())).thenReturn(null);
  return mock;
}

AppConfig mockAppConfig({
  bool isTest = true,
  bool isIntegrationTest = false,
  String appName = 'Larvacora',
  ApiMode apiMode = ApiMode.prod,
  String webAppUrl = 'https://app.bitacora.io',
  String apiBaseUrl = 'https://app.bitacora.io/api/',
  String trackingApiUrl = 'https://gps-tracking.bitacora.io',
  String openAiApiUrl = 'http://localhost:3006',
  bool isDevToolsEnabled = false,
  bool shouldShowPackageInfo = false,
  bool isReportsEnabled = false,
  bool isHybridReportsEnabled = false,
  Level logLevel = Level.debug,
  bool isBackgroundSyncEnabled = false,
  bool isGhostEntriesEnabled = false,
  bool isSpeechToTextEnabled = false,
  bool isInventoryLogTypeEditable = false,
  youtubeChannelId = 'UCVOhZR1hroD0fDIchyuVRfg',
  whatsAppContactNumber = '+528184487740',
  emailContactAddress = '<EMAIL>',
}) {
  return AppConfig.fromParams(
    isTest: isTest,
    isIntegrationTest: isIntegrationTest,
    appName: appName,
    targetName: 'dev4ios',
    apiMode: apiMode,
    webAppUrl: webAppUrl,
    apiBaseUrl: apiBaseUrl,
    locationTrackingApiUrl: trackingApiUrl,
    openAiApiUrl: openAiApiUrl,
    isDevToolsEnabled: isDevToolsEnabled,
    shouldShowPackageInfo: shouldShowPackageInfo,
    logLevel: logLevel,
    isBackgroundSyncEnabled: isBackgroundSyncEnabled,
    isGhostEntriesEnabled: isGhostEntriesEnabled,
    isReportsEnabled: isReportsEnabled,
    isHybridReportsEnabled: isHybridReportsEnabled,
    isSpeechToTextInAudioRecorderEnabled: isSpeechToTextEnabled,
    isInventorylogTypeEditable: isInventoryLogTypeEditable,
    youtubeChannelId: youtubeChannelId,
    whatsAppContactNumber: whatsAppContactNumber,
    emailContactAddress: emailContactAddress,
    devLoginHackUserPass: '',
  );
}

AuthAppService mockAuthAppService() {
  final mock = MockAppAuthService();
  when(() => mock.logout(any())).thenAnswer((_) => Future.value());
  return mock;
}

AppSupportInjector mockAppSupportInjector() {
  final mock = MockAppSupportInjector();
  final appSupport = AppSupport.fromMap({'whatsapp': '+528184487740'});

  when(() => mock.get()).thenAnswer((_) => Future.value(appSupport));

  return mock;
}
