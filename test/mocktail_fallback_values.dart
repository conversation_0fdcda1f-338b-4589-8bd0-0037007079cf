import 'dart:io';
import 'dart:typed_data';

import 'package:amplify_flutter/amplify_flutter.dart' as amp;
import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/application/sync/machine/dedup/sync_entry_dedup_by_created_at_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/entry/has_existing_outgoing_mutation_for_downloaded_entry_repository_query.dart';
import 'package:bitacora/dev/db_tools/sync_download_project_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/upload/sync_outgoing_mutation_repository_query.dart';
import 'package:bitacora/application/sync/pending_attachments_upload_repository_query.dart';
import 'package:bitacora/application/sync/pending_mutations_upload_repository_query.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/auth/auth_session_user_repository_query.dart';
import 'package:bitacora/domain/common/query/entry_common_db_queries.dart';
import 'package:bitacora/domain/common/query/organization_common_repository_queries.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_last_sync_time.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/domain/user_invite/user_invite.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/attachment/attachment_sync_repository_query.dart';
import 'package:bitacora/infrastructure/attachment/pending_attachment_upload_repository_query.dart';
import 'package:bitacora/infrastructure/db_lock.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_repository_query.dart';
import 'package:bitacora/presentation/daylog/entry_timer/find_entry_with_timer_repository_query.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_load_repository_query.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/options/tags/tag_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/templatelog/user_by_email_repository_query.dart';
import 'package:bitacora/presentation/entry_form/type_selector/entry_form_page_type.dart';
import 'package:bitacora/presentation/organization/staff/user_invite_for_send_repository_query.dart';
import 'package:bitacora/presentation/user_settings/has_pending_outgoing_mutations_repository_query.dart';
import 'package:bitacora/util/access/access_for_organization_repository_query.dart';
import 'package:bitacora/util/access/find_permission_repository_query.dart';
import 'package:bitacora/util/background_work/background_provider.dart';
import 'package:bitacora/util/camera/camera_utils.dart';
import 'package:bitacora/util/restart_widget.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:bitacora/util/suggestion_typeahead/search_multi_pattern_repository_query.dart';
import 'package:bitacora/util/workmanager/workmanager_utils.dart';
import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image/image.dart' as img;
import 'package:logger/logger.dart';
import 'package:mocktail/mocktail.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:workmanager/workmanager.dart';

import 'analytics/mocks.dart';
import 'application/api/mocks.dart';
import 'application/cache/auth/mocks.dart';
import 'application/cache/organization/mocks.dart';
import 'application/cache/project/mocks.dart';
import 'application/sync/machine/mocks.dart';
import 'domain/auth/mocks.dart';
import 'domain/common/mocks.dart';
import 'domain/entry/mocks.dart';
import 'infrastructure/attachment/mocks.dart';
import 'infrastructure/mocks.dart';
import 'mocks.dart';
import 'presentation/user_settings/mocks.dart';
import 'util/camera/mocks.dart';
import 'util/entry/mocks.dart';
import 'util/file_system/mocks.dart';
import 'util/image/mocks.dart';
import 'util/mocks.dart';
import 'util/workmanager/mocks.dart';

class MocktailFallbackValues {
  static bool _isInitialized = false;

  static void ensureInitialized() {
    resetMocksRandomSeed();

    if (_isInitialized) {
      return;
    }
    _isInitialized = true;

    registerFallbackValue(AccessForOrganizationRepositoryQuery(
        organization: const Organization()));
    registerFallbackValue(const amp.StoragePath.fromString('1'));
    registerFallbackValue(AnalyticsEvent.login);
    registerFallbackValue(ApiTranslator());
    registerFallbackValue(const Attachment());
    registerFallbackValue(AttachmentSyncRepositoryQuery(id: const LocalId(1)));
    registerFallbackValue(const AttachmentPath('relative/path'));
    registerFallbackValue(AuthSessionUserRepositoryQuery(id: const LocalId(1)));
    registerFallbackValue(BackgroundContext([]));
    registerFallbackValue(CompressFormat.jpeg);
    registerFallbackValue(DbLockKey.foreground);
    registerFallbackValue(const Duration());
    registerFallbackValue(const Entry());
    registerFallbackValue(const EntryFormProps());
    registerFallbackValue(EntryFormPageType.personnellog());
    registerFallbackValue(
        const EntryFormPageLoadRepositoryQuery(entryId: LocalId(1)));
    registerFallbackValue(const EntryIdRepositoryQuery(remoteId: RemoteId(0)));

    registerFallbackValue(ExistingWorkPolicy.keep);
    registerFallbackValue(ExtensionType.worklog);
    registerFallbackValue(File('/'));
    registerFallbackValue(const FindEntryWithStartedTimerRepositoryQuery());
    registerFallbackValue(FlashMode.auto);
    registerFallbackValue(FindAccessPermissionRepositoryQuery(
        organization: const Organization(), permission: 1));
    registerFallbackValue(const HasPendingOutgoingMutationsRepositoryQuery(
        ignoreRetryLimit: true));
    registerFallbackValue(
        HasExistingOutgoingMutationForDownloadedEntry(entry: const Entry()));
    registerFallbackValue(ImageFormatGroup.jpeg);
    registerFallbackValue(img.Interpolation.nearest);
    registerFallbackValue(const Key(''));
    registerFallbackValue(LaunchMode.platformDefault);
    registerFallbackValue(Level.debug);
    registerFallbackValue(const LocalId(9998));
    registerFallbackValue(const LocationTracking());
    registerFallbackValue(const LogDay(19962901));
    registerFallbackValue(const LogListEntriesRepositoryQuery());
    registerFallbackValue(MockActiveSession());
    registerFallbackValue(MockActiveProject());
    registerFallbackValue(MockAnalyticsLogger());
    registerFallbackValue(MockApiHelper());
    registerFallbackValue(MockAuthRepository());
    registerFallbackValue(MockAWSFile());
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(mockCameraDescription());
    registerFallbackValue(MockDbContext());
    registerFallbackValue(MockEntryApiTranslator());
    registerFallbackValue(MockEntryUrlUtilContextSnapshot());
    registerFallbackValue(MockFile());
    registerFallbackValue(MockImage());
    registerFallbackValue(MockQueryScope());
    registerFallbackValue(MockRepository());
    registerFallbackValue(MockRepositoryQuery());
    registerFallbackValue(MockRepositoryQueryContext());
    registerFallbackValue(mockSession());
    registerFallbackValue(MockState<RestartWidget>());
    registerFallbackValue(MockSyncMachineParams());
    registerFallbackValue(MockNavigatorUtilContextSnapshot());
    registerFallbackValue(MockNukeContextSnapshot());
    registerFallbackValue(NavigatorState());
    registerFallbackValue(const Organization());
    registerFallbackValue(const OutgoingMutation());
    registerFallbackValue(const OrganizationIdsRepositoryQuery());
    registerFallbackValue(
        const OrganizationIdRepositoryQuery(remoteId: RemoteId(1)));
    registerFallbackValue(const PendingAttachmentUploadRepositoryQuery());
    registerFallbackValue(const PendingAttachmentsUploadRepositoryQuery());
    registerFallbackValue(const PendingMutationsUploadRepositoryQuery());
    registerFallbackValue(const Project());
    registerFallbackValue(const ProjectByNameRepositoryQuery(ProjectName('P')));
    registerFallbackValue(const ProjectIdsRepositoryQuery(remoteIds: []));
    registerFallbackValue(RecoverSessionLauncher());
    registerFallbackValue(const RemoteId(9999));
    registerFallbackValue(const RemoteMessage());
    registerFallbackValue(ResolutionPreset.max);
    registerFallbackValue(RestartWidgetState());
    registerFallbackValue(RequestOptions(path: ''));
    registerFallbackValue(StorageSubdirectory.attachments);
    registerFallbackValue(
        SyncDownloadProjectsRepositoryQuery(organizationId: const LocalId(1)));
    registerFallbackValue(SyncEntryDedupByCreatedAtRepositoryQuery(
        createdAt: EntryCreatedAt(DateTime.now())));
    registerFallbackValue(
        const SyncedProjectIdsRepositoryQuery(orgId: LocalId(1)));
    registerFallbackValue(SyncMetadataLastSyncTime(null));
    registerFallbackValue(const SyncOutgoingMutationRepositoryQuery());
    registerFallbackValue(const SearchMultiPatternRepositoryQuery(
        subQuery: TagSuggestionRepositoryQuery([]), patterns: []));
    registerFallbackValue(
        SyncState(MockRepository(), MockActiveOrganization()));
    registerFallbackValue(SyncTrigger());
    registerFallbackValue(const SyncTriggerEvent(SyncTriggerSource.session));
    registerFallbackValue(SyncTriggerMode.normal);
    registerFallbackValue(SyncTriggerSource.session);
    registerFallbackValue(const Tag());
    registerFallbackValue(TestWorkmanagerRunnable());
    registerFallbackValue(Uint8List(0));
    registerFallbackValue(UserByEmailRepositoryQuery(email: ''));
    registerFallbackValue(Uri());
    registerFallbackValue(const User());
    registerFallbackValue(const UserInvite());
    registerFallbackValue(
        const UserInviteToSendRepositoryQuery(id: LocalId(1)));
    registerFallbackValue(WorkmanagerTask.backgroundSync);
  }
}
