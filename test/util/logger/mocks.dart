import 'package:bitacora/util/logger/logger.dart';
import 'package:logger/logger.dart';
import 'package:mocktail/mocktail.dart';

class MockLogger extends Mock implements Logger {}

class MockLoggerUtils extends Mock implements LoggerUtils {}

class MockLoggerInjector extends Mock implements LoggerInjector {}

Logger mockLogger() {
  final mock = MockLogger();
  when(() => mock.log(any(), any())).thenReturn(null);
  return mock;
}

LoggerInjector mockLoggerInjector({Logger? logger}) {
  final mock = MockLoggerInjector();
  when(
    () => mock.get(
      filter: any(named: 'filter'),
      printer: any(named: 'printer'),
      output: any(named: 'output'),
      level: any(named: 'level'),
    ),
  ).thenReturn(logger ?? MockLogger());
  return mock;
}
