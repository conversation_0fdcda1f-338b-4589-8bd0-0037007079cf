import 'package:bitacora/util/future/future.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/throttler.dart';
import 'package:flutter_test/flutter_test.dart';

import '../mocktail_fallback_values.dart';
import '../test_util.dart';
import 'future/mocks.dart';

void main() {
  group('$Throttler tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Injects same $ThrottlerInjector', () {
      expect(ThrottlerInjector(), ThrottlerInjector());
    });

    test('Injects $Throttler with expected duration', () {
      const period = Duration(seconds: 3);
      expect(
        ThrottlerInjector().get(throttlePeriod: period).throttlePeriod,
        period,
      );
    });

    test('Throttles', () {
      final throttler = ThrottlerInjector().get();
      final futureInjector = mockFutureInjector();
      var callCount = 0;
      f() => ++callCount;
      withInjected<FutureInjector>(futureInjector, () async {
        throttler.add(f);
        throttler.add(f);
        throttler.add(f);
        throttler.add(f);

        expect(callCount, 1);
        expect(throttler.isWaiting, true);
        await awaitUntil(() => !throttler.isWaiting);
        expect(callCount, 2);
      });
    });

    // FIXME: test should reset after dispatching
  });
}
