import 'package:bitacora/util/entry/entry_url_util.dart';
import 'package:bitacora/util/entry/entry_url_util_snapshot.dart';
import 'package:mocktail/mocktail.dart';

class MockEntryUrlUtil extends Mock implements EntryUrlUtil {}

class MockEntryUrlUtilContextSnapshot extends Mock
    implements EntryUrlUtilContextSnapshot {}

EntryUrlUtil mockEntryUrlUtil() {
  final mock = MockEntryUrlUtil();
  when(() => mock.maybeLaunchEntryInWebApp(any(), any()))
      .thenAnswer((_) => Future.value());
  when(() => mock.shareEntry(any(), any())).thenAnswer((_) => Future.value());
  return mock;
}
