import 'package:bitacora/util/clipboard.dart';
import 'package:bitacora/util/image/image_utils.dart';
import 'package:image/image.dart' as img;
import 'package:mocktail/mocktail.dart';

class MockImageUtils extends Mock implements ImageUtils {}

class MockImage extends Mock implements img.Image {}

ImageUtils mockImageUtils(
    {required FileSystem fileSystem, required File resultFile}) {
  final image = img.decodeImage(resultFile.readAsBytesSync());
  final mock = MockImageUtils();
  when(() => mock.encodeJpg(any(), quality: any(named: 'quality')))
      .thenReturn(img.encodeJpg(image!));
  when(() => mock.decodeImage(any())).thenReturn(image);
  when(() => mock.copyRotate(any(),
      angle: any(named: 'angle'),
      interpolation: any(named: 'interpolation'))).thenReturn(image);
  return mock;
}
