import 'package:bitacora/util/app_settings/app_settings_utils.dart';
import 'package:mocktail/mocktail.dart';

class MockAppSettingsUtils extends Mock implements AppSettingsUtils {}

AppSettingsUtils mockAppSettingsUtils() {
  final mock = MockAppSettingsUtils();
  when(() => mock.openLocationSettings()).thenAnswer((_) => Future.value());
  when(() => mock.openAppSettings()).thenAnswer((_) => Future.value());
  return mock;
}
