import 'package:bitacora/util/attachment/attachment_compression.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:test/test.dart';

import '../../mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../file_system/mocks.dart';
import 'mocks.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
    PathProviderPlatform.instance = MockPathProvider();
  });

  group('$AttachmentCompression tests', () {
    test('Injects same', () {
      expect(AttachmentCompression(), AttachmentCompression());
    });

    test('Results null on compression library error ', () async {
      final file = FileSystemInjector.get().file('path');

      final result = await withInjected<FlutterImageCompressUtil>(
        mockFlutterImageCompressUtil(throws: true),
        () => AttachmentCompression().maybeCompressImage(
          file,
          '/target/path/',
        ),
      );

      expect(null, result);
    });

    test('Results null on compression library returning null ', () async {
      final file = FileSystemInjector.get().file('path');

      final result = await withInjected<FlutterImageCompressUtil>(
        mockFlutterImageCompressUtil(returnsNull: true),
        () => AttachmentCompression().maybeCompressImage(
          file,
          '/target/path/',
        ),
      );

      expect(null, result);
    });

    test('Calls compression library with correct args ', () async {
      await _testCompression();
      await _testCompression(filename: 'file.png', format: CompressFormat.png);
    });

    test('Uses original image if compression result is larger ', () async {
      await _testCompression(sourceLength: 10, resultLength: 20);
    });
  });
}

Future<void> _testCompression({
  String filename = 'file.jpg',
  CompressFormat format = CompressFormat.jpeg,
  int sourceLength = 10,
  int resultLength = 5,
}) async {
  final file = mockFile(filename: filename, length: sourceLength);
  final filePath = file.path;
  const targetFileDir = '/target/path';
  final targetFilePath = '$targetFileDir/$filename';
  final flutterImageCompressUtil =
      mockFlutterImageCompressUtil(resultLength: resultLength);

  final result = await withInjected<FlutterImageCompressUtil>(
    flutterImageCompressUtil,
    () => AttachmentCompression().maybeCompressImage(file, targetFileDir),
  ) as File?;

  if (resultLength > sourceLength) {
    expect(result, null);
  } else {
    expect(result?.path, targetFilePath);
  }
  verify(
    () => flutterImageCompressUtil.compressAndGetFile(
      filePath,
      targetFilePath,
      minWidth: 1080,
      minHeight: 1080,
      quality: 95,
      format: format,
      keepExif: true,
    ),
  ).called(1);
}
