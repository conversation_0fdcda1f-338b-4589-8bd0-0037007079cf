import 'dart:convert';

import 'package:bitacora/analytics/bitacora_analytics_logger.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/organization_cache_repository_query.dart';
import 'package:bitacora/application/hook/repository_hook.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/infrastructure/db_lock.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/infrastructure/entry/entry_db_table.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/awesome_notifications/awesome_notifications_utils.dart';
import 'package:bitacora/util/background_work/background_work_utils.dart';
import 'package:bitacora/util/fcm/fcm_organization_topic_repository_query.dart';
import 'package:bitacora/util/fcm/fcm_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';

import '../../analytics/mocks.dart';
import '../../application/cache/auth/mocks.dart';
import '../../application/hook/mocks.dart';
import '../../infrastructure/entry/mocks.dart';
import '../../infrastructure/location_tracking/mocks.dart';
import '../../infrastructure/mocks.dart';
import '../../infrastructure/organization/mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../awesome_notifications/mocks.dart';
import '../background_work/mocks.dart';
import '../workmanager/mocks.dart';

void main() {
  group('Fcm utils tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Injects same', () {
      expect(FcmUtils(), FcmUtils());
    });

    test('Background handler initializes background work', () async {
      SharedPreferences.setMockInitialValues({});
      const org = Organization(id: LocalId(122), remoteId: RemoteId(321));
      final workmanager = mockWorkmanager();
      final payload = _payload(
        from: org.remoteId!,
        message: <String, dynamic>{'nothing': true},
      );
      final backgroundWorkUtils = mockBackgroundWorkUtils<Null>();

      await withInjectedN(
        {
          ActiveSession: _mockActiveSession(),
          Workmanager: workmanager,
          BackgroundWorkUtils: backgroundWorkUtils,
          AwesomeNotificationsUtils: mockAwesomeNotificationsUtils(),
          BitacoraAnalyticsLogger: MockBitacoraAnalyticsLogger(),
          RepositoryHook: mockRepositoryHook(),
          DbRepository: _mockRepository(organizations: [org]),
        },
        () => FcmUtils.backgroundHandler(payload),
      );

      verify(() => backgroundWorkUtils.wrapWork<Null>(DbLockKey.fcm, any()))
          .called(1);
    });

    test('Background message from same session id does nothing', () async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.hasPendingSync: false,
      });
      const sessionId = 9838;
      const org = Organization(id: LocalId(321), remoteId: RemoteId(3210));
      final payload = _payload(
        from: org.remoteId!,
        message: <String, dynamic>{
          'performSync': true,
          'session_id': sessionId,
        },
      );

      await withInjectedN(
        {
          ActiveSession: _mockActiveSession(
              token: const SessionToken('natoheunthaou_$sessionId')),
          AwesomeNotificationsUtils: mockAwesomeNotificationsUtils(),
          BitacoraAnalyticsLogger: MockBitacoraAnalyticsLogger(),
          BackgroundWorkUtils: mockBackgroundWorkUtils<Null>(),
          DbRepository: _mockRepository(organizations: [org]),
          RepositoryHook: mockRepositoryHook(),
        },
        () => FcmUtils.backgroundHandler(payload),
      );

      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getBool(SharedPreferencesKeys.hasPendingSync), false);
    });

    //   test('Background message -> Sync', () async {
    //     const org = Organization(id: LocalId(122), remoteId: RemoteId(321));
    //     SharedPreferences.setMockInitialValues({
    //       SharedPreferencesKeys.hasPendingSync: false,
    //       SharedPreferencesKeys.activeOrganizationId: org.id!.value
    //     });
    //     final now = DateTime.now();
    //     final clock = mockClock(now);
    //     final workmanagerUtils = mockWorkmanagerUtils();
    //     final payload = _payload(
    //       from: org.remoteId!,
    //       message: <String, dynamic>{
    //         'perform_sync': true,
    //       },
    //     );
    //
    //     await withInjectedN(
    //       {
    //         ActiveSession: _mockActiveSession(),
    //         WorkmanagerUtils: workmanagerUtils,
    //         Clock: clock,
    //         AwesomeNotificationsUtils: mockAwesomeNotificationsUtils(),
    //         BitacoraAnalyticsLogger: MockBitacoraAnalyticsLogger(),
    //         BackgroundWorkUtils: mockBackgroundWorkUtils<Null>(),
    //         DbRepository: _mockRepository(organizations: [org]),
    //         RepositoryHook: mockRepositoryHook(),
    //       },
    //       () => FcmUtils.backgroundHandler(payload),
    //     );
    //
    //     final prefs = await SharedPreferences.getInstance();
    //     expect(prefs.getBool(SharedPreferencesKeys.hasPendingSync), true);
    //     verify(
    //       () => workmanagerUtils.registerOneOffTask(
    //         WorkmanagerTask.backgroundSync,
    //         initialDelay: kBackgroundSyncDelay,
    //       ),
    //     ).called(1);
    //   });
    //
    //   test('Background message -> Delete entry', () async {
    //     const org = Organization(id: LocalId(122), remoteId: RemoteId(321));
    //     SharedPreferences.setMockInitialValues({
    //       SharedPreferencesKeys.hasDirtyEntryRepository: false,
    //     });
    //     final payload = _payload(
    //       from: org.remoteId!,
    //       message: <String, dynamic>{
    //         'entry': {
    //           'id': 123,
    //           'deleted': true,
    //         }
    //       },
    //     );
    //     const entry = Entry(id: LocalId(12));
    //     final entryRepository = mockEntryDbTable();
    //     final db = _mockRepository(
    //       organizations: [org],
    //       entryRepository: entryRepository,
    //     );
    //     whenQuery<EntryIdRepositoryQuery, Entry?>(db, (_) => entry);
    //
    //     await withInjectedN(
    //       {
    //         ActiveSession: _mockActiveSession(),
    //         DbRepository: db,
    //         AwesomeNotificationsUtils: mockAwesomeNotificationsUtils(),
    //         OpenStateNotificationMonitor: MockOpenStateNotificationMonitor(),
    //         BitacoraAnalyticsLogger: MockBitacoraAnalyticsLogger(),
    //         BackgroundWorkUtils: mockBackgroundWorkUtils<Null>(),
    //       },
    //       () => FcmUtils.backgroundHandler(payload),
    //     );
    //
    //     verify(() => entryRepository.delete(any(), entry.id!));
    //     final prefs = await SharedPreferences.getInstance();
    //     expect(
    //         prefs.getBool(SharedPreferencesKeys.hasDirtyEntryRepository), true);
    //   });
    //
    //   test('Background message -> Save entry', () async {
    //     const org = Organization(id: LocalId(122), remoteId: RemoteId(321));
    //     SharedPreferences.setMockInitialValues({
    //       SharedPreferencesKeys.hasDirtyEntryRepository: false,
    //     });
    //     final payload = _payload(
    //       from: org.remoteId!,
    //       message: <String, dynamic>{
    //         'entry': {
    //           'id': 123,
    //         }
    //       },
    //     );
    //     final entry = mockEntry(extension: mockWorklog(withRemoteId: true));
    //     final entryRepository = mockEntryDbTable();
    //     final db = _mockRepository(
    //       organizations: [org],
    //       entryRepository: entryRepository,
    //     );
    //     whenQuery<ProjectIdsRepositoryQuery, List<Project?>>(
    //         db, (_) => <Project?>[const Project()]);
    //     final entryApiTranslator = MockEntryApiTranslator();
    //     when(() => entryApiTranslator.fromMap(any())).thenReturn(entry);
    //     final apiTranslator = MockApiTranslator();
    //     when(() => apiTranslator.entry).thenReturn(entryApiTranslator);
    //
    //     await withInjectedN(
    //       {
    //         ActiveSession: _mockActiveSession(),
    //         DbRepository: db,
    //         ApiTranslator: apiTranslator,
    //         AwesomeNotificationsUtils: mockAwesomeNotificationsUtils(),
    //         OpenStateNotificationMonitor: MockOpenStateNotificationMonitor(),
    //         BitacoraAnalyticsLogger: MockBitacoraAnalyticsLogger(),
    //         BackgroundWorkUtils: mockBackgroundWorkUtils<Null>(),
    //       },
    //       () => FcmUtils.backgroundHandler(payload),
    //     );
    //
    //     verify(() => entryRepository.save(any(), entry));
    //     final prefs = await SharedPreferences.getInstance();
    //     expect(
    //         prefs.getBool(SharedPreferencesKeys.hasDirtyEntryRepository), true);
    //   });
    //
    //   testWidgets('Foreground message -> Sync', (tester) async {
    //     const org = Organization(id: LocalId(122), remoteId: RemoteId(321));
    //     SharedPreferences.setMockInitialValues({
    //       SharedPreferencesKeys.activeOrganizationId: org.id!.value,
    //     });
    //     final payload = _payload(
    //       from: org.remoteId!,
    //       message: <String, dynamic>{
    //         'perform_sync': true,
    //       },
    //     );
    //     final syncTrigger = MockSyncTrigger();
    //     when(() => syncTrigger
    //             .trigger(const SyncTriggerEvent(SyncTriggerSource.notification)))
    //         .thenReturn(null);
    //     final fcmUtils = FcmUtils();
    //
    //     await tester.pumpWidget(testAppForContextCallback(
    //       providers: [
    //         Provider<Repository>(
    //             create: (_) => _mockRepository(organizations: [org])),
    //         Provider<ApiTranslator>(create: (_) => MockApiTranslator()),
    //         ChangeNotifierProvider<ActiveSession>(create: (_) {
    //           final activeSession = _mockActiveSession();
    //           when(() => activeSession.hasLoaded).thenReturn(true);
    //           return activeSession;
    //         }),
    //         Provider<SyncTrigger>(create: (_) => syncTrigger),
    //         Provider<AnalyticsLogger>(create: (_) => MockAnalyticsLogger()),
    //         ChangeNotifierProvider<ActiveOrganization>(
    //             create: (_) => mockActiveOrganization(organization: org)),
    //       ],
    //       callback: (context) => fcmUtils.foregroundHandler(context, payload),
    //     ));
    //     await tester.tap(find.text('Run Test'));
    //
    //     await awaitUntilStopsThrowing(
    //         () => verify(() => syncTrigger.trigger(any())));
    //   });
    //
    //   // FIXME: test foreground delete, save, session check,
    //   // FIXME: test doesn't save based on missing projects
    //   // FIXME: test doesn't save progresslog on missing parent entry
    //   // FIXME: test process message after activeSession is done loading
  });
}

MockDbRepository _mockRepository(
    {required List<Organization> organizations,
    EntryDbTable? entryRepository}) {
  final db = MockDbRepository();
  final orgDbTable = mockOrganizationDbTable();
  final locationTrackingDbTable = mockLocationTrackingDbTable();
  when(() => db.entry).thenReturn(entryRepository ?? MockEntryDbTable());
  when(() => db.context()).thenReturn(MockDbContext());
  when(() => db.query(const FcmOrganizationTopicRepositoryQuery()))
      .thenAnswer((_) => Future.value(organizations));
  when(() => db.query(const OrganizationCacheRepositoryQuery()))
      .thenAnswer((_) => Future.value(organizations));
  when(() => db.organization).thenReturn(orgDbTable);
  when(() => db.locationTracking).thenReturn(locationTrackingDbTable);
  when(() => db.context(queryScope: any(named: 'queryScope')))
      .thenReturn(MockDbContext());
  return db;
}

ActiveSession _mockActiveSession({SessionToken? token}) {
  final activeSession = mockActiveSession(session: mockSession(token: token));
  when(() => activeSession.load()).thenAnswer((_) => Future.value());
  return activeSession;
}

RemoteMessage _payload(
    {required RemoteId from, required Map<String, dynamic> message}) {
  return RemoteMessage.fromMap(<String, dynamic>{
    'data': {'message': json.encode(message), 'topic': '${from.apiValue}'}
  });
}
