import 'dart:async';

import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry_repository.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/organization/organization_repository.dart';
import 'package:bitacora/util/fcm/fcm_organization_topic_repository_query.dart';
import 'package:bitacora/util/fcm/fcm_utils.dart';
import 'package:bitacora/util/fcm/fcm_widget.dart';
import 'package:bitacora/util/fcm/firebase_messaging.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:firebase_messaging/firebase_messaging.dart' as firebase;
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../domain/common/mocks.dart';
import '../../domain/entry/mocks.dart';
import '../../domain/organization/mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../../test_util.dart';
import 'mocks.dart';

void main() {
  BuildContext? context;

  Widget testFcmWidget({
    Repository? repository,
    SyncTrigger? syncTrigger,
  }) {
    return MultiProvider(
      providers: [
        Provider<Repository>(create: (_) => repository ?? _mockRepository()),
      ],
      child: MaterialApp(
        home: Localizations(
          locale: const Locale('es'),
          delegates: AppLocalizations.localizationsDelegates,
          child: Scaffold(
            body: FcmWidget(
              child: Builder(
                builder: (c) {
                  context = c;
                  return Container();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  group('$FcmWidget tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Register on BackgroundMessage', (tester) async {
      firebase.BackgroundMessageHandler? capturedHandler;
      final fcmUtils = _mockFcmUtils();
      final firebaseMessagingUtils = _mockFirebaseMessagingUtils(
        onBackgroundMessage: (handler) {
          capturedHandler = handler;
        },
      );
      final controller = StreamController<firebase.RemoteMessage>();
      when(() => firebaseMessagingUtils.onMessage)
          .thenReturn(() => controller.stream);

      await withInjected<FirebaseMessaging>(
        firebaseMessagingUtils,
        () => withInjected<FcmUtils>(
          fcmUtils,
          () => tester.pumpWidget(testFcmWidget()),
        ),
      );

      expect(capturedHandler, FcmUtils.backgroundHandler);
    });

    testWidgets('Register on onMessage', (tester) async {
      final controller = StreamController<firebase.RemoteMessage>();
      firebase.RemoteMessage? messageCaptured;
      final fcmUtils = _mockFcmUtils(
        foregroundHandler: (invocation) {
          messageCaptured = invocation.positionalArguments[1];
          return Future.value();
        },
      );
      final firebaseMessagingUtils =
          _mockFirebaseMessagingUtils(onOnMessage: () => controller.stream);

      const message = firebase.RemoteMessage(data: {'sos': 'help!'});
      await withInjected<FirebaseMessaging>(
        firebaseMessagingUtils,
        () => withInjected<FcmUtils>(
          fcmUtils,
          () async {
            await tester.pumpWidget(testFcmWidget());
            controller.sink.add(message);
          },
        ),
      );

      expect(controller.hasListener, true);
      await awaitUntil(() => messageCaptured == message);
    });

    testWidgets('Disposing widget unregister ForegroundMessageHandler',
        (tester) async {
      final controller = StreamController<firebase.RemoteMessage>();
      final fcmUtils = _mockFcmUtils();
      final firebaseMessagingUtils =
          _mockFirebaseMessagingUtils(onOnMessage: () => controller.stream);

      await withInjected<FirebaseMessaging>(
        firebaseMessagingUtils,
        () => withInjected<FcmUtils>(
          fcmUtils,
          () async {
            await tester.pumpWidget(testFcmWidget());
            Navigator.of(context!).pop();
          },
        ),
      );

      await tester.pumpAndSettle();
      expect(controller.hasListener, false);
    });
  });
}

Repository _mockRepository({
  OrganizationRepository? testOrgRepository,
  EntryRepository? testEntry,
}) {
  var orgRepository = testOrgRepository;
  if (orgRepository == null) {
    orgRepository = MockOrganizationRepository();
    when(() => orgRepository!.getMutations()).thenAnswer(
        (_) => StreamController<Mutation<Organization>>.broadcast().stream);
  }

  final entryRepository = testEntry ?? MockEntryRepository();

  final db = MockRepository();
  when(
    () => db.query(const FcmOrganizationTopicRepositoryQuery()),
  ).thenAnswer((_) => Future.value(<Organization>[]));
  when(() => db.organization).thenReturn(orgRepository);
  when(() => db.entry).thenReturn(entryRepository);

  return db;
}

FcmUtils _mockFcmUtils({
  Future<void> Function(Invocation)? foregroundHandler,
}) {
  final fcmUtils = MockFcmUtils();
  when(() => fcmUtils.foregroundHandler(any(), any()))
      .thenAnswer(foregroundHandler ?? (_) => Future.value());
  when(() => fcmUtils.syncSubscriptions(any()))
      .thenAnswer((_) => Future.value());

  return fcmUtils;
}

FirebaseMessaging _mockFirebaseMessagingUtils({
  void Function(firebase.BackgroundMessageHandler)? onBackgroundMessage,
  Stream<firebase.RemoteMessage> Function()? onOnMessage,
}) {
  final firebaseMessaging = MockFirebaseMessaging();
  when(() => firebaseMessaging.onBackgroundMessage).thenAnswer(
    (_) => onBackgroundMessage ?? (_) {},
  );
  when(() => firebaseMessaging.onMessage).thenAnswer((_) =>
      onOnMessage ??
      () => StreamController<firebase.RemoteMessage>.broadcast().stream);

  return firebaseMessaging;
}
