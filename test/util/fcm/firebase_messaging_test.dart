import 'package:bitacora/util/fcm/firebase_messaging.dart';
import 'package:firebase_messaging/firebase_messaging.dart' as firebase;
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('$FirebaseMessaging tests', () {
    test('Injects same', () {
      expect(FirebaseMessaging(), FirebaseMessaging());
    });

    test('Provides onMessage', () {
      final firebaseMessaging = FirebaseMessaging();

      expect(
        firebaseMessaging.onMessage(),
        firebase.FirebaseMessaging.onMessage,
      );
    });

    test('Provides onBackgroundMessage', () {
      final firebaseMessaging = FirebaseMessaging();

      expect(
        firebaseMessaging.onBackgroundMessage,
        firebase.FirebaseMessaging.onBackgroundMessage,
      );
    });
  });
}
