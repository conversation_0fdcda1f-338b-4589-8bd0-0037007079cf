import 'package:bitacora/util/suggestion_typeahead/suggestion_typeahead_utils.dart';
import 'package:test/test.dart';

void main() {
  group('SuggestionTypeaheadUtils test', () {
    test('Pattern resolvers with non-empty input', () {
      const input = 'test';
      final patterns = defaultSearchPatternResolvers
          .map((resolver) => resolver(input))
          .toList();

      expect(patterns, hasLength(2));
      expect(patterns[0], equals('test%')); // Start position pattern
      expect(patterns[1], equals('%test%')); // Any position pattern
    });

    test('Pattern resolvers with empty input', () {
      const input = '';
      final patterns = defaultSearchPatternResolvers
          .map((resolver) => resolver(input))
          .where((pattern) => pattern.isNotEmpty)
          .toList();

      expect(patterns, hasLength(1));
      expect(patterns[0], equals('%')); // Should return '%' to match all results
    });

    test('Pattern resolvers with whitespace-only input', () {
      const input = '   ';
      final patterns = defaultSearchPatternResolvers
          .map((resolver) => resolver(input))
          .where((pattern) => pattern.isNotEmpty)
          .toList();

      expect(patterns, hasLength(1));
      expect(patterns[0], equals('%')); // Should return '%' to match all results
    });

    test('Pattern resolvers with single character input', () {
      const input = 'a';
      final patterns = defaultSearchPatternResolvers
          .map((resolver) => resolver(input))
          .toList();

      expect(patterns, hasLength(2));
      expect(patterns[0], equals('a%')); // Start position pattern
      expect(patterns[1], equals('%a%')); // Any position pattern
    });
  });
}
