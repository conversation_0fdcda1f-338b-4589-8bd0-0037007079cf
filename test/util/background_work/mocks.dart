import 'package:bitacora/util/background_work/background_work_utils.dart';
import 'package:bitacora/util/background_work/bitacora_background_app.dart';
import 'package:mocktail/mocktail.dart';

class MockBackgroundWorkUtils extends Mock implements BackgroundWorkUtils {}

BackgroundWorkUtils mockBackgroundWorkUtils<T>() {
  final mock = MockBackgroundWorkUtils();
  when(() => mock.wrapWork<T>(any(), any())).thenAnswer(
    (invocation) => invocation.positionalArguments[1](
        BitacoraBackgroundApp(invocation.positionalArguments.first).context),
  );
  return mock;
}
