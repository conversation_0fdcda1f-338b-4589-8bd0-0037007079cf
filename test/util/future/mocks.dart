import 'package:bitacora/util/future/future.dart';
import 'package:mocktail/mocktail.dart';

class MockFutureInjector extends Mock implements FutureInjector {}

FutureInjector mockFutureInjector<T>() {
  final mock = MockFutureInjector();
  when(() => mock.delayed<T>(any(), any())).thenAnswer(
    (invocation) {
      invocation.positionalArguments[1]();
      return Future.value(null as T);
    },
  );
  when(() => mock.delayed<T>(any())).thenAnswer(
    (invocation) {
      return Future.value(null as T);
    },
  );
  return mock;
}
