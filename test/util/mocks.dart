import 'package:bitacora/util/clipboard.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:bitacora/util/navigator_utils.dart';
import 'package:bitacora/util/platform_utils.dart';
import 'package:bitacora/util/share.dart';
import 'package:bitacora/util/throttler.dart';
import 'package:bitacora/util/url_launcher.dart';
import 'package:bitacora/util/web_app_launcher.dart';
import 'package:bitacora/util/web_external_launcher.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:mocktail/mocktail.dart';

class MockClock extends Mock implements Clock {}

class MockPlatformUtils extends Mock implements PlatformUtils {}

class MockShare extends Mock implements Share {}

class MockLocationUtils extends Mock implements LocationUtils {}

class MockStopwatch extends Mock implements Stopwatch {}

class MockUrlLauncher extends Mock implements UrlLauncher {}

class MockWebAppLauncher extends Mock implements WebAppLauncher {}

class MockWebExternalLauncher extends Mock implements WebExternalLauncher {}

class MockClipboard extends Mock implements Clipboard {}

class MockNavigatorUtils extends Mock implements NavigatorUtils {}

class MockThrottler extends Mock implements Throttler {}

class MockNavigatorUtilContextSnapshot extends Mock
    implements NavigatorUtilsContextSnapshot {}

class MockPosition extends Mock implements Position {}

Clock mockClock(DateTime now) {
  final mock = MockClock();
  when(() => mock.now()).thenReturn(now);
  return mock;
}

Share mockShare() {
  final mock = MockShare();
  when(() => mock.share(any())).thenAnswer((_) => Future.value());
  return mock;
}

LocationUtils mockLocationUtils({
  Position? position,
  LocationPermission? permission,
  LocationPermission? requestedPermission,
}) {
  final mock = MockLocationUtils();

  when(() => mock.checkPermission())
      .thenAnswer((_) => Future.value(permission ?? LocationPermission.always));
  when(() => mock.requestPermission()).thenAnswer(
      (_) => Future.value(requestedPermission ?? LocationPermission.always));

  final mPosition = mockPosition();
  when(() => mock.determinePosition())
      .thenAnswer((_) => Future.value(position ?? mPosition));

  return mock;
}

Position mockPosition({LatLng? latLng}) {
  final mock = MockPosition();

  when(() => mock.longitude).thenReturn(latLng?.longitude ?? 0.0);
  when(() => mock.latitude).thenReturn(latLng?.latitude ?? 0.0);

  return mock;
}

Clipboard mockClipboard() {
  final mock = MockClipboard();
  when(() => mock.copyText(any())).thenAnswer((_) => Future.value());
  return mock;
}

NavigatorUtils mockNavigatorUtils() {
  final mock = MockNavigatorUtils();
  when(() => mock.popUntilRoot(any())).thenReturn(null);
  when(() => mock.popUntilParentRoute(any())).thenReturn(null);
  return mock;
}

Throttler mockThrottler() {
  final mock = MockThrottler();
  when(() => mock.add(any())).thenReturn(null);
  when(() => mock.isWaiting).thenReturn(false);
  return mock;
}
