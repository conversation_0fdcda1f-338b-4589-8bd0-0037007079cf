import 'package:bitacora/dev/db_tools/dev_db_speed_test.dart';
import 'package:bitacora/dev/dev_log_dumper.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

void main() async {
  runApp(
    MaterialApp(
      home: const _TestHome(),
      theme: buildLightThemeData(bitacoraGreen),
      themeMode: ThemeMode.light,
    ),
  );
}

class _TestHome extends StatefulWidget {
  const _TestHome();

  @override
  State<_TestHome> createState() => _TestHomeState();
}

class _TestHomeState extends State<_TestHome> {
  final LogDumper _dumper = LogDumper('Db Speed Test App');

  @override
  void initState() {
    super.initState();
    _dumper.clear();
    runDbSpeedTest(_dumper);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: ChangeNotifierProvider<LogDumper>.value(
          value: _dumper,
          child: const SingleChildScrollView(
            child: DevLogDumperWidget(),
          ),
        ),
      ),
    );
  }
}
