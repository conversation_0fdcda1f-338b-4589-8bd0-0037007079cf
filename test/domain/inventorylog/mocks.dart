import 'package:bitacora/domain/inventorylog/inventorylog_repository.dart';
import 'package:mocktail/mocktail.dart';

class MockInventorylogRepository extends Mock
    implements InventorylogRepository {}

InventorylogRepository mockInventorylogRepository({
  List<String> itemNames = const <String>[],
  List<String> reasons = const <String>[],
}) {
  final mock = MockInventorylogRepository();
  when(() => mock.itemNames(any())).thenAnswer((_) => Future.value(itemNames));
  when(() => mock.reasons(any())).thenAnswer((_) => Future.value(reasons));
  return mock;
}
