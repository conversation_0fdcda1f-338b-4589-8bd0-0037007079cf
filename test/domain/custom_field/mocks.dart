import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:mocktail/mocktail.dart';

class MockCustomField extends Mock implements CustomField {}

MockCustomField mockCustomField({
  CustomFieldName? name,
  CustomFieldType? type,
  CustomFieldDeletedAt? deletedAt,
  CustomFieldCreatedAt? createdAt,
  CustomFieldUpdatedAt? updatedAt,
  List<CustomFieldAllowedValue>? allowedValues,
  CustomField? parent,
  Organization? organization,
}) {
  final mock = MockCustomField();
  final mockedOrg = organization ?? mockOrganization();
  when(() => mock.name)
      .thenReturn(name ?? CustomFieldName('Mocked Custom Field'));
  when(() => mock.type).thenReturn(type ?? CustomFieldType.text);
  when(() => mock.deletedAt)
      .thenReturn(deletedAt ?? CustomFieldDeletedAt(null));
  when(() => mock.createdAt)
      .thenReturn(createdAt ?? CustomFieldCreatedAt(DateTime.now()));
  when(() => mock.updatedAt)
      .thenReturn(updatedAt ?? CustomFieldUpdatedAt(DateTime.now()));
  when(() => mock.allowedValues).thenReturn(allowedValues ?? []);
  when(() => mock.parent).thenReturn(parent);
  when(() => mock.organization).thenReturn(mockedOrg);
  return mock;
}
