import 'dart:ui';

import 'package:bitacora/domain/common/repository_cache.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/project/project_repository_cache.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/organization/organization_db_fields_builder.dart';
import 'package:bitacora/infrastructure/project/project_db_fields_builder.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../infrastructure/mocks.dart';

void main() {
  group('$ProjectRepositoryCache tests', () {
    test('Caches project', () {
      final cache = RepositoryCache();
      const project = Project(id: LocalId(2));
      final tableCache = cache.table<Project>()!;

      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedModel(cache, project.id!);
      expect(cached, project);
    });

    test('Caches with nested model', () {
      final cache = RepositoryCache();
      const org = Organization(id: LocalId(1), remoteId: RemoteId(3));
      cache.table<Organization>()!.save(cache, org.id!, org);
      const project = Project(id: LocalId(2), organization: org);
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedModel(cache, project.id!)!;

      expect(cached, project);
      expect(cached.organization, project.organization!);
    });

    test('Provides cache with nested updates', () {
      final cache = RepositoryCache();
      const org = Organization(id: LocalId(1), remoteId: RemoteId(3));
      cache.table<Organization>()!.save(cache, org.id!, org);
      const project = Project(id: LocalId(2), organization: org);
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);
      const color = OrganizationColor(Color(0xffffffff));
      cache
          .table<Organization>()!
          .save(cache, org.id!, org.copyWith(color: color));

      final cached = tableCache.cachedModel(cache, project.id!)!;

      expect(cached != project, true);
      expect(cached.organization!.color, color);
    });

    test('Merges fields in cache', () {
      final cache = RepositoryCache();
      const project1 = Project(id: LocalId(2), name: ProjectName('p'));
      const project2 = Project(id: LocalId(2), remoteId: RemoteId(3));
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project1.id!, project1);

      tableCache.save(cache, project2.id!, project2);

      final cached = tableCache.cachedModel(cache, project1.id!)!;
      expect(cached.id, project1.id!);
      expect(cached.name, project1.name!);
      expect(cached.remoteId, project2.remoteId!);
    });

    test('Merges model in cache', () {
      final cache = RepositoryCache();
      const project1 = Project(id: LocalId(2), name: ProjectName('p'));
      const project2 =
          Project(id: LocalId(2), organization: Organization(id: LocalId(1)));
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project1.id!, project1);

      tableCache.save(cache, project2.id!, project2);

      final cached = tableCache.cachedModel(cache, project1.id!)!;
      expect(cached.id, project1.id!);
      expect(cached.name, project1.name!);
      expect(cached.organization, project2.organization!);
    });

    test('Provides cached id with field checks', () {
      final cache = RepositoryCache();
      const project = Project(id: LocalId(2), name: ProjectName('p'));
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedIdWithFieldChecks(cache, project);

      expect(cached, project.id);
    });

    test('Fails to provide cached id - new field', () {
      final cache = RepositoryCache();
      const project = Project(id: LocalId(2));
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedIdWithFieldChecks(
        cache,
        project.copyWith(name: const ProjectName('new')),
      );

      expect(cached, null);
    });

    test('Fails to provide cached id - edited field', () {
      final cache = RepositoryCache();
      const project = Project(id: LocalId(2), name: ProjectName('p'));
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedIdWithFieldChecks(
        cache,
        project.copyWith(name: const ProjectName('other')),
      );

      expect(cached, null);
    });

    test('Fails to provide cached id - new nested model', () {
      final cache = RepositoryCache();
      const project = Project(id: LocalId(2), name: ProjectName('p'));
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedIdWithFieldChecks(
        cache,
        project.copyWith(organization: const Organization(id: LocalId(3))),
      );

      expect(cached, null);
    });

    test('Fails to provide cached id - edited nested model - new field', () {
      final cache = RepositoryCache();
      const organization = Organization(id: LocalId(1));
      const project = Project(id: LocalId(2), organization: organization);
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedIdWithFieldChecks(
        cache,
        project.copyWith(
            organization: organization.copyWith(
                color: const OrganizationColor(Color(0xffffffff)))),
      );

      expect(cached, null);
    });

    test('Fails to provide cached id - edited nested model - edited field', () {
      final cache = RepositoryCache();
      const organization = Organization(id: LocalId(1));
      const project = Project(id: LocalId(2), organization: organization);
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedIdWithFieldChecks(
        cache,
        project.copyWith(organization: const Organization(id: LocalId(2))),
      );

      expect(cached, null);
    });

    test('Fails to provide cached id - edited nested model - edited field', () {
      final cache = RepositoryCache();
      const organization = Organization(id: LocalId(1));
      const project = Project(id: LocalId(2), organization: organization);
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);

      final cached = tableCache.cachedIdWithFieldChecks(
        cache,
        project.copyWith(organization: const Organization(id: LocalId(2))),
      );

      expect(cached, null);
    });

    test('Provides cached model with field checks', () {
      final cache = RepositoryCache();
      const organization = Organization(id: LocalId(1));
      const project = Project(id: LocalId(2), organization: organization);
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);
      final context = DbContext(
        db: MockDbRepository(),
        fields: ProjectDbFieldsBuilder()
            .organization(OrganizationDbFieldsBuilder().build())
            .build(),
      );

      final cached =
          tableCache.cachedModelWithFieldChecks(cache, project.id!, context);

      expect(cached, project);
    });

    test('Fails to provides cached model - missing field', () {
      final cache = RepositoryCache();
      const project = Project(id: LocalId(2));
      final tableCache = cache.table<Project>()!;
      tableCache.save(cache, project.id!, project);
      final context = DbContext(
        db: MockDbRepository(),
        fields: ProjectDbFieldsBuilder().name().build(),
      );

      final cached =
          tableCache.cachedModelWithFieldChecks(cache, project.id!, context);

      expect(cached, null);
    });
  });

  test('Fails to provides cached model - missing nested field', () {
    final cache = RepositoryCache();
    const organization = Organization(id: LocalId(1));
    const project = Project(id: LocalId(2), organization: organization);
    final tableCache = cache.table<Project>()!;
    tableCache.save(cache, project.id!, project);
    final context = DbContext(
      db: MockDbRepository(),
      fields: ProjectDbFieldsBuilder()
          .organization(OrganizationDbFieldsBuilder().name().build())
          .build(),
    );

    final cached =
        tableCache.cachedModelWithFieldChecks(cache, project.id!, context);

    expect(cached, null);
  });
}
