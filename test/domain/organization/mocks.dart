import 'dart:async';

import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/organization/organization_repository.dart';
import 'package:mocktail/mocktail.dart';

class MockOrganizationRepository extends Mock
    implements OrganizationRepository {}

OrganizationRepository mockOrganizationRepository() {
  final mock = MockOrganizationRepository();
  when(() => mock.getMutations()).thenAnswer(
      (_) => StreamController<Mutation<Organization>>.broadcast().stream);
  return mock;
}
