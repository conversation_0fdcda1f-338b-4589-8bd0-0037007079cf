import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('$QueryScope tests', () {
    test('BuildQueryScope whit all params with null', () {
      expect(() => QueryScope(), throwsAssertionError);
    });

    test('Scope is by User', () {
      final user = mockUser();
      final dbScope = QueryScope(userId: user.id);

      expect(dbScope.byUser, true);
    });

    test('Scope is by Organization', () {
      final organization = mockOrganization();
      final user = mockUser();
      final dbScope = QueryScope(orgId: organization.id, userId: user.id);

      expect(dbScope.byOrg, true);
    });

    test('Scope is by Project', () {
      final organization = mockOrganization();
      final user = mockUser();
      final project = mockProject();
      final dbScope = QueryScope(
        orgId: organization.id,
        projectId: project.id,
        userId: user.id,
      );

      expect(dbScope.byProject, true);
    });

    test('SameQueryScopes', () {
      final organization = mockOrganization();
      final user = mockUser();
      final dbScope = QueryScope(orgId: organization.id, userId: user.id);
      final otherScope = QueryScope(orgId: organization.id, userId: user.id);

      expect(dbScope == otherScope, true);
    });

    test('DifferentQueryScopes', () {
      final organization = mockOrganization();
      final user = mockUser();
      final project = mockProject();
      final dbScope = QueryScope(orgId: organization.id, userId: user.id);
      final otherScope = QueryScope(projectId: project.id, userId: user.id);

      expect(dbScope == otherScope, false);
    });
  });
}
