allprojects {
    repositories {
        google()
        jcenter()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    afterEvaluate {
        if (it.hasProperty('android')) {

            if (it.android.namespace == null) {
                def manifest = new XmlSlurper().parse(file(it.android.sourceSets.main.manifest.srcFile))
                def packageName = <EMAIL>()
                println("Setting ${packageName} as android namespace")
                android.namespace = packageName
            }

            def javaVersion = JavaVersion.VERSION_17
            android {
                def androidApiVersion = 35
                compileSdkVersion androidApiVersion
                defaultConfig {
                    targetSdkVersion androidApiVersion
                }
                compileOptions {
                    sourceCompatibility javaVersion
                    targetCompatibility javaVersion
                }
                tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                    kotlinOptions {
                        jvmTarget = javaVersion.toString()
                    }
                }
                println("Setting java version to ${javaVersion.toString()} which is $javaVersion")
                println("Setting compileSdkVersion and targetSdkVersion to $androidApiVersion")
            }
        }
    }

}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
