-keep class androidx.lifecycle.DefaultLifecycleObserver

# To solve awesome notification shares preferences issue: https://stackoverflow.com/questions/78656128/awesome-notifications-platformexceptionshared-preferences-not-available
-keep class com.google.common.reflect.TypeToken
-keep class * extends com.google.common.reflect.TypeToken

# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn com.google.j2objc.annotations.ReflectionSupport
-dontwarn com.google.j2objc.annotations.RetainedWith