{"auth": {"bitacoraflutter6fb117b66fb117b6": {"service": "Cognito", "providerPlugin": "awscloudformation", "dependsOn": [], "customAuth": false, "frontendAuthConfig": {"loginMechanisms": ["PREFERRED_USERNAME"], "signupAttributes": ["EMAIL"], "passwordProtectionSettings": {"passwordPolicyMinLength": 8, "passwordPolicyCharacters": []}, "mfaConfiguration": "OFF", "mfaTypes": ["SMS"]}}}, "storage": {"bitacoraflutterfb96bccb": {"service": "S3", "serviceType": "imported", "providerPlugin": "awscloudformation", "dependsOn": []}}}