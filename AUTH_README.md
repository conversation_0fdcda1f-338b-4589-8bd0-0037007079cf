# Bitacora API Auth System

We use an ad-hoc auth system. Sorry.

## How does it work?

### (A) Getting the First Token 

The client logs in (or signs up) with email/password combo
to start a session and obtain an access token(A).

```
Login(Email/Password) -> Response(Token(A))

Session(A).tokens = [Token(A)]
Token(A).isCurrent
```

### (B) Using Tokens

#### On an API call, we place the token(A) in the auth header...

The client acknowledged the **current** token(A), so the
server created a token(B) which becomes the new **current** 
token. The used token(A) becomes the **previous** token.

```
Request(Token(A)) -> Response(Token(B))

Session(A).tokens = [Token(B), Token(A)]
Token(B).isCurrent
Token(A).isPrevious

// Token mutated A -> B
```

#### On an API call, if we request with the **previous** token(A)...

The client did **not** acknowledge the **current** token(B).
The server considers the previous response could have been 
dropped. It responds with the **current** token(B) again.

```
Request(Token(A)) -> Response(Token(B))

Session(A).tokens = [Token(B), Token(A)]
Token(B).isCurrent
Token(A).isPrevious

// No state change
```

This means that a token does not expire on use. It can be 
used indefinitely, unless...

### (C) Expiring Tokens

#### On a new API call, if we request with the **current** token(B)...

The client acknowledged the **current** token(B), so the server 
schedules expiration of the **previous** token(A) in 10 minutes,
and creates a new **current** token(C).

```
Request(Token(B)) -> Response(Token(C))

Session(A).tokens = [Token(C), Token(B), Token(A)]
Token(C).isCurrent
Token(B).isPrevious
Token(A).isExpiring(minutes:10)
```

#### On a new API call, if we request with an **expiring** token(A)...

The token(A) is still valid, so the server obliges. It considers
it could be an old request that took a long time to reach. But,
since the client had already acknowledged the **previous** 
token(B) it does **NOT** inform about the **current** token(C).

```
Request(Token(A)) -> Response(Token(?))

Session(A).tokens = [Token(C), Token(B), Token(A)]
Token(C).isCurrent
Token(B).isPrevious
Token(A).isExpiring(minutes:10)

// No state change, but no token in response.
```

10 minutes later...

#### On a new API call, if we request with an **expired** token(A)...

The token(A) is no longer valid, so the server responds 498. 
It considers there could be something shady going on, so the whole
session is invalidated.

```
Request(Token(A)) -> Response(498)

Session(A).isInvalid
```

## FAQ
### (A) Why is the session invalidated on use of expired tokens?

This is a security mechanism to prevent dual use of tokens. If a
token is maliciously copied from a device, it can't be indefinitely
used by both a real client and a malicious one, as the real client 
will rotate tokens on use (regardless of whether the malicious does
this as well). 

Once this situation is detected (via attempted use of expired tokens),
since the server has no way of knowing who is performing requests
with expired tokens, it kicks both of them out.

### (B) What about parallel requests?

A problem can arise from mishandling tokens when performing parallel
requests. Consider the following scenario: 

Two requests are done simultaneously with the same token.
While the slow request is processing, multiple fast requests happen.

```
Request1(Token(A)) <-- Slow request fires
Request2(Token(A))
Response2(Token(B))
Request3(Token(B))
Response3(Token(C))
Request4(Token(C))
Response4(Token(D))
Response1(Token(B)) <-- Slow request arrives
```

In this scenario, the tokens will be responded in this order:
`[B, C, D, B]`. If the client considers the last token(B) received
as the **current** one, it will be using an **expiring** token for
the following requests. From then on, the real **current** token will
not be responded by the server. The client will be able to perform
requests for a few more minutes, but it will eventually expire and
invalidate the session.

```
Request5(Token(B))
Response5(Token(?))
Request6(Token(B))
Response6(Token(?))
...
// 10 minutes later
...
Request10(Token(B))
Response10(498)
```

#### The solution: Keep track of spent tokens.

The client can keep a set of **spent** tokens and make sure not to 
replace the **current** token with a **spent** token.

```
-> SpentTokens = []
-> CurrentToken = Token(A)
Request1(Token(A))
Request2(Token(A))
Response1(Token(B))
-> SpentTokens = {Token(A)}
-> CurrentToken = Token(B)
Request3(Token(B))
Response3(Token(C))
-> SpentTokens = {Token(A), Token(B)}
-> CurrentToken = Token(C)
Response1(Token(B))
-> SpentTokens = {Token(A), Token(B)}
-> CurrentToken = Token(C)

// Token(A) did not become current because it was already spent.

Request4(Token(C))
Response4(Token(D))
-> SpentTokens = {Token(A), Token(B), Token(C)}
-> CurrentToken = Token(C)
```


### (C) What about sharing the token non-maliciously?

This is possible, but since a new token will be generated on API
calls, the System(B) using the shared token should **NOT** rotate the
token. (It should keep using the shared token exclusively.) If it
does this even once, the original System(A) will have an **expiring**
token and the session will eventually be invalidated. 

Alternatively, if the System(B) using the shared token wants (or 
can't avoid) to rotate the token it should finally respond it's 
**current** token when it is done so that the original System(A) can
use it instead. 

Parallel queries between the original System(A) and the System(B) 
using the shared token is not possible. A solution for this is 
needed (a **temporary** token).
